#!/bin/bash

# Firebase Functions Setup Script for MyTube Platform
# This script sets up and deploys optimized Firebase Functions

echo "🚀 Setting up Firebase Functions for MyTube Platform..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found. Installing..."
    npm install -g firebase-tools
fi

# Check if logged in to Firebase
echo "🔐 Checking Firebase authentication..."
firebase projects:list > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "🔑 Please login to Firebase..."
    firebase login
fi

# Navigate to functions directory
echo "📁 Setting up functions directory..."
cd functions

# Install dependencies
echo "📦 Installing function dependencies..."
npm install

# Build TypeScript
echo "🔨 Building TypeScript functions..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please check for TypeScript errors."
    exit 1
fi

# Deploy functions
echo "🚀 Deploying functions to Firebase..."
cd ..
firebase deploy --only functions

if [ $? -eq 0 ]; then
    echo "✅ Functions deployed successfully!"
    echo ""
    echo "📊 Deployed Functions:"
    echo "  - getUserDashboardData (Dashboard optimization)"
    echo "  - submitVideoBatch (Video submission optimization)"
    echo "  - processWithdrawalRequest (Withdrawal optimization)"
    echo "  - getUserNotifications (Notification optimization)"
    echo "  - getUserTransactions (Transaction optimization)"
    echo "  - dailyActiveDaysIncrement (Scheduled daily process)"
    echo ""
    echo "🎯 Next Steps:"
    echo "  1. Update client code to use function calls"
    echo "  2. Test critical user flows"
    echo "  3. Monitor function performance in Firebase console"
    echo "  4. Check Firestore read reduction"
    echo ""
    echo "📖 See FIREBASE_FUNCTIONS_OPTIMIZATION.md for detailed implementation guide"
else
    echo "❌ Deployment failed. Please check the error messages above."
    exit 1
fi
