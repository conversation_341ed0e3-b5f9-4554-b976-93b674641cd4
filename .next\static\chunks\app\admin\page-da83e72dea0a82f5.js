(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698,6779],{1469:(e,a,s)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),!function(e,a){for(var s in a)Object.defineProperty(e,s,{enumerable:!0,get:a[s]})}(a,{default:function(){return n},getImageProps:function(){return d}});let t=s(8229),l=s(8883),r=s(3063),i=t._(s(1193));function d(e){let{props:a}=(0,l.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(a))void 0===s&&delete a[e];return{props:a}}let n=r.Image},6766:(e,a,s)=>{"use strict";s.d(a,{default:()=>l.a});var t=s(1469),l=s.n(t)},6779:(e,a,s)=>{"use strict";s.d(a,{CF:()=>c,I0:()=>h,Pn:()=>d,TK:()=>u,getAllPendingWithdrawals:()=>m,getAllWithdrawals:()=>g,hG:()=>N,lo:()=>n,nQ:()=>x,updateWithdrawalStatus:()=>f,x5:()=>o});var t=s(5317),l=s(6104),r=s(3592);let i=new Map;async function d(){let e="dashboard-stats",a=function(e){let a=i.get(e);return a&&Date.now()-a.timestamp<3e5?a.data:null}(e);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let s=t.Dc.fromDate(a),d=await (0,t.getDocs)((0,t.collection)(l.db,r.COLLECTIONS.users)),n=d.size,o=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t._M)(r.FIELD_NAMES.joinedDate,">=",s)),c=(await (0,t.getDocs)(o)).size,x=0,h=0,m=0,g=0;d.forEach(e=>{var s;let t=e.data();x+=t[r.FIELD_NAMES.totalVideos]||0,h+=t[r.FIELD_NAMES.wallet]||0;let l=null==(s=t[r.FIELD_NAMES.lastVideoDate])?void 0:s.toDate();l&&l.toDateString()===a.toDateString()&&(m+=t[r.FIELD_NAMES.todayVideos]||0)});try{let e=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.transactions),(0,t._M)(r.FIELD_NAMES.type,"==","video_earning"),(0,t.AB)(1e3));(await (0,t.getDocs)(e)).forEach(e=>{var s;let t=e.data(),l=null==(s=t[r.FIELD_NAMES.date])?void 0:s.toDate();l&&l>=a&&(g+=t[r.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let u=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.withdrawals),(0,t._M)("status","==","pending")),N=(await (0,t.getDocs)(u)).size,f=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.withdrawals),(0,t._M)("date",">=",s)),w=(await (0,t.getDocs)(f)).size,y={totalUsers:n,totalVideos:x,totalEarnings:h,pendingWithdrawals:N,todayUsers:c,todayVideos:m,todayEarnings:g,todayWithdrawals:w};return i.set(e,{data:y,timestamp:Date.now()}),y}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let s=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,t.AB)(e));a&&(s=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,t.HM)(a),(0,t.AB)(e)));let i=await (0,t.getDocs)(s);return{users:i.docs.map(e=>{var a,s;return{id:e.id,...e.data(),joinedDate:null==(a=e.data()[r.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(s=e.data()[r.FIELD_NAMES.planExpiry])?void 0:s.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function o(e){try{if(!e||0===e.trim().length)return[];let a=e.toLowerCase().trim(),s=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,t.getDocs)(s)).docs.map(e=>{var a,s;return{id:e.id,...e.data(),joinedDate:null==(a=e.data()[r.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(s=e.data()[r.FIELD_NAMES.planExpiry])?void 0:s.toDate()}}).filter(e=>{let s=String(e[r.FIELD_NAMES.name]||"").toLowerCase(),t=String(e[r.FIELD_NAMES.email]||"").toLowerCase(),l=String(e[r.FIELD_NAMES.mobile]||"").toLowerCase(),i=String(e[r.FIELD_NAMES.referralCode]||"").toLowerCase();return s.includes(a)||t.includes(a)||l.includes(a)||i.includes(a)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users),(0,t.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,t.getDocs)(e)).docs.map(e=>{var a,s;return{id:e.id,...e.data(),joinedDate:null==(a=e.data()[r.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(s=e.data()[r.FIELD_NAMES.planExpiry])?void 0:s.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function x(){try{let e=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.users));return(await (0,t.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let s=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.transactions),(0,t.My)(r.FIELD_NAMES.date,"desc"),(0,t.AB)(e));a&&(s=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.transactions),(0,t.My)(r.FIELD_NAMES.date,"desc"),(0,t.HM)(a),(0,t.AB)(e)));let i=await (0,t.getDocs)(s);return{transactions:i.docs.map(e=>{var a;return{id:e.id,...e.data(),date:null==(a=e.data()[r.FIELD_NAMES.date])?void 0:a.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function m(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.withdrawals),(0,t._M)("status","==","pending"),(0,t.My)("date","desc")),a=(await (0,t.getDocs)(e)).docs.map(e=>{var a;return{id:e.id,...e.data(),date:null==(a=e.data().date)?void 0:a.toDate()}});return console.log("✅ Loaded ".concat(a.length," pending withdrawals")),a}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,t.P)((0,t.collection)(l.db,r.COLLECTIONS.withdrawals),(0,t.My)("date","desc")),a=(await (0,t.getDocs)(e)).docs.map(e=>{var a;return{id:e.id,...e.data(),date:null==(a=e.data().date)?void 0:a.toDate()}});return console.log("✅ Loaded ".concat(a.length," total withdrawals")),a}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function u(e,a){try{await (0,t.mZ)((0,t.H9)(l.db,r.COLLECTIONS.users,e),a),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function N(e){try{await (0,t.kd)((0,t.H9)(l.db,r.COLLECTIONS.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function f(e,a,d){try{let n=await (0,t.x7)((0,t.H9)(l.db,r.COLLECTIONS.withdrawals,e));if(!n.exists())throw Error("Withdrawal not found");let{userId:o,amount:c,status:x}=n.data(),h={status:a,updatedAt:t.Dc.now()};if(d&&(h.adminNotes=d),await (0,t.mZ)((0,t.H9)(l.db,r.COLLECTIONS.withdrawals,e),h),"approved"===a&&"approved"!==x){let{addTransaction:e}=await Promise.resolve().then(s.bind(s,3592));await e(o,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===a&&"rejected"!==x){let{updateWalletBalance:e,addTransaction:a}=await Promise.resolve().then(s.bind(s,3592));await e(o,c),await a(o,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},7220:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>x});var t=s(5155),l=s(2115),r=s(6874),i=s.n(r),d=s(6766),n=s(6681),o=s(6779),c=s(12);function x(){var e,a,s,r,x;let{user:h,loading:m,isAdmin:g}=(0,n.wC)(),[u,N]=(0,l.useState)(null),[f,w]=(0,l.useState)(!0),[y,j]=(0,l.useState)(!1);(0,l.useEffect)(()=>{g&&v()},[g]);let v=async()=>{try{w(!0);let e=await (0,o.Pn)();N(e)}catch(e){console.error("Error loading dashboard stats:",e)}finally{w(!1)}};return m||f?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,t.jsxs)("aside",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ".concat(y?"translate-x-0":"-translate-x-full"," transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-center h-16 bg-gray-900",children:[(0,t.jsx)(d.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:32,height:32,className:"mr-2"}),(0,t.jsx)("span",{className:"text-white text-xl font-bold",children:"MyTube Admin"})]}),(0,t.jsx)("nav",{className:"mt-8",children:(0,t.jsxs)("div",{className:"px-4 space-y-2",children:[(0,t.jsxs)(i(),{href:"/admin",className:"flex items-center px-4 py-2 text-white bg-gray-700 rounded-lg",children:[(0,t.jsx)("i",{className:"fas fa-tachometer-alt mr-3"}),"Dashboard"]}),(0,t.jsxs)(i(),{href:"/admin/users",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-users mr-3"}),"Users"]}),(0,t.jsxs)(i(),{href:"/admin/simple-upload",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-file-csv mr-3"}),"Simple Upload"]}),(0,t.jsxs)(i(),{href:"/admin/transactions",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-exchange-alt mr-3"}),"Transactions"]}),(0,t.jsxs)(i(),{href:"/admin/withdrawals",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-money-bill-wave mr-3"}),"Withdrawals"]}),(0,t.jsxs)(i(),{href:"/admin/notifications",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-bell mr-3"}),"Notifications"]}),(0,t.jsxs)(i(),{href:"/admin/leaves",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-calendar-times mr-3"}),"Leave Management"]}),(0,t.jsxs)(i(),{href:"/admin/settings",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-cog mr-3"}),"Settings"]}),(0,t.jsxs)(i(),{href:"/admin/fix-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-tools mr-3"}),"Fix Active Days"]}),(0,t.jsxs)(i(),{href:"/admin/daily-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Daily Active Days"]})]})}),(0,t.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,t.jsxs)("button",{onClick:()=>{(0,c._f)(null==h?void 0:h.uid,"/admin/login")},className:"w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,t.jsx)("i",{className:"fas fa-sign-out-alt mr-3"}),"Logout"]})})]}),(0,t.jsxs)("div",{className:"lg:ml-64",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,t.jsx)("button",{onClick:()=>j(!y),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,t.jsx)("i",{className:"fas fa-bars text-xl"})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("span",{className:"text-gray-700",children:"Welcome, Admin"}),(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,t.jsx)("i",{className:"fas fa-user-shield text-gray-600"})})]})]})}),(0,t.jsxs)("main",{className:"p-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-users text-blue-600 text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==u||null==(e=u.totalUsers)?void 0:e.toLocaleString())||"0"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-video text-green-600 text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Videos"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==u||null==(a=u.totalVideos)?void 0:a.toLocaleString())||"0"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-rupee-sign text-yellow-600 text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Earnings"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₹",(null==u||null==(s=u.totalEarnings)?void 0:s.toLocaleString())||"0"]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-clock text-red-600 text-xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Withdrawals"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==u?void 0:u.pendingWithdrawals)||"0"})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow mb-8",children:[(0,t.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Today's Activity"})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:(null==u?void 0:u.todayUsers)||"0"}),(0,t.jsx)("p",{className:"text-gray-600",children:"New Users"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-3xl font-bold text-green-600",children:(null==u||null==(r=u.todayVideos)?void 0:r.toLocaleString())||"0"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Videos Watched"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-3xl font-bold text-yellow-600",children:["₹",(null==u||null==(x=u.todayEarnings)?void 0:x.toLocaleString())||"0"]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Earnings Paid"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-3xl font-bold text-red-600",children:(null==u?void 0:u.todayWithdrawals)||"0"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Withdrawals"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(i(),{href:"/admin/users",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-users text-blue-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manage Users"}),(0,t.jsx)("p",{className:"text-gray-600",children:"View and manage user accounts"})]})]})}),(0,t.jsx)(i(),{href:"/admin/withdrawals",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-money-bill-wave text-green-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Process Withdrawals"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Review and approve withdrawals"})]})]})}),(0,t.jsx)(i(),{href:"/admin/notifications",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-bell text-yellow-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Send Notifications"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Notify users about updates"})]})]})}),(0,t.jsx)(i(),{href:"/admin/settings",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-cog text-purple-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Settings"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Configure platform settings"})]})]})}),(0,t.jsx)(i(),{href:"/admin/fix-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-tools text-red-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fix Active Days"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Fix daily counts and active days"})]})]})}),(0,t.jsx)(i(),{href:"/admin/simple-upload",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-file-csv text-green-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Simple Upload"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Update videos, wallet & active days via CSV"})]})]})}),(0,t.jsx)(i(),{href:"/admin/daily-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"p-3 bg-indigo-100 rounded-lg",children:(0,t.jsx)("i",{className:"fas fa-calendar-plus text-indigo-600 text-2xl"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Daily Active Days"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage daily active days increment"})]})]})})]})]})]}),y&&(0,t.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>j(!1)})]})}},8856:(e,a,s)=>{Promise.resolve().then(s.bind(s,7220))}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,8818,6874,3063,3592,6681,8441,1684,7358],()=>a(8856)),_N_E=e.O()}]);