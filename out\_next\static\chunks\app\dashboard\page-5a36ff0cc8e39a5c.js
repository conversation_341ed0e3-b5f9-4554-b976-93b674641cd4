(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{1469:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return c},getImageProps:function(){return n}});let s=a(8229),l=a(8883),r=a(3063),i=s._(a(1193));function n(e){let{props:t}=(0,l.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let c=r.Image},2751:(e,t,a)=>{Promise.resolve().then(a.bind(a,5907))},5907:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var s=a(5155),l=a(2115),r=a(6874),i=a.n(r),n=a(6766),c=a(6681),o=a(7460),d=a(3592),x=a(12);function m(e){let{userId:t,isOpen:a,onClose:r}=e,[i,n]=(0,l.useState)([]),[c,o]=(0,l.useState)(!0);(0,l.useEffect)(()=>{a&&t&&x()},[a,t]);let x=async()=>{try{o(!0);let e=await (0,d.Ss)(t,20);n(e)}catch(e){console.error("Error loading notifications:",e)}finally{o(!1)}},m=e=>{e.id&&!(0,d.mv)(e.id,t)&&((0,d.bA)(e.id,t),n([...i]))},h=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},u=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return"".concat(e," minute").concat(e>1?"s":""," ago")}if(t<86400){let e=Math.floor(t/3600);return"".concat(e," hour").concat(e>1?"s":""," ago")}{let e=Math.floor(t/86400);return"".concat(e," day").concat(e>1?"s":""," ago")}};return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-16 z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-gray-900",children:[(0,s.jsx)("i",{className:"fas fa-bell mr-2"}),"Notifications"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:x,disabled:c,className:"text-gray-500 hover:text-gray-700 transition-colors p-1",title:"Refresh notifications",children:(0,s.jsx)("i",{className:"fas fa-sync-alt ".concat(c?"animate-spin":"")})}),(0,s.jsx)("button",{onClick:r,className:"text-gray-500 hover:text-gray-700 transition-colors",children:(0,s.jsx)("i",{className:"fas fa-times text-xl"})})]})]}),(0,s.jsx)("div",{className:"overflow-y-auto max-h-[60vh]",children:c?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsx)("div",{className:"spinner w-8 h-8"})}):0===i.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No notifications yet"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"You'll see important updates here"})]}):(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:i.map(e=>{let a=!!e.id&&(0,d.mv)(e.id,t);return(0,s.jsx)("div",{onClick:()=>m(e),className:"p-4 cursor-pointer hover:bg-gray-50 transition-colors ".concat(a?"":"bg-blue-50 border-l-4 border-l-blue-500"),children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("i",{className:h(e.type)})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h4",{className:"text-sm font-medium ".concat(a?"text-gray-700":"text-gray-900"),children:e.title}),!a&&(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,s.jsx)("p",{className:"text-sm mt-1 ".concat(a?"text-gray-600":"text-gray-800"),children:e.message}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:u(e.createdAt)})]})]})},e.id)})})}),i.length>0&&(0,s.jsx)("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:(0,s.jsx)("button",{onClick:()=>{i.forEach(e=>{e.id&&!(0,d.mv)(e.id,t)&&(0,d.bA)(e.id,t)}),n([...i])},className:"w-full text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Mark all as read"})})]})}):null}function h(e){let{userId:t,onClick:a}=e,[r,i]=(0,l.useState)([]),[n,c]=(0,l.useState)(0),[o,x]=(0,l.useState)(!1);(0,l.useEffect)(()=>{if(t){m();let e=setInterval(m,15e3);return()=>clearInterval(e)}},[t]);let m=async()=>{try{x(!0);let e=await (0,d.Ss)(t,20);i(e);let a=(0,d.ul)(e,t);if(a>n&&n>0){let e=document.querySelector(".notification-bell");e&&(e.classList.add("animate-bounce"),setTimeout(()=>{e.classList.remove("animate-bounce")},1e3))}c(a),console.log("Loaded ".concat(e.length," notifications, ").concat(a," unread"))}catch(e){console.error("Error loading notifications for bell:",e)}finally{x(!1)}};return(0,s.jsxs)("button",{onClick:a,className:"relative p-2 text-white hover:text-yellow-300 transition-colors",title:"".concat(n," unread notifications"),children:[(0,s.jsx)("i",{className:"fas fa-bell text-xl notification-bell ".concat(o?"animate-pulse":"")}),n>0&&(0,s.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse",children:n>9?"9+":n}),o&&(0,s.jsx)("span",{className:"absolute -bottom-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-3 w-3 flex items-center justify-center",children:(0,s.jsx)("i",{className:"fas fa-sync-alt text-xs animate-spin"})})]})}var u=a(8647),f=a(4752),p=a.n(f);function g(e){let{userId:t,currentMonth:r,usedLeaves:i,maxLeaves:n,onLeaveCountChange:c}=e,[o,d]=(0,l.useState)([]),[x,m]=(0,l.useState)(!1),[h,u]=(0,l.useState)(!1),[f,g]=(0,l.useState)(""),[b,v]=(0,l.useState)({date:"",reason:""});(0,l.useEffect)(()=>{w(),j()},[t]);let j=async()=>{try{let{getUserData:e,getPlanValidityDays:s,calculateUserActiveDays:l}=await Promise.resolve().then(a.bind(a,3592)),r=await e(t);if(r){let e,a=new Date;if("Trial"===r.plan){let t=r.joinedDate||new Date;e=new Date(t.getTime()+1728e5)}else if(r.planExpiry)e=r.planExpiry;else{let i=s(r.plan),n=await l(t),c=Math.max(0,i-n);e=new Date(a.getTime()+24*c*36e5)}let i=new Date(a.getTime()+2592e6),n=e<i?e:i;g(n.toISOString().split("T")[0])}}catch(t){console.error("Error calculating max date:",t);let e=new Date;e.setDate(e.getDate()+30),g(e.toISOString().split("T")[0])}},w=async()=>{try{let{getUserLeaves:e}=await a.e(9567).then(a.bind(a,9567)),s=await e(t);d(s)}catch(e){console.error("Error loading user leaves:",e),d([])}},y=async()=>{try{if(!b.date||!b.reason.trim())return void p().fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let e=new Date(b.date),s=new Date;s.setHours(0,0,0,0);let l=new Date(s);if(l.setDate(l.getDate()+1),e<=s)return void p().fire({icon:"error",title:"Invalid Date",text:"Cannot apply leave for today or past dates. Please select a future date."});try{let{isUserPlanExpired:l}=await Promise.resolve().then(a.bind(a,3592));if((await l(t)).expired)return void p().fire({icon:"error",title:"Plan Expired",text:"Your plan has expired. Cannot apply for leave."});let{getUserData:r,getPlanValidityDays:i,calculateUserActiveDays:n}=await Promise.resolve().then(a.bind(a,3592)),c=await r(t);if(c){let a;if("Trial"===c.plan){let e=c.joinedDate||new Date;a=new Date(e.getTime()+1728e5)}else if(c.planExpiry)a=c.planExpiry;else{let e=i(c.plan),l=await n(t),r=Math.max(0,e-l);a=new Date(s.getTime()+24*r*36e5)}if(e>a)return void p().fire({icon:"error",title:"Date Outside Plan Period",text:"Cannot apply leave beyond your plan expiry date (".concat(a.toLocaleDateString(),").")})}}catch(e){console.error("Error checking plan expiry:",e)}if(i>=n)return void p().fire({icon:"error",title:"Leave Limit Exceeded",text:"You have already used all ".concat(n," leaves for this month.")});if(o.find(t=>t.date.toDateString()===e.toDateString()))return void p().fire({icon:"error",title:"Duplicate Application",text:"You have already applied for leave on this date."});u(!0);let{applyUserLeave:r}=await a.e(9567).then(a.bind(a,9567)),d=await r({userId:t,date:e,reason:b.reason.trim()});await w(),c&&c(),d.autoApproved?p().fire({icon:"success",title:"✅ Leave Auto-Approved!",html:'\n            <div class="text-left">\n              <p><strong>Your leave has been automatically approved!</strong></p>\n              <br>\n              <p><strong>Date:</strong> '.concat(e.toLocaleDateString(),"</p>\n              <p><strong>Reason:</strong> ").concat(b.reason.trim(),'</p>\n              <br>\n              <p class="text-green-600"><strong>Leave Quota:</strong> ').concat(d.usedLeaves,"/").concat(d.maxLeaves,' used this month</p>\n              <p class="text-blue-600"><strong>Status:</strong> Approved automatically</p>\n            </div>\n          '),timer:6e3,showConfirmButton:!0,confirmButtonText:"Great!"}):p().fire({icon:"warning",title:"⏳ Leave Pending Approval",html:'\n            <div class="text-left">\n              <p><strong>Your leave application has been submitted.</strong></p>\n              <br>\n              <p><strong>Date:</strong> '.concat(e.toLocaleDateString(),"</p>\n              <p><strong>Reason:</strong> ").concat(b.reason.trim(),'</p>\n              <br>\n              <p class="text-orange-600"><strong>Status:</strong> Pending admin approval (quota exceeded)</p>\n              <p class="text-gray-600"><strong>Leave Quota:</strong> ').concat(d.maxLeaves,"/").concat(d.maxLeaves," used this month</p>\n            </div>\n          "),timer:6e3,showConfirmButton:!0,confirmButtonText:"Understood"}),v({date:"",reason:""}),m(!1)}catch(e){console.error("Error applying leave:",e),p().fire({icon:"error",title:"Application Failed",text:"Failed to apply for leave. Please try again."})}finally{u(!1)}},N=async e=>{try{let t=o.find(t=>t.id===e);if(!t||"pending"!==t.status)return void p().fire({icon:"error",title:"Cannot Cancel",text:"Only pending leave applications can be cancelled."});if((await p().fire({title:"Cancel Leave Application",text:"Are you sure you want to cancel this leave application?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Cancel",cancelButtonText:"Keep Application"})).isConfirmed){let{cancelUserLeave:t}=await a.e(9567).then(a.bind(a,9567));await t(e),await w(),c&&c(),p().fire({icon:"success",title:"Application Cancelled",text:"Your leave application has been cancelled.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error cancelling leave:",e),p().fire({icon:"error",title:"Cancellation Failed",text:"Failed to cancel leave application. Please try again."})}},S=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";case"pending":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},C=(e,t)=>{switch(e){case"approved":return"system"===t?"fas fa-magic text-green-500":"fas fa-check-circle text-green-500";case"rejected":return"fas fa-times-circle text-red-500";case"pending":return"fas fa-clock text-yellow-500";default:return"fas fa-question-circle text-gray-500"}},k=n-i;return(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-calendar-times mr-2"}),"Leave Management"]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"text-sm text-white/80",children:[r," Leaves"]}),(0,s.jsxs)("div",{className:"text-lg font-bold text-white",children:[k,"/",n," Available"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"bg-green-500/20 p-3 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-green-400",children:n}),(0,s.jsx)("div",{className:"text-xs text-white/80",children:"Monthly Quota"})]}),(0,s.jsxs)("div",{className:"bg-yellow-500/20 p-3 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-yellow-400",children:i}),(0,s.jsx)("div",{className:"text-xs text-white/80",children:"Used"})]}),(0,s.jsxs)("div",{className:"bg-blue-500/20 p-3 rounded-lg text-center",children:[(0,s.jsx)("div",{className:"text-xl font-bold text-blue-400",children:k}),(0,s.jsx)("div",{className:"text-xs text-white/80",children:"Remaining"})]})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("button",{onClick:()=>m(!0),disabled:k<=0,className:"w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),k>0?"Apply for Leave":"No Leaves Available"]})}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white/80",children:"Recent Applications"}),0===o.length?(0,s.jsxs)("div",{className:"text-center py-4 text-white/60",children:[(0,s.jsx)("i",{className:"fas fa-calendar-check text-2xl mb-2"}),(0,s.jsx)("p",{className:"text-sm",children:"No leave applications yet"})]}):(0,s.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:o.slice(-5).reverse().map(e=>(0,s.jsx)("div",{className:"bg-white/10 p-3 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-white font-medium",children:e.date.toLocaleDateString()}),(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ".concat(S(e.status)),children:[(0,s.jsx)("i",{className:"".concat(C(e.status,e.reviewedBy)," mr-1")}),e.status.charAt(0).toUpperCase()+e.status.slice(1),"system"===e.reviewedBy&&"approved"===e.status&&(0,s.jsx)("span",{className:"ml-1",title:"Auto-approved",children:"⚡"})]})]}),(0,s.jsxs)("div",{className:"text-sm text-white/70 mt-1",children:[e.reason,"system"===e.reviewedBy&&"approved"===e.status&&(0,s.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:[(0,s.jsx)("i",{className:"fas fa-magic mr-1"}),"Auto-approved (within quota)"]}),e.reviewNotes&&"system"!==e.reviewedBy&&(0,s.jsxs)("div",{className:"text-xs text-blue-400 mt-1",children:[(0,s.jsx)("i",{className:"fas fa-comment mr-1"}),e.reviewNotes]})]})]}),"pending"===e.status&&(0,s.jsx)("button",{onClick:()=>N(e.id),className:"text-red-400 hover:text-red-300 text-sm",children:(0,s.jsx)("i",{className:"fas fa-times"})})]})},e.id))})]}),x&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",style:{zIndex:99999},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Apply for Leave"}),(0,s.jsx)("button",{onClick:()=>m(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times text-xl"})})]}),f&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center text-blue-800",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),(0,s.jsxs)("span",{className:"text-sm",children:["Leave applications are allowed until ",new Date(f).toLocaleDateString()]})]})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,s.jsx)("input",{type:"date",value:b.date,onChange:e=>v(t=>({...t,date:e.target.value})),min:(()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]})(),max:f,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Leave can only be applied for future dates within your plan period"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,s.jsx)("textarea",{value:b.reason,onChange:e=>v(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]}),(0,s.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,s.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"You have ",k," leave(s) remaining for ",r,"."]}),k>0&&(0,s.jsxs)("div",{className:"text-sm text-green-700 mt-2",children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,s.jsx)("strong",{children:"Auto-Approval:"})," Your leave will be automatically approved since you have available quota."]}),k<=0&&(0,s.jsxs)("div",{className:"text-sm text-orange-700 mt-2",children:[(0,s.jsx)("i",{className:"fas fa-clock mr-2"}),(0,s.jsx)("strong",{children:"Manual Review:"})," Leave will require admin approval as quota is exceeded."]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,s.jsx)("button",{onClick:()=>m(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,s.jsx)("button",{onClick:y,disabled:h,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Applying..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Apply Leave"]})})]})]})})]})}var b=a(8926),v=a(8278);function j(e){let{showClearButton:t=!1}=e,[a,r]=(0,l.useState)(null);(0,l.useEffect)(()=>{(async()=>{try{await (0,v.checkVersionAndClearCache)(),r((0,v.hQ)())}catch(e){console.error("Error checking version:",e)}})()},[]);let i=async()=>{try{if(!confirm("This will clear all cached data and reload the page to get the latest version. Continue?"))return;let e=new Promise((e,t)=>setTimeout(()=>t(Error("Timeout")),1e4));try{await Promise.race([(0,v.am)(),e])}catch(e){console.error("Cache clear failed, trying simple clear:",e),(0,v.Gw)(),alert("Cache cleared! The page will now reload."),window.location.reload()}}catch(e){console.error("Error during manual cache clear:",e),window.location.reload()}};return t?(0,s.jsxs)("div",{className:"version-checker",children:[t&&(0,s.jsxs)("button",{onClick:i,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200 flex items-center gap-2",title:"Clear cache and data to get the latest version",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt"}),"Clear Cache & Data"]}),a&&(0,s.jsxs)("div",{className:"text-xs text-gray-400 mt-1",children:["v",a.currentVersion]})]}):null}function w(){let{user:e,loading:t}=(0,c.Nu)(),{hasBlockingNotifications:r,isChecking:f,markAllAsRead:p}=(0,o.J)((null==e?void 0:e.uid)||null),[v,w]=(0,l.useState)(null),[y,N]=(0,l.useState)(null),[S,C]=(0,l.useState)(null),[k,D]=(0,l.useState)(!0),[E,A]=(0,l.useState)(!1),[T,I]=(0,l.useState)(0),[L,P]=(0,l.useState)(0);(0,l.useEffect)(()=>{(0,x.G9)(),(async()=>{try{let{checkVersionAndClearCache:e}=await Promise.resolve().then(a.bind(a,8278));await e()}catch(e){console.error("Silent version check failed:",e)}})(),e&&M()},[e]);let M=async()=>{try{D(!0);let[t,s,l]=await Promise.all([(0,d.getUserData)(e.uid),(0,d.getWalletData)(e.uid),(0,d.getVideoCountData)(e.uid),B()]);if(w(t),N(s),C(l),t)try{let{updateUserActiveDays:t,isUserPlanExpired:s}=await Promise.resolve().then(a.bind(a,3592));await t(e.uid);let l=await (0,d.getUserData)(e.uid);w(l);let{getLiveActiveDays:r}=await Promise.resolve().then(a.bind(a,3592)),i=await r(e.uid);P(i)}catch(e){console.error("Error updating active days:",e)}}catch(e){console.error("Error loading dashboard data:",e)}finally{D(!1)}},B=async()=>{try{let{getUserMonthlyLeaveCount:t}=await a.e(9567).then(a.bind(a,9567)),s=new Date,l=s.getFullYear(),r=s.getMonth()+1,i=await t(e.uid,l,r);return I(i),console.log("User ".concat(e.uid," has used ").concat(i," leaves this month")),i}catch(e){return console.error("Error loading user leave count:",e),I(0),0}};return t||k||f?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:t?"Loading...":f?"Checking notifications...":"Loading dashboard..."})]})}):r&&e?(0,s.jsx)(u.A,{userId:e.uid,onAllRead:p}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:40,height:40,className:"mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"MyTube Dashboard"}),(0,s.jsxs)("p",{className:"text-white/80",children:["Welcome back, ",(null==v?void 0:v.name)||"User"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(j,{showClearButton:!0}),e&&(0,s.jsx)(h,{userId:e.uid,onClick:()=>A(!0)}),(0,s.jsxs)("button",{onClick:()=>{(0,x._f)(null==e?void 0:e.uid,"/login")},className:"glass-button px-4 py-2 text-white hover:bg-red-500/20 transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-sign-out-alt mr-2"}),"Logout"]})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6",children:[(0,s.jsxs)(i(),{href:"/work",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-play-circle text-3xl text-youtube-red mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Watch Videos"})]}),(0,s.jsxs)(i(),{href:"/wallet",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-wallet text-3xl text-green-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Wallet"})]}),(0,s.jsxs)(i(),{href:"/transactions",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-history text-3xl text-orange-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Transactions"})]}),(0,s.jsxs)(i(),{href:"/refer",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-users text-3xl text-blue-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Refer & Earn"})]}),(0,s.jsxs)(i(),{href:"/profile",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-user text-3xl text-purple-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Profile"})]}),(0,s.jsxs)(i(),{href:"/plans",className:"glass-card p-4 text-center hover:scale-105 transition-transform",children:[(0,s.jsx)("i",{className:"fas fa-crown text-3xl text-yellow-400 mb-2"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Plans"})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-wallet mr-2"}),"Wallet Overview"]}),(0,s.jsxs)("div",{className:"bg-green-500/20 p-6 rounded-lg text-center",children:[(0,s.jsx)("h3",{className:"text-green-400 font-semibold mb-2",children:"My Wallet"}),(0,s.jsxs)("p",{className:"text-4xl font-bold text-white mb-2",children:["₹",((null==y?void 0:y.wallet)||0).toFixed(2)]}),(0,s.jsx)("p",{className:"text-white/60",children:"Total available balance"}),(null==v?void 0:v.plan)==="Trial"&&(0,s.jsxs)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,s.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,s.jsx)("span",{className:"text-red-400 font-medium text-sm",children:"Withdrawal Restricted"})]}),(0,s.jsx)("p",{className:"text-white/80 text-xs mb-3",children:"Trial users cannot withdraw funds. Upgrade to enable withdrawals."}),(0,s.jsxs)(i(),{href:"/plans",className:"btn-secondary text-xs px-3 py-1",children:[(0,s.jsx)("i",{className:"fas fa-arrow-up mr-1"}),"Upgrade Plan"]})]}),(0,s.jsxs)(i(),{href:"/wallet",className:"btn-primary mt-4 inline-block",children:[(0,s.jsx)("i",{className:"fas fa-eye mr-2"}),"View Details"]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-video mr-2"}),"Today's Progress"]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-youtube-red",children:(null==S?void 0:S.todayVideos)||0}),(0,s.jsx)("p",{className:"text-white/80",children:"Videos Watched"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-yellow-400",children:(null==S?void 0:S.remainingVideos)||0}),(0,s.jsx)("p",{className:"text-white/80",children:"Remaining"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-green-400",children:(null==S?void 0:S.totalVideos)||0}),(0,s.jsx)("p",{className:"text-white/80",children:"Total Videos"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-3xl font-bold text-blue-400",children:L}),(0,s.jsx)("p",{className:"text-white/80",children:"Active Days"})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("div",{className:"bg-white/20 rounded-full h-3",children:(0,s.jsx)("div",{className:"bg-youtube-red h-3 rounded-full transition-all duration-300",style:{width:"".concat(S?S.todayVideos/50*100:0,"%")}})}),(0,s.jsxs)("p",{className:"text-white/80 text-sm mt-2 text-center",children:[(null==S?void 0:S.todayVideos)||0," / 50 videos completed today"]})]})]}),e&&(0,s.jsx)(g,{userId:e.uid,currentMonth:new Date().toLocaleDateString("en-US",{month:"long",year:"numeric"}),usedLeaves:T,maxLeaves:4,onLeaveCountChange:B}),(0,s.jsx)(b.A,{variant:"dashboard",className:"mb-6"}),e&&(0,s.jsx)(m,{userId:e.uid,isOpen:E,onClose:()=>A(!1)})]})}},6766:(e,t,a)=>{"use strict";a.d(t,{default:()=>l.a});var s=a(1469),l=a.n(s)},7460:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var s=a(2115),l=a(3592);function r(e){let[t,a]=(0,s.useState)(!1),[r,i]=(0,s.useState)(!0);(0,s.useEffect)(()=>{e?n():i(!1)},[e]);let n=async()=>{try{i(!0);let t=await (0,l.iA)(e);a(t)}catch(e){console.error("Error checking for blocking notifications:",e),a(!1)}finally{i(!1)}};return{hasBlockingNotifications:t,isChecking:r,checkForBlockingNotifications:n,markAllAsRead:()=>{a(!1)}}}},8278:(e,t,a)=>{"use strict";a.d(t,{Gw:()=>x,am:()=>h,checkVersionAndClearCache:()=>c,hQ:()=>m});var s=a(5317),l=a(6104);let r="1.1.0",i="mytube_app_version",n="mytube_last_cache_clear";async function c(){try{console.log("\uD83D\uDD0D Silently checking app version for cache management...");let t=localStorage.getItem(i),a=r;try{let t=await (0,s.x7)((0,s.H9)(l.db,"system/version"));if(t.exists()){var e;a=(null==(e=t.data())?void 0:e.version)||r}}catch(e){console.warn("Could not fetch server version, using current version:",e)}if(!t||t!==a){console.log("\uD83D\uDD04 Version changed: ".concat(t||"none"," → ").concat(a," - Clearing cache silently..."));try{return await o(),localStorage.setItem(i,a),localStorage.setItem(n,new Date().toISOString()),console.log("✅ Cache cleared silently due to version update"),setTimeout(()=>{window.location.reload()},1e3),!0}catch(e){return console.error("Silent cache clear failed, continuing normally:",e),localStorage.setItem(i,a),!1}}return console.log("✅ Version unchanged, no cache clearing needed"),!1}catch(e){return console.error("Error checking version:",e),!1}}async function o(){try{console.log("\uD83D\uDD07 Silently clearing application cache...");let e={};if(["firebase:authUser","firebase:host"].forEach(t=>{Object.keys(localStorage).filter(e=>e.includes(t)).forEach(t=>{e[t]=localStorage.getItem(t)})}),localStorage.clear(),Object.entries(e).forEach(e=>{let[t,a]=e;a&&localStorage.setItem(t,a)}),sessionStorage.clear(),"caches"in window)try{let e=await Promise.race([caches.keys(),new Promise((e,t)=>setTimeout(()=>t(Error("Cache keys timeout")),2e3))]);await Promise.race([Promise.all(e.map(e=>caches.delete(e))),new Promise((e,t)=>setTimeout(()=>t(Error("Cache deletion timeout")),2e3))])}catch(e){console.warn("Silent cache clear skipped browser caches:",e)}console.log("✅ Silent cache clear completed")}catch(e){console.warn("Silent cache clear encountered error:",e)}}async function d(){try{console.log("\uD83E\uDDF9 Clearing application cache and data...");let e={};if(["firebase:authUser","firebase:host"].forEach(t=>{Object.keys(localStorage).filter(e=>e.includes(t)).forEach(t=>{e[t]=localStorage.getItem(t)})}),localStorage.clear(),Object.entries(e).forEach(e=>{let[t,a]=e;a&&localStorage.setItem(t,a)}),sessionStorage.clear(),console.log("\uD83D\uDDD1️ Storage cleared"),"caches"in window)try{let e=await Promise.race([caches.keys(),new Promise((e,t)=>setTimeout(()=>t(Error("Cache keys timeout")),5e3))]);await Promise.race([Promise.all(e.map(e=>caches.delete(e))),new Promise((e,t)=>setTimeout(()=>t(Error("Cache deletion timeout")),5e3))]),console.log("\uD83D\uDDD1️ Browser caches cleared")}catch(e){console.warn("Could not clear browser caches:",e)}if("indexedDB"in window)try{for(let e of["firebaseLocalStorageDb","firebase-heartbeat-database","firebase-installations-database"])try{await Promise.race([new Promise(t=>{let a=indexedDB.deleteDatabase(e);a.onsuccess=()=>t(),a.onerror=()=>t(),a.onblocked=()=>t(),setTimeout(()=>t(),3e3)}),new Promise((e,t)=>setTimeout(()=>t(Error("IndexedDB timeout")),5e3))])}catch(t){console.warn("Could not clear IndexedDB ".concat(e,":"),t)}console.log("\uD83D\uDDD1️ IndexedDB cleared")}catch(e){console.warn("Could not clear IndexedDB:",e)}console.log("✅ Application cache cleared successfully")}catch(e){throw console.error("Error clearing cache:",e),e}}function x(){try{localStorage.clear(),sessionStorage.clear(),document.cookie.split(";").forEach(e=>{let t=e.indexOf("="),a=t>-1?e.substring(0,t).trim():e.trim();a&&(document.cookie="".concat(a,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"))}),console.log("✅ Simple cache clear completed")}catch(e){console.error("Error in simple cache clear:",e)}}function m(){return{currentVersion:r,localVersion:localStorage.getItem(i),lastCacheClear:localStorage.getItem(n)}}async function h(){try{await Promise.race([d(),new Promise((e,t)=>setTimeout(()=>t(Error("Cache clear operation timed out")),15e3))]),localStorage.setItem(n,new Date().toISOString()),alert("Cache cleared successfully! The page will now reload to apply changes."),window.location.reload()}catch(e){console.error("Error during manual cache clear:",e),confirm("Cache clearing encountered an issue. Would you like to try a simple page refresh instead?")?window.location.reload():alert("Please try refreshing the page manually (Ctrl+F5 or Cmd+Shift+R)")}}},8647:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var s=a(5155),l=a(2115),r=a(3592);function i(e){let{userId:t,onAllRead:a}=e,[i,n]=(0,l.useState)([]),[c,o]=(0,l.useState)(0),[d,x]=(0,l.useState)(!0);(0,l.useEffect)(()=>{t&&m()},[t]);let m=async()=>{try{x(!0);let e=await (0,r.AX)(t);n(e),0===e.length&&a()}catch(e){console.error("Error loading notifications:",e),a()}finally{x(!1)}},h=async()=>{let e=i[c];(null==e?void 0:e.id)&&(await (0,r.bA)(e.id,t),c<i.length-1?o(c+1):a())};if(d)return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,s.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===i.length)return null;let u=i[c];return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(u.type)}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,s.jsxs)("p",{className:"text-blue-100 text-sm",children:[c+1," of ",i.length," notifications"]})]})]}),(0,s.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,s.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:u.title}),(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,s.jsx)("p",{className:"text-gray-800 leading-relaxed",children:u.message})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,s.jsxs)("span",{children:["From: ",u.createdBy]}),(0,s.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?"".concat(Math.floor(t/60)," minutes ago"):t<86400?"".concat(Math.floor(t/3600)," hours ago"):"".concat(Math.floor(t/86400)," days ago")})(u.createdAt)})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,s.jsx)("span",{children:"Progress"}),(0,s.jsxs)("span",{children:[c+1,"/",i.length]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((c+1)/i.length*100,"%")}})})]}),(0,s.jsxs)("button",{onClick:h,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,s.jsx)("i",{className:"fas fa-check"}),(0,s.jsx)("span",{children:c<i.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,s.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,s.jsx)("i",{className:"fas fa-info-circle"}),(0,s.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}},8926:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(5155),l=a(2115),r=a(4752),i=a.n(r);function n(e){let{variant:t="homepage",className:a=""}=e,{isInstallable:r,isInstalled:n,installApp:c,getInstallInstructions:o}=function(){let[e,t]=(0,l.useState)(null),[a,s]=(0,l.useState)(!1),[r,i]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e=e=>{e.preventDefault(),t(e),s(!0)},a=()=>{i(!0),s(!1),t(null)};return window.matchMedia("(display-mode: standalone)").matches&&i(!0),window.addEventListener("beforeinstallprompt",e),window.addEventListener("appinstalled",a),()=>{window.removeEventListener("beforeinstallprompt",e),window.removeEventListener("appinstalled",a)}},[]),{isInstallable:a,isInstalled:r,installApp:async()=>{if(!e)return!1;try{await e.prompt();let{outcome:a}=await e.userChoice;if("accepted"===a)return i(!0),s(!1),t(null),!0;return!1}catch(e){return console.error("Error installing app:",e),!1}},getInstallInstructions:()=>{let e=navigator.userAgent.toLowerCase();return e.includes("chrome")&&!e.includes("edg")?{browser:"Chrome",steps:["Click the install button above","Or click the install icon in the address bar",'Click "Install" in the popup']}:e.includes("firefox")?{browser:"Firefox",steps:["Click the menu button (☰)",'Select "Install this site as an app"','Click "Install" in the dialog']}:e.includes("safari")?{browser:"Safari",steps:["Tap the Share button",'Scroll down and tap "Add to Home Screen"','Tap "Add" to install']}:e.includes("edg")?{browser:"Edge",steps:["Click the install button above","Or click the app icon in the address bar",'Click "Install" in the popup']}:{browser:"Your Browser",steps:["Look for an install option in your browser menu","Or check the address bar for an install icon","Follow your browser's installation prompts"]}}}}(),[d,x]=(0,l.useState)(!1),m=async()=>{await c()?i().fire({icon:"success",title:"App Installed!",text:"MyTube has been installed on your device. You can now access it from your home screen.",timer:3e3,showConfirmButton:!1}):r||x(!0)},h=()=>{let e=o();i().fire({title:"Install MyTube on ".concat(e.browser),html:'\n        <div class="text-left">\n          <p class="mb-4 text-gray-600">Follow these steps to install MyTube as an app:</p>\n          <ol class="list-decimal list-inside space-y-2">\n            '.concat(e.steps.map(e=>'<li class="text-gray-700">'.concat(e,"</li>")).join(""),'\n          </ol>\n          <div class="mt-6 p-4 bg-blue-50 rounded-lg">\n            <p class="text-sm text-blue-800">\n              <i class="fas fa-info-circle mr-2"></i>\n              Installing the app gives you faster access, offline capabilities, and a native app experience!\n            </p>\n          </div>\n        </div>\n      '),confirmButtonText:"Got it!",confirmButtonColor:"#3b82f6"})};return n?(0,s.jsx)("div",{className:"".concat(a),children:"homepage"===t?(0,s.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,s.jsx)("i",{className:"fas fa-check-circle text-4xl text-green-400 mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"App Installed!"}),(0,s.jsx)("p",{className:"text-white/80",children:"MyTube is installed on your device"})]}):(0,s.jsxs)("div",{className:"flex items-center text-green-400",children:[(0,s.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:"App Installed"})]})}):"homepage"===t?(0,s.jsxs)("div",{className:"glass-card p-8 hover:scale-105 transition-transform ".concat(a),children:[(0,s.jsx)("i",{className:"fas fa-mobile-alt text-5xl text-purple-400 mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Install MyTube App"}),(0,s.jsx)("p",{className:"text-white/80 mb-6",children:"Get the best experience with our mobile app"}),(0,s.jsx)("div",{className:"space-y-3",children:r?(0,s.jsxs)("button",{onClick:m,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Install Now"]}):(0,s.jsxs)("button",{onClick:h,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Install"]})}),(0,s.jsx)("div",{className:"mt-4 text-white/60 text-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-bolt mr-1"}),"Faster"]}),(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-wifi mr-1"}),"Offline"]}),(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-home mr-1"}),"Home Screen"]})]})})]}):(0,s.jsx)("div",{className:"glass-card p-4 ".concat(a),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-mobile-alt text-purple-400 text-xl mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-white font-semibold",children:"Install MyTube App"}),(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Get faster access & offline features"})]})]}),r?(0,s.jsxs)("button",{onClick:m,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-download mr-1"}),"Install"]}):(0,s.jsxs)("button",{onClick:h,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-1"}),"How to"]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,3592,6681,8441,1684,7358],()=>t(2751)),_N_E=e.O()}]);