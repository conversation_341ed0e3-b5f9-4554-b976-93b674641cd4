{"version": 3, "file": "index.esm.js", "sources": ["../../src/service.ts", "../../src/constants.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AnalyticsCallOptions,\n  CustomParams,\n  EventParams,\n  FirebaseAnalytics\n} from '@firebase/analytics-types';\nimport {\n  Analytics as AnalyticsServiceExp,\n  logEvent as logEventExp,\n  setAnalyticsCollectionEnabled as setAnalyticsCollectionEnabledExp,\n  setCurrentScreen as setCurrentScreenExp,\n  setUserId as setUserIdExp,\n  setUserProperties as setUserPropertiesExp\n} from '@firebase/analytics';\nimport { _FirebaseService, FirebaseApp } from '@firebase/app-compat';\n\nexport class AnalyticsService implements FirebaseAnalytics, _FirebaseService {\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: AnalyticsServiceExp\n  ) {}\n\n  logEvent(\n    eventName: string,\n    eventParams?: EventParams | CustomParams,\n    options?: AnalyticsCallOptions\n  ): void {\n    logEventExp(this._delegate, eventName as '', eventParams, options);\n  }\n\n  /**\n   * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n   * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n   */\n  setCurrentScreen(screenName: string, options?: AnalyticsCallOptions): void {\n    setCurrentScreenExp(this._delegate, screenName, options);\n  }\n\n  setUserId(id: string, options?: AnalyticsCallOptions): void {\n    setUserIdExp(this._delegate, id, options);\n  }\n\n  setUserProperties(\n    properties: CustomParams,\n    options?: AnalyticsCallOptions\n  ): void {\n    setUserPropertiesExp(this._delegate, properties, options);\n  }\n\n  setAnalyticsCollectionEnabled(enabled: boolean): void {\n    setAnalyticsCollectionEnabledExp(this._delegate, enabled);\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Officially recommended event names for gtag.js\n * Any other string is also allowed.\n */\nexport enum EventName {\n  ADD_SHIPPING_INFO = 'add_shipping_info',\n  ADD_PAYMENT_INFO = 'add_payment_info',\n  ADD_TO_CART = 'add_to_cart',\n  ADD_TO_WISHLIST = 'add_to_wishlist',\n  BEGIN_CHECKOUT = 'begin_checkout',\n  /**\n   * @deprecated\n   * This event name is deprecated and is unsupported in updated\n   * Enhanced Ecommerce reports.\n   */\n  CHECKOUT_PROGRESS = 'checkout_progress',\n  EXCEPTION = 'exception',\n  GENERATE_LEAD = 'generate_lead',\n  LOGIN = 'login',\n  PAGE_VIEW = 'page_view',\n  PURCHASE = 'purchase',\n  REFUND = 'refund',\n  REMOVE_FROM_CART = 'remove_from_cart',\n  SCREEN_VIEW = 'screen_view',\n  SEARCH = 'search',\n  SELECT_CONTENT = 'select_content',\n  SELECT_ITEM = 'select_item',\n  SELECT_PROMOTION = 'select_promotion',\n  /** @deprecated */\n  SET_CHECKOUT_OPTION = 'set_checkout_option',\n  SHARE = 'share',\n  SIGN_UP = 'sign_up',\n  TIMING_COMPLETE = 'timing_complete',\n  VIEW_CART = 'view_cart',\n  VIEW_ITEM = 'view_item',\n  VIEW_ITEM_LIST = 'view_item_list',\n  VIEW_PROMOTION = 'view_promotion',\n  VIEW_SEARCH_RESULTS = 'view_search_results'\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, {\n  _FirebaseNamespace,\n  FirebaseApp\n} from '@firebase/app-compat';\nimport { FirebaseAnalytics } from '@firebase/analytics-types';\nimport { name, version } from '../package.json';\nimport { AnalyticsService } from './service';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport {\n  settings as settingsExp,\n  isSupported as isSupportedExp\n} from '@firebase/analytics';\nimport { EventName } from './constants';\n\nconst factory: InstanceFactory<'analytics-compat'> = (\n  container: ComponentContainer\n) => {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const analyticsServiceExp = container.getProvider('analytics').getImmediate();\n\n  return new AnalyticsService(app as FirebaseApp, analyticsServiceExp);\n};\n\nexport function registerAnalytics(): void {\n  const namespaceExports = {\n    Analytics: AnalyticsService,\n    settings: settingsExp,\n    isSupported: isSupportedExp,\n    // We removed this enum in exp so need to re-create it here for compat.\n    EventName\n  };\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component('analytics-compat', factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n}\n\nregisterAnalytics();\nfirebase.registerVersion(name, version);\n\n/**\n * Define extension behavior of `registerAnalytics`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    analytics(app?: FirebaseApp): FirebaseAnalytics;\n  }\n  interface FirebaseApp {\n    analytics(): FirebaseAnalytics;\n  }\n}\n"], "names": ["logEventExp", "setCurrentScreenExp", "setUserIdExp", "setUserPropertiesExp", "setAnalyticsCollectionEnabledExp", "settingsExp", "isSupportedExp"], "mappings": ";;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAkBH,IAAA,gBAAA,kBAAA,YAAA;IACE,SACS,gBAAA,CAAA,GAAgB,EACd,SAA8B,EAAA;QADhC,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QACd,IAAS,CAAA,SAAA,GAAT,SAAS,CAAqB;KACrC;AAEJ,IAAA,gBAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UACE,SAAiB,EACjB,WAAwC,EACxC,OAA8B,EAAA;QAE9BA,QAAW,CAAC,IAAI,CAAC,SAAS,EAAE,SAAe,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;KACpE,CAAA;AAED;;;AAGG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,UAAkB,EAAE,OAA8B,EAAA;QACjEC,gBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;KAC1D,CAAA;AAED,IAAA,gBAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,EAAU,EAAE,OAA8B,EAAA;QAClDC,SAAY,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;KAC3C,CAAA;AAED,IAAA,gBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,UACE,UAAwB,EACxB,OAA8B,EAAA;QAE9BC,iBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;KAC3D,CAAA;IAED,gBAA6B,CAAA,SAAA,CAAA,6BAAA,GAA7B,UAA8B,OAAgB,EAAA;AAC5C,QAAAC,6BAAgC,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;KAC3D,CAAA;IACH,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACrED;;;;;;;;;;;;;;;AAeG;AAEH;;;AAGG;AACH,IAAY,SAkCX,CAAA;AAlCD,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AACvC,IAAA,SAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrC,IAAA,SAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,SAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC,IAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC;;;;AAIG;AACH,IAAA,SAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AACvC,IAAA,SAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,SAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,SAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,SAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB,IAAA,SAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,SAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;AACrC,IAAA,SAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,SAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB,IAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,SAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B,IAAA,SAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;;AAErC,IAAA,SAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;AAC3C,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,SAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACnB,IAAA,SAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC,IAAA,SAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,SAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB,IAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,SAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AACjC,IAAA,SAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;AAC7C,CAAC,EAlCW,SAAS,KAAT,SAAS,GAkCpB,EAAA,CAAA,CAAA;;ACvDD;;;;;;;;;;;;;;;AAeG;AAqBH,IAAM,OAAO,GAAwC,UACnD,SAA6B,EAAA;;IAG7B,IAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,CAAC;IAC/D,IAAM,mBAAmB,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,CAAC;AAE9E,IAAA,OAAO,IAAI,gBAAgB,CAAC,GAAkB,EAAE,mBAAmB,CAAC,CAAC;AACvE,CAAC,CAAC;SAEc,iBAAiB,GAAA;AAC/B,IAAA,IAAM,gBAAgB,GAAG;AACvB,QAAA,SAAS,EAAE,gBAAgB;AAC3B,QAAA,QAAQ,EAAEC,QAAW;AACrB,QAAA,WAAW,EAAEC,WAAc;;AAE3B,QAAA,SAAS,EAAA,SAAA;KACV,CAAC;IACD,QAA+B,CAAC,QAAQ,CAAC,iBAAiB,CACzD,IAAI,SAAS,CAAC,kBAAkB,EAAE,OAAO,EAAuB,QAAA,4BAAA;SAC7D,eAAe,CAAC,gBAAgB,CAAC;AACjC,SAAA,oBAAoB,CAAC,IAAI,CAAC,CAC9B,CAAC;AACJ,CAAC;AAED,iBAAiB,EAAE,CAAC;AACpB,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC;;;;"}