'use client'

import { useState } from 'react'
import { createUserWithEmailAndPassword, signOut, deleteUser } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'

export default function TestRegSimplePage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const addToResult = (text: string) => {
    setResult(prev => prev + text + '\n')
  }

  const testSimpleRegistration = async () => {
    setResult('')
    setIsLoading(true)
    
    let testUser: any = null
    
    try {
      addToResult('🧪 Testing Simple Registration Process...\n')
      
      // Step 1: Create Firebase Auth user
      addToResult('=== STEP 1: Creating Firebase Auth User ===')
      const testEmail = `test${Date.now()}@example.com`
      const testPassword = 'test123456'
      
      const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
      testUser = userCredential.user
      addToResult(`✅ Auth user created: ${testUser.uid}`)
      addToResult(`   Email: ${testUser.email}`)
      
      // Step 2: Wait for auth state
      addToResult('\n=== STEP 2: Waiting for Auth State ===')
      await new Promise(resolve => setTimeout(resolve, 2000))
      addToResult(`Current auth user: ${auth.currentUser?.uid}`)
      addToResult(`Auth state matches: ${auth.currentUser?.uid === testUser.uid}`)
      
      // Step 3: Create minimal user document (exactly like registration)
      addToResult('\n=== STEP 3: Creating User Document ===')
      
      // Generate referral code (same as registration)
      const timestamp = Date.now().toString().slice(-4)
      const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase()
      const userReferralCode = `MY${timestamp}${randomPart}`
      addToResult(`Generated referral code: ${userReferralCode}`)
      
      // Create user data (exactly like registration)
      const userData = {
        name: 'Test User',
        email: testEmail.toLowerCase(),
        mobile: '9876543210',
        referralCode: userReferralCode,
        referredBy: '',
        referralBonusCredited: false,
        plan: 'Trial',
        planExpiry: null,
        activeDays: 1,
        joinedDate: Timestamp.now(),
        wallet: 0,
        totalVideos: 0,
        todayVideos: 0,
        lastVideoDate: null,
        videoDuration: 30,
        status: 'active'
      }
      
      addToResult(`Document path: users/${testUser.uid}`)
      addToResult(`Data fields: ${Object.keys(userData).length}`)
      
      // Step 4: Attempt document creation
      addToResult('\n=== STEP 4: Creating Document ===')
      const userDocRef = doc(db, 'users', testUser.uid)
      
      addToResult('Attempting setDoc...')
      await setDoc(userDocRef, userData)
      addToResult('✅ setDoc completed successfully')
      
      // Step 5: Verify document
      addToResult('\n=== STEP 5: Verifying Document ===')
      const verifyDoc = await getDoc(userDocRef)
      
      if (verifyDoc.exists()) {
        const docData = verifyDoc.data()
        addToResult('✅ Document verification successful')
        addToResult(`   Name: ${docData.name}`)
        addToResult(`   Email: ${docData.email}`)
        addToResult(`   Plan: ${docData.plan}`)
        addToResult(`   Referral Code: ${docData.referralCode}`)
        addToResult(`   Wallet: ${docData.wallet}`)
        addToResult(`   Fields count: ${Object.keys(docData).length}`)
        
        addToResult('\n🎉 SUCCESS: Registration process works perfectly!')
        addToResult('The issue might be in the registration form logic or error handling.')
        
      } else {
        addToResult('❌ Document not found after creation')
        addToResult('This indicates a serious Firestore issue')
      }
      
    } catch (error: any) {
      addToResult(`❌ Test failed: ${error.message}`)
      addToResult(`   Error code: ${error.code}`)
      addToResult(`   Error name: ${error.name}`)
      
      if (error.code === 'permission-denied') {
        addToResult('\n🔧 PERMISSION DENIED ANALYSIS:')
        addToResult('   - Firestore security rules are blocking the write')
        addToResult('   - Check if user authentication is properly recognized')
        addToResult('   - Verify rules allow authenticated users to create documents')
      } else if (error.code === 'unavailable') {
        addToResult('\n🔧 FIRESTORE UNAVAILABLE:')
        addToResult('   - Check internet connection')
        addToResult('   - Verify Firestore is enabled in Firebase console')
      } else if (error.code === 'auth/email-already-in-use') {
        addToResult('\n🔧 EMAIL ALREADY IN USE:')
        addToResult('   - This is expected if testing multiple times')
        addToResult('   - Try with a different email or wait a moment')
      }
      
      addToResult(`\n   Full error details:`)
      addToResult(`   ${JSON.stringify(error, null, 2)}`)
    } finally {
      // Cleanup
      if (testUser) {
        try {
          addToResult('\n=== CLEANUP ===')
          await deleteUser(testUser)
          addToResult('✅ Test user deleted')
        } catch (deleteError: any) {
          addToResult(`⚠️ User deletion failed: ${deleteError.message}`)
        }
      }
      
      try {
        await signOut(auth)
        addToResult('✅ Signed out')
      } catch (signOutError: any) {
        addToResult(`⚠️ Sign out failed: ${signOutError.message}`)
      }
      
      setIsLoading(false)
    }
  }

  const testFirebaseBasics = async () => {
    setResult('')
    setIsLoading(true)
    
    try {
      addToResult('🔧 Testing Firebase Basics...\n')
      
      // Test 1: Firebase instances
      addToResult('=== TEST 1: Firebase Instances ===')
      addToResult(`Auth: ${auth ? '✅ Initialized' : '❌ Not initialized'}`)
      addToResult(`Firestore: ${db ? '✅ Initialized' : '❌ Not initialized'}`)
      addToResult(`Current user: ${auth.currentUser?.uid || 'None'}`)
      
      // Test 2: Basic Firestore write
      addToResult('\n=== TEST 2: Basic Firestore Write ===')
      const testDoc = doc(db, 'test_basic', `test_${Date.now()}`)
      await setDoc(testDoc, { test: true, timestamp: Timestamp.now() })
      addToResult('✅ Basic write successful')
      
      // Test 3: Basic Firestore read
      addToResult('\n=== TEST 3: Basic Firestore Read ===')
      const readDoc = await getDoc(testDoc)
      if (readDoc.exists()) {
        addToResult('✅ Basic read successful')
        addToResult(`   Data: ${JSON.stringify(readDoc.data())}`)
      } else {
        addToResult('❌ Basic read failed')
      }
      
      addToResult('\n✅ Firebase basics are working correctly')
      
    } catch (error: any) {
      addToResult(`❌ Firebase basics test failed: ${error.message}`)
      addToResult(`   Error code: ${error.code}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Test Simple Registration</h1>
        
        <div className="glass-card p-6 mb-6">
          <div className="flex gap-4 mb-4">
            <button
              onClick={testFirebaseBasics}
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? 'Testing...' : 'Test Firebase Basics'}
            </button>
            
            <button
              onClick={testSimpleRegistration}
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? 'Testing...' : 'Test Registration Process'}
            </button>
          </div>
          
          <div className="bg-black/50 p-4 rounded-lg">
            <pre className="text-white text-sm whitespace-pre-wrap overflow-auto max-h-96">
              {result || 'Click a test button to start...'}
            </pre>
          </div>
        </div>
        
        <div className="text-center">
          <a href="/register" className="text-blue-400 hover:text-blue-300 underline">
            ← Back to Registration
          </a>
        </div>
      </div>
    </div>
  )
}
