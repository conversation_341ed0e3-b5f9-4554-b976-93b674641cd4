(()=>{var e={};e.id=8171,e.ids=[8171],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55971:(e,r,s)=>{Promise.resolve().then(s.bind(s,68001))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68001:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\clear-cache\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\clear-cache\\page.tsx","default")},70043:(e,r,s)=>{Promise.resolve().then(s.bind(s,77111))},76251:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>h,tree:()=>c});var t=s(65239),a=s(48088),o=s(88170),i=s.n(o),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(r,l);let c={children:["",{children:["clear-cache",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,68001)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\clear-cache\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\clear-cache\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/clear-cache/page",pathname:"/clear-cache",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},77111:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o});var t=s(60687),a=s(43210);function o(){let[e,r]=(0,a.useState)(""),[s,o]=(0,a.useState)(!1),i=e=>{r(r=>r+e+"\n"),console.log(e)},n=async()=>{r(""),o(!0);try{if(i("\uD83E\uDDF9 Starting cache clearing process..."),"caches"in window){let e=await caches.keys();for(let r of(i(`📦 Found ${e.length} caches: ${e.join(", ")}`),e))await caches.delete(r),i(`✅ Deleted cache: ${r}`)}else i("❌ Cache API not supported");if("serviceWorker"in navigator){let e=await navigator.serviceWorker.getRegistrations();for(let r of(i(`🔧 Found ${e.length} service worker registrations`),e))await r.unregister(),i(`✅ Unregistered service worker: ${r.scope}`)}else i("❌ Service Worker API not supported");localStorage.clear(),i("✅ Cleared localStorage"),sessionStorage.clear(),i("✅ Cleared sessionStorage"),"indexedDB"in window&&i("✅ IndexedDB available (manual clearing may be needed)"),i("\n\uD83C\uDF89 Cache clearing completed!"),i("\uD83D\uDCA1 Refresh the page to see changes")}catch(e){i(`❌ Cache clearing failed: ${e.message}`),console.error("Cache clearing error:",e)}finally{o(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"glass-card p-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Clear Cache & Service Worker"}),(0,t.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,t.jsx)("button",{onClick:n,disabled:s,className:"btn-primary w-full",children:s?"Clearing Cache...":"Clear All Cache & Service Workers"}),(0,t.jsx)("button",{onClick:()=>{i("\uD83D\uDD04 Performing hard refresh..."),window.location.reload()},className:"btn-secondary w-full",children:"Hard Refresh Page"})]}),(0,t.jsx)("div",{className:"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto",children:e||"Click the button to clear cache and service workers..."}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-yellow-500/20 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,t.jsxs)("ol",{className:"text-white/80 text-sm space-y-1",children:[(0,t.jsx)("li",{children:'1. Click "Clear All Cache & Service Workers"'}),(0,t.jsx)("li",{children:"2. Wait for completion"}),(0,t.jsx)("li",{children:'3. Click "Hard Refresh Page" or press Ctrl+Shift+R'}),(0,t.jsx)("li",{children:"4. Try registration again"})]})]}),(0,t.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,t.jsx)("a",{href:"/register",className:"btn-primary inline-block",children:"Go to Registration"}),(0,t.jsx)("a",{href:"/debug-registration-simple",className:"btn-secondary inline-block ml-4",children:"Go to Debug Registration"})]})]})})})}}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6204,8441],()=>s(76251));module.exports=t})();