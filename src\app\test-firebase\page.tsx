'use client'

import { useState } from 'react'
import { auth, db } from '@/lib/firebase'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc } from 'firebase/firestore'

export default function TestFirebase() {
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)
  const [regResult, setRegResult] = useState('')
  const [regLoading, setRegLoading] = useState(false)

  const testFirebaseConnection = async () => {
    setLoading(true)
    setResult('Testing Firebase connection...\n')

    try {
      // Test 1: Check Firebase config
      setResult(prev => prev + 'Firebase config loaded ✓\n')
      setResult(prev => prev + `Project ID: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}\n`)

      // Test 2: Test authentication
      const testEmail = `test${Date.now()}@example.com`
      const testPassword = 'test123456'

      setResult(prev => prev + 'Creating test user...\n')
      const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
      const user = userCredential.user
      setResult(prev => prev + `User created with UID: ${user.uid} ✓\n`)
      setResult(prev => prev + `User email: ${user.email}\n`)

      // Test 3: Test Firestore write
      setResult(prev => prev + 'Testing Firestore write...\n')
      const testData = {
        name: 'Test User',
        email: testEmail,
        mobile: '1234567890',
        referralCode: 'TEST123',
        referredBy: null,
        plan: 'trial',
        planExpiry: null,
        activeDays: 2,
        joinedDate: new Date(),
        wallet: 0,
        totalVideos: 0,
        todayVideos: 0,
        lastVideoDate: null,
        createdAt: new Date(),
        test: true
      }

      setResult(prev => prev + `Writing to collection: users, document: ${user.uid}\n`)
      await setDoc(doc(db, 'users', user.uid), testData)
      setResult(prev => prev + 'Firestore write successful ✓\n')

      // Test 4: Test Firestore read
      setResult(prev => prev + 'Testing Firestore read...\n')
      const docSnap = await getDoc(doc(db, 'users', user.uid))
      if (docSnap.exists()) {
        setResult(prev => prev + 'Firestore read successful ✓\n')
        const data = docSnap.data()
        setResult(prev => prev + `Document exists: ${docSnap.exists()}\n`)
        setResult(prev => prev + `Data keys: ${Object.keys(data).join(', ')}\n`)
        setResult(prev => prev + `Name: ${data.name}\n`)
        setResult(prev => prev + `Email: ${data.email}\n`)
        setResult(prev => prev + `Wallet: ${data.wallet}\n`)
      } else {
        setResult(prev => prev + 'Document not found ✗\n')
      }

      // Test 5: Test transaction creation
      setResult(prev => prev + 'Testing transaction creation...\n')
      const transactionData = {
        userId: user.uid,
        type: 'test',
        amount: 10,
        description: 'Test transaction',
        date: new Date(),
        status: 'completed'
      }

      const transactionRef = doc(db, 'transactions', `test_${user.uid}_${Date.now()}`)
      await setDoc(transactionRef, transactionData)
      setResult(prev => prev + 'Transaction creation successful ✓\n')

      // Clean up - delete the test user
      setResult(prev => prev + 'Cleaning up test data...\n')
      await user.delete()
      setResult(prev => prev + 'Test user deleted ✓\n')

      setResult(prev => prev + '\n🎉 All tests passed! Firebase is working correctly.')

    } catch (error: any) {
      console.error('Firebase test error:', error)
      setResult(prev => prev + `\n❌ Error: ${error.message}\n`)
      setResult(prev => prev + `Error code: ${error.code}\n`)
      if (error.code) {
        setResult(prev => prev + `Error details: ${error.code}\n`)
      }
      setResult(prev => prev + `Stack trace: ${error.stack}\n`)
    } finally {
      setLoading(false)
    }
  }

  const testRegistrationFlow = async () => {
    setRegLoading(true)
    setRegResult('Testing registration flow...\n')

    try {
      const testEmail = `regtest${Date.now()}@example.com`
      const testPassword = 'test123456'

      setRegResult(prev => prev + `Creating user with email: ${testEmail}\n`)

      // Step 1: Create user account
      const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
      const user = userCredential.user
      setRegResult(prev => prev + `✓ User account created: ${user.uid}\n`)

      // Step 2: Create user document (same as registration)
      const userData = {
        name: 'Test Registration User',
        email: testEmail,
        mobile: '**********',
        referralCode: `TEST${Math.random().toString(36).substr(2, 4).toUpperCase()}`, // Keep test format for test users
        referredBy: null,
        plan: 'trial',
        planExpiry: null,
        activeDays: 2,
        joinedDate: new Date(),
        wallet: 0,
        totalVideos: 0,
        todayVideos: 0,
        lastVideoDate: null
      }

      setRegResult(prev => prev + 'Creating user document in Firestore...\n')
      await setDoc(doc(db, 'users', user.uid), userData)
      setRegResult(prev => prev + '✓ User document created successfully\n')

      // Step 3: Verify document was created
      setRegResult(prev => prev + 'Verifying document creation...\n')
      const docSnap = await getDoc(doc(db, 'users', user.uid))
      if (docSnap.exists()) {
        const data = docSnap.data()
        setRegResult(prev => prev + '✓ Document verification successful\n')
        setRegResult(prev => prev + `  Name: ${data.name}\n`)
        setRegResult(prev => prev + `  Email: ${data.email}\n`)
        setRegResult(prev => prev + `  Wallet: ${data.wallet}\n`)
        setRegResult(prev => prev + `  Plan: ${data.plan}\n`)
      } else {
        setRegResult(prev => prev + '✗ Document not found after creation\n')
      }

      // Clean up
      setRegResult(prev => prev + 'Cleaning up...\n')
      await user.delete()
      setRegResult(prev => prev + '✓ Test user deleted\n')

      setRegResult(prev => prev + '\n🎉 Registration flow test completed successfully!')

    } catch (error: any) {
      console.error('Registration test error:', error)
      setRegResult(prev => prev + `\n❌ Registration Error: ${error.message}\n`)
      setRegResult(prev => prev + `Error code: ${error.code}\n`)
      if (error.stack) {
        setRegResult(prev => prev + `Stack: ${error.stack}\n`)
      }
    } finally {
      setRegLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Firebase Connection Test</h1>
        
        <div className="flex gap-4 mb-6">
          <button
            onClick={testFirebaseConnection}
            disabled={loading}
            className="btn-primary"
          >
            {loading ? 'Testing...' : 'Test Firebase Connection'}
          </button>
          <button
            onClick={testRegistrationFlow}
            disabled={regLoading}
            className="btn-secondary"
          >
            {regLoading ? 'Testing...' : 'Test Registration Flow'}
          </button>
        </div>
        
        <div className="glass-card p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Test Results:</h2>
          <pre className="text-white/80 whitespace-pre-wrap font-mono text-sm">
            {result || 'Click the button above to test Firebase connection'}
          </pre>
        </div>
      </div>
    </div>
  )
}
