"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8278],{6104:(e,a,o)=>{o.d(a,{db:()=>i,j2:()=>s});var r=o(3915),c=o(3004),t=o(5317),l=o(858);let n=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),s=(0,c.xI)(n),i=(0,t.aU)(n);(0,l.c7)(n)},8278:(e,a,o)=>{o.d(a,{Gw:()=>h,am:()=>u,checkVersionAndClearCache:()=>s,hQ:()=>m});var r=o(5317),c=o(6104);let t="1.1.0",l="mytube_app_version",n="mytube_last_cache_clear";async function s(){try{console.log("\uD83D\uDD0D Silently checking app version for cache management...");let a=localStorage.getItem(l),o=t;try{let a=await (0,r.x7)((0,r.H9)(c.db,"system/version"));if(a.exists()){var e;o=(null==(e=a.data())?void 0:e.version)||t}}catch(e){console.warn("Could not fetch server version, using current version:",e)}if(!a||a!==o){console.log("\uD83D\uDD04 Version changed: ".concat(a||"none"," → ").concat(o," - Clearing cache silently..."));try{return await i(),localStorage.setItem(l,o),localStorage.setItem(n,new Date().toISOString()),console.log("✅ Cache cleared silently due to version update"),setTimeout(()=>{window.location.reload()},1e3),!0}catch(e){return console.error("Silent cache clear failed, continuing normally:",e),localStorage.setItem(l,o),!1}}return console.log("✅ Version unchanged, no cache clearing needed"),!1}catch(e){return console.error("Error checking version:",e),!1}}async function i(){try{console.log("\uD83D\uDD07 Silently clearing application cache...");let e={};if(["firebase:authUser","firebase:host"].forEach(a=>{Object.keys(localStorage).filter(e=>e.includes(a)).forEach(a=>{e[a]=localStorage.getItem(a)})}),localStorage.clear(),Object.entries(e).forEach(e=>{let[a,o]=e;o&&localStorage.setItem(a,o)}),sessionStorage.clear(),"caches"in window)try{let e=await Promise.race([caches.keys(),new Promise((e,a)=>setTimeout(()=>a(Error("Cache keys timeout")),2e3))]);await Promise.race([Promise.all(e.map(e=>caches.delete(e))),new Promise((e,a)=>setTimeout(()=>a(Error("Cache deletion timeout")),2e3))])}catch(e){console.warn("Silent cache clear skipped browser caches:",e)}console.log("✅ Silent cache clear completed")}catch(e){console.warn("Silent cache clear encountered error:",e)}}async function d(){try{console.log("\uD83E\uDDF9 Clearing application cache and data...");let e={};if(["firebase:authUser","firebase:host"].forEach(a=>{Object.keys(localStorage).filter(e=>e.includes(a)).forEach(a=>{e[a]=localStorage.getItem(a)})}),localStorage.clear(),Object.entries(e).forEach(e=>{let[a,o]=e;o&&localStorage.setItem(a,o)}),sessionStorage.clear(),console.log("\uD83D\uDDD1️ Storage cleared"),"caches"in window)try{let e=await Promise.race([caches.keys(),new Promise((e,a)=>setTimeout(()=>a(Error("Cache keys timeout")),5e3))]);await Promise.race([Promise.all(e.map(e=>caches.delete(e))),new Promise((e,a)=>setTimeout(()=>a(Error("Cache deletion timeout")),5e3))]),console.log("\uD83D\uDDD1️ Browser caches cleared")}catch(e){console.warn("Could not clear browser caches:",e)}if("indexedDB"in window)try{for(let e of["firebaseLocalStorageDb","firebase-heartbeat-database","firebase-installations-database"])try{await Promise.race([new Promise(a=>{let o=indexedDB.deleteDatabase(e);o.onsuccess=()=>a(),o.onerror=()=>a(),o.onblocked=()=>a(),setTimeout(()=>a(),3e3)}),new Promise((e,a)=>setTimeout(()=>a(Error("IndexedDB timeout")),5e3))])}catch(a){console.warn("Could not clear IndexedDB ".concat(e,":"),a)}console.log("\uD83D\uDDD1️ IndexedDB cleared")}catch(e){console.warn("Could not clear IndexedDB:",e)}console.log("✅ Application cache cleared successfully")}catch(e){throw console.error("Error clearing cache:",e),e}}function h(){try{localStorage.clear(),sessionStorage.clear(),document.cookie.split(";").forEach(e=>{let a=e.indexOf("="),o=a>-1?e.substring(0,a).trim():e.trim();o&&(document.cookie="".concat(o,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"))}),console.log("✅ Simple cache clear completed")}catch(e){console.error("Error in simple cache clear:",e)}}function m(){return{currentVersion:t,localVersion:localStorage.getItem(l),lastCacheClear:localStorage.getItem(n)}}async function u(){try{await Promise.race([d(),new Promise((e,a)=>setTimeout(()=>a(Error("Cache clear operation timed out")),15e3))]),localStorage.setItem(n,new Date().toISOString()),alert("Cache cleared successfully! The page will now reload to apply changes."),window.location.reload()}catch(e){console.error("Error during manual cache clear:",e),confirm("Cache clearing encountered an issue. Would you like to try a simple page refresh instead?")?window.location.reload():alert("Please try refreshing the page manually (Ctrl+F5 or Cmd+Shift+R)")}}}}]);