"use strict";exports.id=1391,exports.ids=[1391],exports.modules={91391:(t,e,a)=>{a.d(e,{CF:()=>c,I0:()=>w,Pn:()=>n,TK:()=>h,getAllPendingWithdrawals:()=>L,getAllWithdrawals:()=>D,hG:()=>u,lo:()=>i,nQ:()=>E,updateWithdrawalStatus:()=>g,x5:()=>l});var o=a(75535),r=a(33784),s=a(3582);let d=new Map;async function n(){let t="dashboard-stats",e=function(t){let e=d.get(t);return e&&Date.now()-e.timestamp<3e5?e.data:null}(t);if(e)return e;try{let e=new Date;e.setHours(0,0,0,0);let a=o.Dc.fromDate(e),n=await (0,o.getDocs)((0,o.collection)(r.db,s.COLLECTIONS.users)),i=n.size,l=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o._M)(s.FIELD_NAMES.joinedDate,">=",a)),c=(await (0,o.getDocs)(l)).size,E=0,w=0,L=0,D=0;n.forEach(t=>{let a=t.data();E+=a[s.FIELD_NAMES.totalVideos]||0,w+=a[s.FIELD_NAMES.wallet]||0;let o=a[s.FIELD_NAMES.lastVideoDate]?.toDate();o&&o.toDateString()===e.toDateString()&&(L+=a[s.FIELD_NAMES.todayVideos]||0)});try{let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o._M)(s.FIELD_NAMES.type,"==","video_earning"),(0,o.AB)(1e3));(await (0,o.getDocs)(t)).forEach(t=>{let a=t.data(),o=a[s.FIELD_NAMES.date]?.toDate();o&&o>=e&&(D+=a[s.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let h=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o._M)("status","==","pending")),u=(await (0,o.getDocs)(h)).size,g=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o._M)("date",">=",a)),S=(await (0,o.getDocs)(g)).size,p={totalUsers:i,totalVideos:E,totalEarnings:w,pendingWithdrawals:u,todayUsers:c,todayVideos:L,todayEarnings:D,todayWithdrawals:S};return d.set(t,{data:p,timestamp:Date.now()}),p}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function i(t=50,e=null){try{let a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,o.AB)(t));e&&(a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,o.HM)(e),(0,o.AB)(t)));let d=await (0,o.getDocs)(a);return{users:d.docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function l(t){try{if(!t||0===t.trim().length)return[];let e=t.toLowerCase().trim(),a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(a)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})).filter(t=>{let a=String(t[s.FIELD_NAMES.name]||"").toLowerCase(),o=String(t[s.FIELD_NAMES.email]||"").toLowerCase(),r=String(t[s.FIELD_NAMES.mobile]||"").toLowerCase(),d=String(t[s.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(e)||o.includes(e)||r.includes(e)||d.includes(e)})}catch(t){throw console.error("Error searching users:",t),t}}async function c(){try{let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(t)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()}))}catch(t){throw console.error("Error getting all users:",t),t}}async function E(){try{let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users));return(await (0,o.getDocs)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function w(t=50,e=null){try{let a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o.My)(s.FIELD_NAMES.date,"desc"),(0,o.AB)(t));e&&(a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o.My)(s.FIELD_NAMES.date,"desc"),(0,o.HM)(e),(0,o.AB)(t)));let d=await (0,o.getDocs)(a);return{transactions:d.docs.map(t=>({id:t.id,...t.data(),date:t.data()[s.FIELD_NAMES.date]?.toDate()})),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting transactions:",t),t}}async function L(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o._M)("status","==","pending"),(0,o.My)("date","desc")),e=(await (0,o.getDocs)(t)).docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()}));return console.log(`✅ Loaded ${e.length} pending withdrawals`),e}catch(t){throw console.error("Error getting all pending withdrawals:",t),t}}async function D(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o.My)("date","desc")),e=(await (0,o.getDocs)(t)).docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()}));return console.log(`✅ Loaded ${e.length} total withdrawals`),e}catch(t){throw console.error("Error getting all withdrawals:",t),t}}async function h(t,e){try{await (0,o.mZ)((0,o.H9)(r.db,s.COLLECTIONS.users,t),e),d.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function u(t){try{await (0,o.kd)((0,o.H9)(r.db,s.COLLECTIONS.users,t)),d.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function g(t,e,n){try{let i=await (0,o.x7)((0,o.H9)(r.db,s.COLLECTIONS.withdrawals,t));if(!i.exists())throw Error("Withdrawal not found");let{userId:l,amount:c,status:E}=i.data(),w={status:e,updatedAt:o.Dc.now()};if(n&&(w.adminNotes=n),await (0,o.mZ)((0,o.H9)(r.db,s.COLLECTIONS.withdrawals,t),w),"approved"===e&&"approved"!==E){let{addTransaction:t}=await Promise.resolve().then(a.bind(a,3582));await t(l,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===e&&"rejected"!==E){let{updateWalletBalance:t,addTransaction:e}=await Promise.resolve().then(a.bind(a,3582));await t(l,c),await e(l,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}d.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}};