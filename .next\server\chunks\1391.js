"use strict";exports.id=1391,exports.ids=[1391],exports.modules={91391:(t,e,a)=>{a.d(e,{CF:()=>l,I0:()=>w,Pn:()=>n,TK:()=>u,getWithdrawals:()=>D,hG:()=>L,lo:()=>i,nQ:()=>E,updateWithdrawalStatus:()=>h,x5:()=>c});var o=a(75535),r=a(33784),s=a(3582);let d=new Map;async function n(){let t="dashboard-stats",e=function(t){let e=d.get(t);return e&&Date.now()-e.timestamp<3e5?e.data:null}(t);if(e)return e;try{let e=new Date;e.setHours(0,0,0,0);let a=o.Dc.fromDate(e),n=await (0,o.getDocs)((0,o.collection)(r.db,s.COLLECTIONS.users)),i=n.size,c=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o._M)(s.FIELD_NAMES.joinedDate,">=",a)),l=(await (0,o.getDocs)(c)).size,E=0,w=0,D=0,u=0;n.forEach(t=>{let a=t.data();E+=a[s.FIELD_NAMES.totalVideos]||0,w+=a[s.FIELD_NAMES.wallet]||0;let o=a[s.FIELD_NAMES.lastVideoDate]?.toDate();o&&o.toDateString()===e.toDateString()&&(D+=a[s.FIELD_NAMES.todayVideos]||0)});try{let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o._M)(s.FIELD_NAMES.type,"==","video_earning"),(0,o.AB)(1e3));(await (0,o.getDocs)(t)).forEach(t=>{let a=t.data(),o=a[s.FIELD_NAMES.date]?.toDate();o&&o>=e&&(u+=a[s.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let L=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o._M)("status","==","pending")),h=(await (0,o.getDocs)(L)).size,S=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o._M)("date",">=",a)),g=(await (0,o.getDocs)(S)).size,C={totalUsers:i,totalVideos:E,totalEarnings:w,pendingWithdrawals:h,todayUsers:l,todayVideos:D,todayEarnings:u,todayWithdrawals:g};return d.set(t,{data:C,timestamp:Date.now()}),C}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function i(t=50,e=null){try{let a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,o.AB)(t));e&&(a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,o.HM)(e),(0,o.AB)(t)));let d=await (0,o.getDocs)(a);return{users:d.docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function c(t){try{if(!t||0===t.trim().length)return[];let e=t.toLowerCase().trim(),a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(a)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()})).filter(t=>{let a=String(t[s.FIELD_NAMES.name]||"").toLowerCase(),o=String(t[s.FIELD_NAMES.email]||"").toLowerCase(),r=String(t[s.FIELD_NAMES.mobile]||"").toLowerCase(),d=String(t[s.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(e)||o.includes(e)||r.includes(e)||d.includes(e)})}catch(t){throw console.error("Error searching users:",t),t}}async function l(){try{let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users),(0,o.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(t)).docs.map(t=>({id:t.id,...t.data(),joinedDate:t.data()[s.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:t.data()[s.FIELD_NAMES.planExpiry]?.toDate()}))}catch(t){throw console.error("Error getting all users:",t),t}}async function E(){try{let t=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.users));return(await (0,o.getDocs)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function w(t=50,e=null){try{let a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o.My)(s.FIELD_NAMES.date,"desc"),(0,o.AB)(t));e&&(a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.transactions),(0,o.My)(s.FIELD_NAMES.date,"desc"),(0,o.HM)(e),(0,o.AB)(t)));let d=await (0,o.getDocs)(a);return{transactions:d.docs.map(t=>({id:t.id,...t.data(),date:t.data()[s.FIELD_NAMES.date]?.toDate()})),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting transactions:",t),t}}async function D(t=50,e=null){try{let a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o.My)("date","desc"),(0,o.AB)(t));e&&(a=(0,o.P)((0,o.collection)(r.db,s.COLLECTIONS.withdrawals),(0,o.My)("date","desc"),(0,o.HM)(e),(0,o.AB)(t)));let d=await (0,o.getDocs)(a);return{withdrawals:d.docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()})),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function u(t,e){try{await (0,o.mZ)((0,o.H9)(r.db,s.COLLECTIONS.users,t),e),d.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function L(t){try{await (0,o.kd)((0,o.H9)(r.db,s.COLLECTIONS.users,t)),d.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function h(t,e,n){try{let i=await (0,o.x7)((0,o.H9)(r.db,s.COLLECTIONS.withdrawals,t));if(!i.exists())throw Error("Withdrawal not found");let{userId:c,amount:l,status:E}=i.data(),w={status:e,updatedAt:o.Dc.now()};if(n&&(w.adminNotes=n),await (0,o.mZ)((0,o.H9)(r.db,s.COLLECTIONS.withdrawals,t),w),"approved"===e&&"approved"!==E){let{addTransaction:t}=await Promise.resolve().then(a.bind(a,3582));await t(c,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${l} processed for transfer`})}if("rejected"===e&&"rejected"!==E){let{updateWalletBalance:t,addTransaction:e}=await Promise.resolve().then(a.bind(a,3582));await t(c,l),await e(c,{type:"withdrawal_rejected",amount:l,description:`Withdrawal rejected - ₹${l} credited back to wallet`})}d.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}};