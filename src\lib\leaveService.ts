import { db } from './firebase'
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  Timestamp 
} from 'firebase/firestore'

export interface AdminLeave {
  id: string
  date: Date
  reason: string
  type: 'holiday' | 'maintenance' | 'emergency'
  createdBy: string
  createdAt: Date
}

export interface UserLeave {
  id: string
  userId: string
  date: Date
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  appliedAt: Date
  reviewedBy?: string
  reviewedAt?: Date
  reviewNotes?: string
}

const COLLECTIONS = {
  adminLeaves: 'adminLeaves',
  userLeaves: 'userLeaves'
}

// Admin Leave Functions
export async function createAdminLeave(leaveData: Omit<AdminLeave, 'id' | 'createdAt'>) {
  try {
    const docRef = await addDoc(collection(db, COLLECTIONS.adminLeaves), {
      ...leaveData,
      date: Timestamp.fromDate(leaveData.date),
      createdAt: Timestamp.now()
    })
    return docRef.id
  } catch (error) {
    console.error('Error creating admin leave:', error)
    throw error
  }
}

export async function getAdminLeaves(): Promise<AdminLeave[]> {
  try {
    const q = query(
      collection(db, COLLECTIONS.adminLeaves),
      orderBy('date', 'asc')
    )
    const querySnapshot = await getDocs(q)

    const leaves = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date.toDate(),
      createdAt: doc.data().createdAt.toDate()
    })) as AdminLeave[]

    console.log('📅 All admin leaves:', leaves)
    return leaves
  } catch (error) {
    console.error('Error getting admin leaves:', error)
    throw error
  }
}

// Debug function to check current admin leave status
export async function debugAdminLeaveStatus(): Promise<void> {
  try {
    const today = new Date()
    console.log('🔍 Debug: Checking admin leave status for today:', today.toDateString())

    const isLeave = await isAdminLeaveDay(today)
    console.log('📊 Debug: Admin leave result:', isLeave)

    const allLeaves = await getAdminLeaves()
    console.log('📅 Debug: All admin leaves in database:', allLeaves)

    const todayLeaves = allLeaves.filter(leave =>
      leave.date.toDateString() === today.toDateString()
    )
    console.log('📅 Debug: Today\'s admin leaves:', todayLeaves)
  } catch (error) {
    console.error('❌ Debug: Error checking admin leave status:', error)
  }
}

export async function deleteAdminLeave(leaveId: string) {
  try {
    await deleteDoc(doc(db, COLLECTIONS.adminLeaves, leaveId))
  } catch (error) {
    console.error('Error deleting admin leave:', error)
    throw error
  }
}

export async function isAdminLeaveDay(date: Date): Promise<boolean> {
  try {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    console.log('🔍 Checking admin leave for date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString())

    const q = query(
      collection(db, COLLECTIONS.adminLeaves),
      where('date', '>=', Timestamp.fromDate(startOfDay)),
      where('date', '<=', Timestamp.fromDate(endOfDay))
    )

    const querySnapshot = await getDocs(q)
    const hasAdminLeave = !querySnapshot.empty

    if (hasAdminLeave) {
      console.log('📅 Found admin leave(s) for today:', querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        date: doc.data().date.toDate()
      })))
    } else {
      console.log('📅 No admin leaves found for today')
    }

    return hasAdminLeave
  } catch (error) {
    console.error('❌ Error checking admin leave day:', error)
    // Return false (no leave) on error to avoid blocking work unnecessarily
    return false
  }
}

// User Leave Functions
export async function applyUserLeave(leaveData: Omit<UserLeave, 'id' | 'appliedAt' | 'status'>) {
  try {
    // Check if user has available leave quota for automatic approval
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const currentMonth = currentDate.getMonth() + 1

    const usedLeaves = await getUserMonthlyLeaveCount(leaveData.userId, currentYear, currentMonth)
    const maxLeaves = 4 // Monthly leave quota

    // Determine status and approval details
    let status: 'pending' | 'approved' = 'pending'
    let reviewedBy: string | undefined
    let reviewedAt: any = undefined
    let reviewNotes: string | undefined

    // Auto-approve if user has available quota
    if (usedLeaves < maxLeaves) {
      status = 'approved'
      reviewedBy = 'system'
      reviewedAt = Timestamp.now()
      reviewNotes = `Auto-approved: ${usedLeaves + 1}/${maxLeaves} monthly leaves used`
    }

    const docRef = await addDoc(collection(db, COLLECTIONS.userLeaves), {
      ...leaveData,
      date: Timestamp.fromDate(leaveData.date),
      status,
      appliedAt: Timestamp.now(),
      ...(reviewedBy && { reviewedBy }),
      ...(reviewedAt && { reviewedAt }),
      ...(reviewNotes && { reviewNotes })
    })

    return {
      id: docRef.id,
      autoApproved: status === 'approved',
      usedLeaves: usedLeaves + (status === 'approved' ? 1 : 0),
      maxLeaves
    }
  } catch (error) {
    console.error('Error applying user leave:', error)
    throw error
  }
}

export async function getUserLeaves(userId: string): Promise<UserLeave[]> {
  try {
    const q = query(
      collection(db, COLLECTIONS.userLeaves),
      where('userId', '==', userId),
      orderBy('date', 'desc')
    )
    const querySnapshot = await getDocs(q)

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date.toDate(),
      appliedAt: doc.data().appliedAt.toDate(),
      reviewedAt: doc.data().reviewedAt?.toDate()
    })) as UserLeave[]
  } catch (error) {
    console.error('Error getting user leaves:', error)
    throw error
  }
}

// Get all user leaves for admin review
export async function getAllUserLeaves(): Promise<(UserLeave & { userName?: string; userEmail?: string })[]> {
  try {
    const q = query(
      collection(db, COLLECTIONS.userLeaves),
      orderBy('appliedAt', 'desc')
    )
    const querySnapshot = await getDocs(q)

    const leaves = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date.toDate(),
      appliedAt: doc.data().appliedAt.toDate(),
      reviewedAt: doc.data().reviewedAt?.toDate()
    })) as (UserLeave & { userName?: string; userEmail?: string })[]

    // Get user details for each leave
    const { getUserData } = await import('./dataService')
    for (const leave of leaves) {
      try {
        const userData = await getUserData(leave.userId)
        if (userData) {
          leave.userName = userData.name
          leave.userEmail = userData.email
        }
      } catch (error) {
        console.error(`Error getting user data for ${leave.userId}:`, error)
        leave.userName = 'Unknown User'
        leave.userEmail = '<EMAIL>'
      }
    }

    return leaves
  } catch (error) {
    console.error('Error getting all user leaves:', error)
    throw error
  }
}

export async function updateUserLeaveStatus(
  leaveId: string, 
  status: 'approved' | 'rejected',
  reviewedBy: string,
  reviewNotes?: string
) {
  try {
    await updateDoc(doc(db, COLLECTIONS.userLeaves, leaveId), {
      status,
      reviewedBy,
      reviewedAt: Timestamp.now(),
      reviewNotes: reviewNotes || ''
    })
  } catch (error) {
    console.error('Error updating user leave status:', error)
    throw error
  }
}

export async function cancelUserLeave(leaveId: string) {
  try {
    await deleteDoc(doc(db, COLLECTIONS.userLeaves, leaveId))
  } catch (error) {
    console.error('Error cancelling user leave:', error)
    throw error
  }
}

export async function getUserMonthlyLeaveCount(userId: string, year: number, month: number): Promise<number> {
  try {
    const startOfMonth = new Date(year, month - 1, 1)
    const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999)
    
    const q = query(
      collection(db, COLLECTIONS.userLeaves),
      where('userId', '==', userId),
      where('status', '==', 'approved'),
      where('date', '>=', Timestamp.fromDate(startOfMonth)),
      where('date', '<=', Timestamp.fromDate(endOfMonth))
    )
    
    const querySnapshot = await getDocs(q)
    return querySnapshot.size
  } catch (error) {
    console.error('Error getting user monthly leave count:', error)
    return 0
  }
}

export async function isUserOnLeave(userId: string, date: Date): Promise<boolean> {
  try {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    console.log('🔍 Checking user leave for user:', userId, 'on date range:', startOfDay.toISOString(), 'to', endOfDay.toISOString())

    const q = query(
      collection(db, COLLECTIONS.userLeaves),
      where('userId', '==', userId),
      where('status', '==', 'approved'),
      where('date', '>=', Timestamp.fromDate(startOfDay)),
      where('date', '<=', Timestamp.fromDate(endOfDay))
    )

    const querySnapshot = await getDocs(q)
    const hasUserLeave = !querySnapshot.empty

    if (hasUserLeave) {
      console.log('👤 Found user leave(s) for today:', querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        date: doc.data().date.toDate()
      })))
    } else {
      console.log('👤 No user leaves found for today')
    }

    return hasUserLeave
  } catch (error) {
    console.error('❌ Error checking user leave day:', error)
    // Return false (no leave) on error to avoid blocking work unnecessarily
    return false
  }
}

// Legacy function - now redirects to centralized calculation
// This function is kept for backward compatibility but should not be used for new code
export async function calculateActiveDays(
  userId: string,
  planActivatedDate: Date
): Promise<number> {
  console.warn('⚠️ Using legacy calculateActiveDays function. Please use calculateUserActiveDays from dataService instead.')

  // Import and use the centralized calculation
  const { calculateUserActiveDays } = await import('./dataService')
  return await calculateUserActiveDays(userId)
}

// Check if work/withdrawals should be blocked
export async function isWorkBlocked(userId: string): Promise<{ blocked: boolean; reason?: string }> {
  try {
    const today = new Date()
    console.log('🔍 Checking work block status for user:', userId, 'on date:', today.toDateString())

    // Check admin leave with detailed logging
    try {
      const isAdminLeave = await isAdminLeaveDay(today)
      console.log('📅 Admin leave check result:', isAdminLeave)
      if (isAdminLeave) {
        console.log('🚫 Work blocked due to admin leave')
        return { blocked: true, reason: 'System maintenance/holiday' }
      }
    } catch (adminLeaveError) {
      console.error('❌ Error checking admin leave (allowing work to continue):', adminLeaveError)
      // Don't block work if admin leave check fails
    }

    // Check user leave with detailed logging
    try {
      const isUserLeave = await isUserOnLeave(userId, today)
      console.log('👤 User leave check result:', isUserLeave)
      if (isUserLeave) {
        console.log('🚫 Work blocked due to user leave')
        return { blocked: true, reason: 'You are on approved leave today' }
      }
    } catch (userLeaveError) {
      console.error('❌ Error checking user leave (allowing work to continue):', userLeaveError)
      // Don't block work if user leave check fails
    }

    console.log('✅ Work is not blocked')
    return { blocked: false }
  } catch (error) {
    console.error('❌ Error checking work block status (allowing work to continue):', error)
    return { blocked: false }
  }
}
