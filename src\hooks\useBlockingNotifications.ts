'use client'

import { useState, useEffect } from 'react'
import { hasUnreadNotifications } from '@/lib/dataService'

export function useBlockingNotifications(userId: string | null) {
  const [hasBlockingNotifications, setHasBlockingNotifications] = useState(false)
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    if (userId) {
      checkForBlockingNotifications()
    } else {
      setIsChecking(false)
    }
  }, [userId])

  const checkForBlockingNotifications = async () => {
    try {
      setIsChecking(true)
      const hasBlocking = await hasUnreadNotifications(userId!)
      setHasBlockingNotifications(hasBlocking)
    } catch (error) {
      console.error('Error checking for blocking notifications:', error)
      setHasBlockingNotifications(false)
    } finally {
      setIsChecking(false)
    }
  }

  const markAllAsRead = () => {
    setHasBlockingNotifications(false)
  }

  return {
    hasBlockingNotifications,
    isChecking,
    checkForBlockingNotifications,
    markAllAsRead
  }
}
