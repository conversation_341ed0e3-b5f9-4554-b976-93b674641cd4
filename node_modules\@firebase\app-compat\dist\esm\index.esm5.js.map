{"version": 3, "file": "index.esm5.js", "sources": ["../../src/firebaseApp.ts", "../../src/errors.ts", "../../src/firebaseNamespaceCore.ts", "../../src/firebaseNamespace.ts", "../../src/logger.ts", "../../src/registerCoreComponents.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseOptions } from './public-types';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstantiationMode,\n  Name\n} from '@firebase/component';\nimport {\n  deleteApp,\n  _addComponent,\n  _addOrOverwriteComponent,\n  _DEFAULT_ENTRY_NAME,\n  _FirebaseAppInternal as _FirebaseAppExp\n} from '@firebase/app';\nimport { _FirebaseService, _FirebaseNamespace } from './types';\nimport { Compat } from '@firebase/util';\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport interface _FirebaseApp {\n  /**\n   * The (read-only) name (identifier) for this App. '[DEFAULT]' is the default\n   * App.\n   */\n  name: string;\n\n  /**\n   * The (read-only) configuration options from the app initialization.\n   */\n  options: FirebaseOptions;\n\n  /**\n   * The settable config flag for GDPR opt-in/opt-out\n   */\n  automaticDataCollectionEnabled: boolean;\n\n  /**\n   * Make the given App unusable and free resources.\n   */\n  delete(): Promise<void>;\n}\n/**\n * Global context object for a collection of services using\n * a shared authentication state.\n *\n * marked as internal because it references internal types exported from @firebase/app\n * @internal\n */\nexport class FirebaseAppImpl implements Compat<_FirebaseAppExp>, _FirebaseApp {\n  private container: ComponentContainer;\n\n  constructor(\n    readonly _delegate: _FirebaseAppExp,\n    private readonly firebase: _FirebaseNamespace\n  ) {\n    // add itself to container\n    _addComponent(\n      _delegate,\n      new Component('app-compat', () => this, ComponentType.PUBLIC)\n    );\n\n    this.container = _delegate.container;\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    return this._delegate.automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val) {\n    this._delegate.automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    return this._delegate.name;\n  }\n\n  get options(): FirebaseOptions {\n    return this._delegate.options;\n  }\n\n  delete(): Promise<void> {\n    return new Promise<void>(resolve => {\n      this._delegate.checkDestroyed();\n      resolve();\n    }).then(() => {\n      this.firebase.INTERNAL.removeApp(this.name);\n      return deleteApp(this._delegate);\n    });\n  }\n\n  /**\n   * Return a service instance associated with this app (creating it\n   * on demand), identified by the passed instanceIdentifier.\n   *\n   * NOTE: Currently storage and functions are the only ones that are leveraging this\n   * functionality. They invoke it by calling:\n   *\n   * ```javascript\n   * firebase.app().storage('STORAGE BUCKET ID')\n   * ```\n   *\n   * The service name is passed to this already\n   * @internal\n   */\n  _getService(\n    name: string,\n    instanceIdentifier: string = _DEFAULT_ENTRY_NAME\n  ): _FirebaseService {\n    this._delegate.checkDestroyed();\n\n    // Initialize instance if InstantiationMode is `EXPLICIT`.\n    const provider = this._delegate.container.getProvider(name as Name);\n    if (\n      !provider.isInitialized() &&\n      provider.getComponent()?.instantiationMode === InstantiationMode.EXPLICIT\n    ) {\n      provider.initialize();\n    }\n\n    // getImmediate will always succeed because _getService is only called for registered components.\n    return provider.getImmediate({\n      identifier: instanceIdentifier\n    }) as unknown as _FirebaseService;\n  }\n\n  /**\n   * Remove a service instance from the cache, so we will create a new instance for this service\n   * when people try to get it again.\n   *\n   * NOTE: currently only firestore uses this functionality to support firestore shutdown.\n   *\n   * @param name The service name\n   * @param instanceIdentifier instance identifier in case multiple instances are allowed\n   * @internal\n   */\n  _removeServiceInstance(\n    name: string,\n    instanceIdentifier: string = _DEFAULT_ENTRY_NAME\n  ): void {\n    this._delegate.container\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      .getProvider(name as any)\n      .clearInstance(instanceIdentifier);\n  }\n\n  /**\n   * @param component the component being added to this app's container\n   * @internal\n   */\n  _addComponent(component: Component): void {\n    _addComponent(this._delegate, component);\n  }\n\n  _addOrOverwriteComponent(component: Component): void {\n    _addOrOverwriteComponent(this._delegate, component);\n  }\n\n  toJSON(): object {\n    return {\n      name: this.name,\n      automaticDataCollectionEnabled: this.automaticDataCollectionEnabled,\n      options: this.options\n    };\n  }\n}\n\n// TODO: investigate why the following needs to be commented out\n// Prevent dead-code elimination of these methods w/o invalid property\n// copying.\n// (FirebaseAppImpl.prototype.name && FirebaseAppImpl.prototype.options) ||\n//   FirebaseAppImpl.prototype.delete ||\n//   console.log('dc');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call Firebase App.initializeApp()',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.'\n};\n\ntype ErrorParams = { [key in AppError]: { appName: string } };\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app-compat',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from './public-types';\nimport {\n  _FirebaseNamespace,\n  _FirebaseService,\n  FirebaseServiceNamespace\n} from './types';\nimport * as modularAPIs from '@firebase/app';\nimport { _FirebaseAppInternal as _FirebaseAppExp } from '@firebase/app';\nimport { Component, ComponentType, Name } from '@firebase/component';\n\nimport { deepExtend, contains } from '@firebase/util';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { FirebaseAppLiteImpl } from './lite/firebaseAppLite';\n\n/**\n * Because auth can't share code with other components, we attach the utility functions\n * in an internal namespace to share code.\n * This function return a firebase namespace object without\n * any utility functions, so it can be shared between the regular firebaseNamespace and\n * the lite version.\n */\nexport function createFirebaseNamespaceCore(\n  firebaseAppImpl: typeof FirebaseAppImpl | typeof FirebaseAppLiteImpl\n): _FirebaseNamespace {\n  const apps: { [name: string]: FirebaseApp } = {};\n  // // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  // const components = new Map<string, Component<any>>();\n\n  // A namespace is a plain JavaScript Object.\n  const namespace: _FirebaseNamespace = {\n    // Hack to prevent Babel from modifying the object returned\n    // as the firebase namespace.\n    // @ts-ignore\n    __esModule: true,\n    initializeApp: initializeAppCompat,\n    // @ts-ignore\n    app,\n    registerVersion: modularAPIs.registerVersion,\n    setLogLevel: modularAPIs.setLogLevel,\n    onLog: modularAPIs.onLog,\n    // @ts-ignore\n    apps: null,\n    SDK_VERSION: modularAPIs.SDK_VERSION,\n    INTERNAL: {\n      registerComponent: registerComponentCompat,\n      removeApp,\n      useAsService,\n      modularAPIs\n    }\n  };\n\n  // Inject a circular default export to allow Babel users who were previously\n  // using:\n  //\n  //   import firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase').default;\n  //\n  // instead of\n  //\n  //   import * as firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase');\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (namespace as any)['default'] = namespace;\n\n  // firebase.apps is a read-only getter.\n  Object.defineProperty(namespace, 'apps', {\n    get: getApps\n  });\n\n  /**\n   * Called by App.delete() - but before any services associated with the App\n   * are deleted.\n   */\n  function removeApp(name: string): void {\n    delete apps[name];\n  }\n\n  /**\n   * Get the App object for a given name (or DEFAULT).\n   */\n  function app(name?: string): FirebaseApp {\n    name = name || modularAPIs._DEFAULT_ENTRY_NAME;\n    if (!contains(apps, name)) {\n      throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n    }\n    return apps[name];\n  }\n\n  // @ts-ignore\n  app['App'] = firebaseAppImpl;\n\n  /**\n   * Create a new App instance (name must be unique).\n   *\n   * This function is idempotent. It can be called more than once and return the same instance using the same options and config.\n   */\n  function initializeAppCompat(\n    options: FirebaseOptions,\n    rawConfig = {}\n  ): FirebaseApp {\n    const app = modularAPIs.initializeApp(\n      options,\n      rawConfig\n    ) as _FirebaseAppExp;\n\n    if (contains(apps, app.name)) {\n      return apps[app.name];\n    }\n\n    const appCompat = new firebaseAppImpl(app, namespace);\n    apps[app.name] = appCompat;\n    return appCompat;\n  }\n\n  /*\n   * Return an array of all the non-deleted FirebaseApps.\n   */\n  function getApps(): FirebaseApp[] {\n    // Make a copy so caller cannot mutate the apps list.\n    return Object.keys(apps).map(name => apps[name]);\n  }\n\n  function registerComponentCompat<T extends Name>(\n    component: Component<T>\n  ): FirebaseServiceNamespace<_FirebaseService> | null {\n    const componentName = component.name;\n    const componentNameWithoutCompat = componentName.replace('-compat', '');\n    if (\n      modularAPIs._registerComponent(component) &&\n      component.type === ComponentType.PUBLIC\n    ) {\n      // create service namespace for public components\n      // The Service namespace is an accessor function ...\n      const serviceNamespace = (\n        appArg: FirebaseApp = app()\n      ): _FirebaseService => {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (typeof (appArg as any)[componentNameWithoutCompat] !== 'function') {\n          // Invalid argument.\n          // This happens in the following case: firebase.storage('gs:/')\n          throw ERROR_FACTORY.create(AppError.INVALID_APP_ARGUMENT, {\n            appName: componentName\n          });\n        }\n\n        // Forward service instance lookup to the FirebaseApp.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return (appArg as any)[componentNameWithoutCompat]();\n      };\n\n      // ... and a container for service-level properties.\n      if (component.serviceProps !== undefined) {\n        deepExtend(serviceNamespace, component.serviceProps);\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (namespace as any)[componentNameWithoutCompat] = serviceNamespace;\n\n      // Patch the FirebaseAppImpl prototype\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (firebaseAppImpl.prototype as any)[componentNameWithoutCompat] =\n        // TODO: The eslint disable can be removed and the 'ignoreRestArgs'\n        // option added to the no-explicit-any rule when ESlint releases it.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        function (...args: any) {\n          const serviceFxn = this._getService.bind(this, componentName);\n          return serviceFxn.apply(\n            this,\n            component.multipleInstances ? args : []\n          );\n        };\n    }\n\n    return component.type === ComponentType.PUBLIC\n      ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (namespace as any)[componentNameWithoutCompat]\n      : null;\n  }\n\n  // Map the requested service to a registered service name\n  // (used to map auth to serverAuth service when needed).\n  function useAsService(app: FirebaseApp, name: string): string | null {\n    if (name === 'serverAuth') {\n      return null;\n    }\n\n    const useService = name;\n\n    return useService;\n  }\n\n  return namespace;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseNamespace } from './public-types';\nimport { createSubscribe, deepExtend, ErrorFactory } from '@firebase/util';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { createFirebaseNamespaceCore } from './firebaseNamespaceCore';\n\n/**\n * Return a firebase namespace object.\n *\n * In production, this will be called exactly once and the result\n * assigned to the 'firebase' global.  It may be called multiple times\n * in unit tests.\n */\nexport function createFirebaseNamespace(): FirebaseNamespace {\n  const namespace = createFirebaseNamespaceCore(FirebaseAppImpl);\n  namespace.INTERNAL = {\n    ...namespace.INTERNAL,\n    createFirebaseNamespace,\n    extendNamespace,\n    createSubscribe,\n    ErrorFactory,\n    deepExtend\n  };\n\n  /**\n   * Patch the top-level firebase namespace with additional properties.\n   *\n   * firebase.INTERNAL.extendNamespace()\n   */\n  function extendNamespace(props: { [prop: string]: unknown }): void {\n    deepExtend(namespace, props);\n  }\n\n  return namespace;\n}\n\nexport const firebase = createFirebaseNamespace();\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app-compat');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion } from '@firebase/app';\n\nimport { name, version } from '../package.json';\n\nexport function registerCoreComponents(variant?: string): void {\n  // Register `app` package.\n  registerVersion(name, version, variant);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseNamespace } from './public-types';\nimport { getGlobal } from '@firebase/util';\nimport { firebase as firebaseNamespace } from './firebaseNamespace';\nimport { logger } from './logger';\nimport { registerCoreComponents } from './registerCoreComponents';\n\ndeclare global {\n  interface Window {\n    firebase: FirebaseNamespace;\n  }\n}\n\ntry {\n  const globals = getGlobal();\n  // Firebase Lite detection\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if ((globals as any).firebase !== undefined) {\n    logger.warn(`\n      Warning: Firebase is already defined in the global scope. Please make sure\n      Firebase library is only loaded once.\n    `);\n\n    // eslint-disable-next-line\n    const sdkVersion = ((globals as any).firebase as FirebaseNamespace)\n      .SDK_VERSION;\n    if (sdkVersion && sdkVersion.indexOf('LITE') >= 0) {\n      logger.warn(`\n        Warning: You are trying to load Firebase while using Firebase Performance standalone script.\n        You should load Firebase Performance with this instance of Firebase to avoid loading duplicate code.\n        `);\n    }\n  }\n} catch {\n  // ignore errors thrown by getGlobal\n}\n\nconst firebase = firebaseNamespace;\n\nregisterCoreComponents();\n\n// eslint-disable-next-line import/no-default-export\nexport default firebase;\n\nexport { _FirebaseNamespace, _FirebaseService } from './types';\nexport { FirebaseApp, FirebaseNamespace } from './public-types';\n"], "names": ["firebase", "firebaseNamespace"], "mappings": ";;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AA2CH;;;;;;AAMG;AACH,IAAA,eAAA,kBAAA,YAAA;IAGE,SACW,eAAA,CAAA,SAA0B,EAClB,QAA4B,EAAA;QAF/C,IAWC,KAAA,GAAA,IAAA,CAAA;QAVU,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiB;QAClB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAoB;;AAG7C,QAAA,aAAa,CACX,SAAS,EACT,IAAI,SAAS,CAAC,YAAY,EAAE,YAAA,EAAM,OAAA,KAAI,CAAA,EAAA,EAAA,QAAA,4BAAuB,CAC9D,CAAC;AAEF,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;KACtC;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,eAA8B,CAAA,SAAA,EAAA,gCAAA,EAAA;AAAlC,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,8BAA8B,CAAC;SACtD;AAED,QAAA,GAAA,EAAA,UAAmC,GAAG,EAAA;AACpC,YAAA,IAAI,CAAC,SAAS,CAAC,8BAA8B,GAAG,GAAG,CAAC;SACrD;;;AAJA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,eAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAAR,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;SAC5B;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,eAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAAX,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;SAC/B;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,eAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QAAA,IAQC,KAAA,GAAA,IAAA,CAAA;AAPC,QAAA,OAAO,IAAI,OAAO,CAAO,UAAA,OAAO,EAAA;AAC9B,YAAA,KAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;AAChC,YAAA,OAAO,EAAE,CAAC;SACX,CAAC,CAAC,IAAI,CAAC,YAAA;YACN,KAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC;AAC5C,YAAA,OAAO,SAAS,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC;AACnC,SAAC,CAAC,CAAC;KACJ,CAAA;AAED;;;;;;;;;;;;;AAaG;AACH,IAAA,eAAA,CAAA,SAAA,CAAA,WAAW,GAAX,UACE,IAAY,EACZ,kBAAgD,EAAA;;AAAhD,QAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,kBAAgD,GAAA,mBAAA,CAAA,EAAA;AAEhD,QAAA,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;;AAGhC,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,IAAY,CAAC,CAAC;AACpE,QAAA,IACE,CAAC,QAAQ,CAAC,aAAa,EAAE;YACzB,CAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,YAAY,EAAE,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAiB,mDAC1C;YACA,QAAQ,CAAC,UAAU,EAAE,CAAC;AACvB,SAAA;;QAGD,OAAO,QAAQ,CAAC,YAAY,CAAC;AAC3B,YAAA,UAAU,EAAE,kBAAkB;AAC/B,SAAA,CAAgC,CAAC;KACnC,CAAA;AAED;;;;;;;;;AASG;AACH,IAAA,eAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtB,UACE,IAAY,EACZ,kBAAgD,EAAA;AAAhD,QAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,kBAAgD,GAAA,mBAAA,CAAA,EAAA;QAEhD,IAAI,CAAC,SAAS,CAAC,SAAS;;aAErB,WAAW,CAAC,IAAW,CAAC;aACxB,aAAa,CAAC,kBAAkB,CAAC,CAAC;KACtC,CAAA;AAED;;;AAGG;IACH,eAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,SAAoB,EAAA;AAChC,QAAA,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;KAC1C,CAAA;IAED,eAAwB,CAAA,SAAA,CAAA,wBAAA,GAAxB,UAAyB,SAAoB,EAAA;AAC3C,QAAA,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;KACrD,CAAA;AAED,IAAA,eAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QACE,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,8BAA8B,EAAE,IAAI,CAAC,8BAA8B;YACnE,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;KACH,CAAA;IACH,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;;AC5LA;;;;;;;;;;;;;;;AAeG;;AASH,IAAM,MAAM,IAAA,EAAA,GAAA,EAAA;AACV,IAAA,EAAA,CAAA,QAAA,uBAAA,GACE,kDAAkD;QAClD,mCAAmC;AACrC,IAAA,EAAA,CAAA,sBAAA,qCAAA,GACE,sDAAsD;QACtD,wBAAwB;OAC3B,CAAC;AAIK,IAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,YAAY,EACZ,UAAU,EACV,MAAM,CACP;;ACvCD;;;;;;;;;;;;;;;AAeG;AAiBH;;;;;;AAMG;AACG,SAAU,2BAA2B,CACzC,eAAoE,EAAA;IAEpE,IAAM,IAAI,GAAoC,EAAE,CAAC;;;;AAKjD,IAAA,IAAM,SAAS,GAAuB;;;;AAIpC,QAAA,UAAU,EAAE,IAAI;AAChB,QAAA,aAAa,EAAE,mBAAmB;;AAElC,QAAA,GAAG,EAAA,GAAA;QACH,eAAe,EAAE,WAAW,CAAC,eAAe;QAC5C,WAAW,EAAE,WAAW,CAAC,WAAW;QACpC,KAAK,EAAE,WAAW,CAAC,KAAK;;AAExB,QAAA,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,WAAW,CAAC,WAAW;AACpC,QAAA,QAAQ,EAAE;AACR,YAAA,iBAAiB,EAAE,uBAAuB;AAC1C,YAAA,SAAS,EAAA,SAAA;AACT,YAAA,YAAY,EAAA,YAAA;AACZ,YAAA,WAAW,EAAA,WAAA;AACZ,SAAA;KACF,CAAC;;;;;;;;;;;;AAaD,IAAA,SAAiB,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;;AAG1C,IAAA,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE;AACvC,QAAA,GAAG,EAAE,OAAO;AACb,KAAA,CAAC,CAAC;AAEH;;;AAGG;IACH,SAAS,SAAS,CAAC,IAAY,EAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;KACnB;AAED;;AAEG;IACH,SAAS,GAAG,CAAC,IAAa,EAAA;AACxB,QAAA,IAAI,GAAG,IAAI,IAAI,WAAW,CAAC,mBAAmB,CAAC;AAC/C,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;YACzB,MAAM,aAAa,CAAC,MAAM,CAAkB,QAAA,wBAAA,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAChE,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;KACnB;;AAGD,IAAA,GAAG,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC;AAE7B;;;;AAIG;AACH,IAAA,SAAS,mBAAmB,CAC1B,OAAwB,EACxB,SAAc,EAAA;AAAd,QAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAc,GAAA,EAAA,CAAA,EAAA;QAEd,IAAM,GAAG,GAAG,WAAW,CAAC,aAAa,CACnC,OAAO,EACP,SAAS,CACS,CAAC;QAErB,IAAI,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvB,SAAA;QAED,IAAM,SAAS,GAAG,IAAI,eAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;AAC3B,QAAA,OAAO,SAAS,CAAC;KAClB;AAED;;AAEG;AACH,IAAA,SAAS,OAAO,GAAA;;QAEd,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI,EAAI,EAAA,OAAA,IAAI,CAAC,IAAI,CAAC,CAAV,EAAU,CAAC,CAAC;KAClD;IAED,SAAS,uBAAuB,CAC9B,SAAuB,EAAA;AAEvB,QAAA,IAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC;QACrC,IAAM,0BAA0B,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AACxE,QAAA,IACE,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC;YACzC,SAAS,CAAC,IAAI,KAAA,QAAA,6BACd;;;YAGA,IAAM,gBAAgB,GAAG,UACvB,MAA2B,EAAA;gBAA3B,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAsB,GAAA,GAAG,EAAE,CAAA,EAAA;;AAG3B,gBAAA,IAAI,OAAQ,MAAc,CAAC,0BAA0B,CAAC,KAAK,UAAU,EAAE;;;oBAGrE,MAAM,aAAa,CAAC,MAAM,CAAgC,sBAAA,sCAAA;AACxD,wBAAA,OAAO,EAAE,aAAa;AACvB,qBAAA,CAAC,CAAC;AACJ,iBAAA;;;AAID,gBAAA,OAAQ,MAAc,CAAC,0BAA0B,CAAC,EAAE,CAAC;AACvD,aAAC,CAAC;;AAGF,YAAA,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS,EAAE;AACxC,gBAAA,UAAU,CAAC,gBAAgB,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;AACtD,aAAA;;AAGA,YAAA,SAAiB,CAAC,0BAA0B,CAAC,GAAG,gBAAgB,CAAC;;;AAIjE,YAAA,eAAe,CAAC,SAAiB,CAAC,0BAA0B,CAAC;;;;AAI5D,gBAAA,YAAA;oBAAU,IAAY,IAAA,GAAA,EAAA,CAAA;yBAAZ,IAAY,EAAA,GAAA,CAAA,EAAZ,EAAY,GAAA,SAAA,CAAA,MAAA,EAAZ,EAAY,EAAA,EAAA;wBAAZ,IAAY,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;AACpB,oBAAA,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC9D,oBAAA,OAAO,UAAU,CAAC,KAAK,CACrB,IAAI,EACJ,SAAS,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CACxC,CAAC;AACJ,iBAAC,CAAC;AACL,SAAA;QAED,OAAO,SAAS,CAAC,IAAI,KAAyB,QAAA;AAC5C;gBACG,SAAiB,CAAC,0BAA0B,CAAC;cAC9C,IAAI,CAAC;KACV;;;AAID,IAAA,SAAS,YAAY,CAAC,GAAgB,EAAE,IAAY,EAAA;QAClD,IAAI,IAAI,KAAK,YAAY,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QAED,IAAM,UAAU,GAAG,IAAI,CAAC;AAExB,QAAA,OAAO,UAAU,CAAC;KACnB;AAED,IAAA,OAAO,SAAS,CAAC;AACnB;;AClNA;;;;;;;;;;;;;;;AAeG;AAOH;;;;;;AAMG;SACa,uBAAuB,GAAA;AACrC,IAAA,IAAM,SAAS,GAAG,2BAA2B,CAAC,eAAe,CAAC,CAAC;IAC/D,SAAS,CAAC,QAAQ,GACb,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,SAAS,CAAC,QAAQ,CAAA,EAAA,EACrB,uBAAuB,EAAA,uBAAA,EACvB,eAAe,iBAAA,EACf,eAAe,iBAAA,EACf,YAAY,cAAA,EACZ,UAAU,EAAA,UAAA,EAAA,CACX,CAAC;AAEF;;;;AAIG;IACH,SAAS,eAAe,CAAC,KAAkC,EAAA;AACzD,QAAA,UAAU,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;KAC9B;AAED,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEM,IAAMA,UAAQ,GAAG,uBAAuB,EAAE;;ACpDjD;;;;;;;;;;;;;;;AAeG;AAII,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,sBAAsB,CAAC;;;;;ACnBxD;;;;;;;;;;;;;;;AAeG;AAMG,SAAU,sBAAsB,CAAC,OAAgB,EAAA;;AAErD,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC1C;;ACxBA;;;;;;;;;;;;;;;AAeG;AAcH,IAAI;AACF,IAAA,IAAM,OAAO,GAAG,SAAS,EAAE,CAAC;;;AAG5B,IAAA,IAAK,OAAe,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC3C,QAAA,MAAM,CAAC,IAAI,CAAC,uIAGX,CAAC,CAAC;;AAGH,QAAA,IAAM,UAAU,GAAK,OAAe,CAAC,QAA8B;AAChE,aAAA,WAAW,CAAC;QACf,IAAI,UAAU,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AACjD,YAAA,MAAM,CAAC,IAAI,CAAC,gOAGT,CAAC,CAAC;AACN,SAAA;AACF,KAAA;AACF,CAAA;AAAC,OAAM,EAAA,EAAA;;AAEP,CAAA;AAEK,IAAA,QAAQ,GAAGC,WAAkB;AAEnC,sBAAsB,EAAE;;;;"}