'use client'

import { useState } from 'react'
import { signInWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import Swal from 'sweetalert2'

export default function FixAdminPermissionsPage() {
  const [isFixing, setIsFixing] = useState(false)
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('123456')

  const fixAdminPermissions = async () => {
    if (!email || !password) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Please enter admin email and password',
      })
      return
    }

    setIsFixing(true)

    try {
      // Sign in as admin
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      const user = userCredential.user

      // Create admin document in admins collection
      const adminDoc = {
        email: user.email,
        name: '<PERSON><PERSON><PERSON> Admin',
        role: 'super_admin',
        permissions: ['all'],
        createdAt: Timestamp.now(),
        isActive: true,
        uid: user.uid
      }

      await setDoc(doc(db, 'admins', user.uid), adminDoc)

      Swal.fire({
        icon: 'success',
        title: 'Permissions Fixed!',
        text: 'Admin permissions have been successfully fixed. You can now perform admin actions.',
        confirmButtonText: 'Go to Admin Dashboard'
      }).then(() => {
        window.location.href = '/admin'
      })

    } catch (error: any) {
      console.error('Error fixing admin permissions:', error)
      
      let message = 'Failed to fix admin permissions'
      
      switch (error.code) {
        case 'auth/user-not-found':
          message = 'Admin account not found'
          break
        case 'auth/wrong-password':
          message = 'Incorrect password'
          break
        case 'auth/invalid-email':
          message = 'Invalid email address'
          break
        default:
          message = error.message || 'Failed to fix admin permissions'
      }

      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: message,
      })
    } finally {
      setIsFixing(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="glass-card p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Fix Admin Permissions</h1>
          <p className="text-white/70">
            This will create the required admin document in Firestore to fix permission issues.
          </p>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              Admin Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="input-field"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <label className="block text-white/80 text-sm font-medium mb-2">
              Admin Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="input-field"
              placeholder="Enter admin password"
              required
            />
          </div>

          <button
            onClick={fixAdminPermissions}
            disabled={isFixing}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-all ${
              isFixing
                ? 'bg-gray-600 cursor-not-allowed'
                : 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700'
            } text-white`}
          >
            {isFixing ? (
              <>
                <div className="spinner mr-2 w-5 h-5"></div>
                Fixing Permissions...
              </>
            ) : (
              'Fix Admin Permissions'
            )}
          </button>

          <div className="text-center">
            <a
              href="/admin/login"
              className="text-blue-400 hover:text-blue-300 text-sm"
            >
              Back to Admin Login
            </a>
          </div>
        </div>

        <div className="mt-8 p-4 bg-yellow-500/20 border border-yellow-400/30 rounded-lg">
          <div className="flex items-start">
            <i className="fas fa-exclamation-triangle text-yellow-400 mr-2 mt-1"></i>
            <div className="text-yellow-200 text-sm">
              <p className="font-medium mb-1">Important:</p>
              <p>This page fixes the admin permissions issue by creating the required admin document in Firestore. Run this once if you're getting permission errors.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
