'use client'

import { useState } from 'react'
import { auth, db } from '@/lib/firebase'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc } from 'firebase/firestore'

export default function TestFirebaseConnectionPage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const testFirebaseConnection = async () => {
    setResult('')
    setIsLoading(true)

    try {
      setResult('🔍 Testing Complete Registration Flow...\n')
      setResult(prev => prev + `Environment: ${window.location.origin}\n`)
      setResult(prev => prev + `Project ID: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}\n`)
      setResult(prev => prev + `Auth Domain: ${process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}\n\n`)

      // Test 1: Firebase Auth User Creation (like registration)
      setResult(prev => prev + '📡 Test 1: Firebase Auth User Creation\n')
      const testEmail = `regtest${Date.now()}@example.com`
      const testPassword = 'regtest123456'

      try {
        setResult(prev => prev + `Creating user: ${testEmail}\n`)
        const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
        const user = userCredential.user
        setResult(prev => prev + `✅ Auth user created: ${user.uid}\n`)
        setResult(prev => prev + `   Email: ${user.email}\n`)
        setResult(prev => prev + `   Email verified: ${user.emailVerified}\n`)

        // Wait for auth to propagate
        await new Promise(resolve => setTimeout(resolve, 1000))
        setResult(prev => prev + `   Auth state: ${auth.currentUser ? 'authenticated' : 'not authenticated'}\n`)

        // Test 2: Create user document exactly like registration
        setResult(prev => prev + '\n📡 Test 2: Creating User Document (Registration Style)\n')

        // Import the exact field names and collections
        const { FIELD_NAMES, COLLECTIONS } = await import('@/lib/dataService')

        // Generate referral code exactly like registration
        const timestamp = Date.now().toString().slice(-4)
        const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase()
        const userReferralCode = `MY${timestamp}${randomPart}`
        setResult(prev => prev + `Generated referral code: ${userReferralCode}\n`)

        // Create user data exactly like registration
        const userData = {
          [FIELD_NAMES.name]: 'Registration Test User',
          [FIELD_NAMES.email]: testEmail.toLowerCase(),
          [FIELD_NAMES.mobile]: '9876543210',
          [FIELD_NAMES.referralCode]: userReferralCode,
          [FIELD_NAMES.referredBy]: '',
          [FIELD_NAMES.referralBonusCredited]: false,
          [FIELD_NAMES.plan]: 'Trial',
          [FIELD_NAMES.planExpiry]: null,
          [FIELD_NAMES.activeDays]: 0,
          [FIELD_NAMES.joinedDate]: new Date(),
          [FIELD_NAMES.wallet]: 0,
          [FIELD_NAMES.totalVideos]: 0,
          [FIELD_NAMES.todayVideos]: 0,
          [FIELD_NAMES.lastVideoDate]: null,
          [FIELD_NAMES.videoDuration]: 30,
          status: 'active'
        }

        setResult(prev => prev + `Document path: ${COLLECTIONS.users}/${user.uid}\n`)
        setResult(prev => prev + `Field count: ${Object.keys(userData).length}\n`)
        setResult(prev => prev + `Current auth UID: ${auth.currentUser?.uid}\n`)
        setResult(prev => prev + `Target UID: ${user.uid}\n`)
        setResult(prev => prev + `UIDs match: ${auth.currentUser?.uid === user.uid}\n`)

        const userDocRef = doc(db, COLLECTIONS.users, user.uid)

        try {
          setResult(prev => prev + '\nAttempting setDoc...\n')
          await setDoc(userDocRef, userData)
          setResult(prev => prev + '✅ User document created successfully!\n')

          // Verify document
          const verifyDoc = await getDoc(userDocRef)
          if (verifyDoc.exists()) {
            const docData = verifyDoc.data()
            setResult(prev => prev + '✅ Document verification successful\n')
            setResult(prev => prev + `   Name: ${docData[FIELD_NAMES.name]}\n`)
            setResult(prev => prev + `   Email: ${docData[FIELD_NAMES.email]}\n`)
            setResult(prev => prev + `   Plan: ${docData[FIELD_NAMES.plan]}\n`)
            setResult(prev => prev + `   Referral Code: ${docData[FIELD_NAMES.referralCode]}\n`)

            setResult(prev => prev + '\n🎉 REGISTRATION TEST SUCCESSFUL!\n')
            setResult(prev => prev + 'The registration flow works perfectly.\n')
            setResult(prev => prev + 'If registration is failing, check for:\n')
            setResult(prev => prev + '- Form validation errors\n')
            setResult(prev => prev + '- Network connectivity issues\n')
            setResult(prev => prev + '- Browser console errors\n')
          } else {
            setResult(prev => prev + '❌ Document verification failed\n')
          }

        } catch (setDocError: any) {
          setResult(prev => prev + `❌ setDoc failed: ${setDocError.message}\n`)
          setResult(prev => prev + `   Error code: ${setDocError.code}\n`)
          setResult(prev => prev + `   Full error: ${JSON.stringify(setDocError, null, 2)}\n`)

          if (setDocError.code === 'permission-denied') {
            setResult(prev => prev + '\n🔧 PERMISSION ISSUE DETECTED:\n')
            setResult(prev => prev + '   - Check Firestore security rules\n')
            setResult(prev => prev + '   - Ensure rules allow authenticated users to write their own documents\n')
            setResult(prev => prev + '   - Verify the user is properly authenticated\n')
          }
        }

        // Clean up
        try {
          await user.delete()
          setResult(prev => prev + '✅ Test user deleted\n')
        } catch (deleteError: any) {
          setResult(prev => prev + `⚠️ User deletion failed: ${deleteError.message}\n`)
        }

      } catch (authError: any) {
        setResult(prev => prev + `❌ Auth user creation failed: ${authError.message}\n`)
        setResult(prev => prev + `   Code: ${authError.code}\n`)

        if (authError.code === 'auth/network-request-failed') {
          setResult(prev => prev + '\n🔧 NETWORK ISSUE DETECTED:\n')
          setResult(prev => prev + '   - Check your internet connection\n')
          setResult(prev => prev + '   - Try disabling VPN/proxy\n')
          setResult(prev => prev + '   - Check if firewall is blocking Firebase\n')
          setResult(prev => prev + '   - Try testing on a different network\n')
        }
      }

    } catch (error: any) {
      setResult(prev => prev + `❌ Test failed: ${error.message}\n`)
      setResult(prev => prev + `   Code: ${error.code}\n`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Registration Flow Test</h1>

        <div className="glass-card p-6 mb-6">
          <button
            onClick={testFirebaseConnection}
            disabled={isLoading}
            className="btn-primary mb-4"
          >
            {isLoading ? 'Testing Registration...' : 'Test Complete Registration Flow'}
          </button>
          
          <div className="bg-black/50 p-4 rounded-lg">
            <pre className="text-white text-sm whitespace-pre-wrap">
              {result || 'Click "Test Complete Registration Flow" to start...'}
            </pre>
          </div>
        </div>
        
        <div className="text-center">
          <a href="/register" className="text-blue-400 hover:text-blue-300 underline">
            ← Back to Registration
          </a>
        </div>
      </div>
    </div>
  )
}
