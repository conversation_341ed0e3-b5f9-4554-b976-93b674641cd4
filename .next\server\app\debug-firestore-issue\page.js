(()=>{var e={};e.id=2848,e.ids=[2848],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7604:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),i=r(43210),o=r(63385),a=r(75535),n=r(33784),u=r(3582);function d(){let[e,t]=(0,i.useState)(""),[r,d]=(0,i.useState)(!1),l=e=>{t(t=>t+e+"\n")},c=async()=>{t(""),d(!0);let e=null;try{l("\uD83D\uDD0D Debugging Firestore Document Creation Issue...\n"),l("=== STEP 1: Firebase Configuration Test ==="),l(`Auth instance: ${n.j2?"✅ Initialized":"❌ Not initialized"}`),l(`Firestore instance: ${n.db?"✅ Initialized":"❌ Not initialized"}`),l(`Current user: ${n.j2.currentUser?.uid||"None"}`),l("\n=== STEP 2: Basic Firestore Write Test ===");try{let e=(0,a.H9)(n.db,"test_collection",`test_${Date.now()}`);await (0,a.BN)(e,{test:!0,timestamp:a.Dc.now(),message:"Basic write test"}),l("✅ Basic Firestore write works"),(await (0,a.x7)(e)).exists()?l("✅ Basic Firestore read works"):l("❌ Basic Firestore read failed")}catch(e){l(`❌ Basic Firestore write failed: ${e.message}`),l(`   Error code: ${e.code}`)}l("\n=== STEP 3: Firebase Auth Test ===");let t=`debug${Date.now()}@test.com`;try{e=(await (0,o.eJ)(n.j2,t,"debug123456")).user,l(`✅ Auth user created: ${e.uid}`),l(`   Email: ${e.email}`),l(`   Email verified: ${e.emailVerified}`),await new Promise(e=>setTimeout(e,1e3)),l(`   Current auth user: ${n.j2.currentUser?.uid}`),l(`   Auth state matches: ${n.j2.currentUser?.uid===e.uid}`)}catch(e){l(`❌ Auth creation failed: ${e.message}`),l(`   Error code: ${e.code}`);return}l("\n=== STEP 4: Users Collection Write Test ===");try{let r=(0,a.H9)(n.db,u.COLLECTIONS.users,e.uid);l(`   Document path: ${r.path}`);let s={name:"Debug Test User",email:t,mobile:"9876543210",plan:"Trial",joinedDate:a.Dc.now(),status:"active"};l("   Attempting minimal user document creation..."),await (0,a.BN)(r,s),l("✅ Minimal user document created");let i=await (0,a.x7)(r);if(i.exists()){l("✅ User document verification successful");let e=i.data();l(`   Name: ${e.name}`),l(`   Email: ${e.email}`),l(`   Plan: ${e.plan}`)}else l("❌ User document verification failed")}catch(e){l(`❌ User document creation failed: ${e.message}`),l(`   Error code: ${e.code}`),l(`   Error details: ${JSON.stringify(e,null,2)}`),"permission-denied"===e.code&&(l("\n\uD83D\uDD27 PERMISSION DENIED ANALYSIS:"),l("   This indicates Firestore security rules are blocking the write"),l("   Possible causes:"),l("   1. Rules require authentication but user is not properly authenticated"),l("   2. Rules have specific conditions that are not met"),l("   3. Rules are too restrictive for user document creation"))}l("\n=== STEP 5: Full Registration Data Test ===");let r={[u.FIELD_NAMES.name]:"Debug Full Test User",[u.FIELD_NAMES.email]:t.toLowerCase(),[u.FIELD_NAMES.mobile]:"9876543210",[u.FIELD_NAMES.referralCode]:`MY${Date.now().toString().slice(-4)}AB`,[u.FIELD_NAMES.referredBy]:"",[u.FIELD_NAMES.referralBonusCredited]:!1,[u.FIELD_NAMES.plan]:"Trial",[u.FIELD_NAMES.planExpiry]:null,[u.FIELD_NAMES.activeDays]:0,[u.FIELD_NAMES.joinedDate]:a.Dc.now(),[u.FIELD_NAMES.wallet]:0,[u.FIELD_NAMES.totalVideos]:0,[u.FIELD_NAMES.todayVideos]:0,[u.FIELD_NAMES.lastVideoDate]:null,[u.FIELD_NAMES.videoDuration]:30,status:"active"};try{let t=(0,a.H9)(n.db,u.COLLECTIONS.users,`${e.uid}_full`);l("   Attempting full registration data creation..."),await (0,a.BN)(t,r),l("✅ Full registration data document created");let s=await (0,a.x7)(t);if(s.exists()){l("✅ Full document verification successful");let e=s.data();l(`   Fields count: ${Object.keys(e).length}`),l(`   Referral code: ${e[u.FIELD_NAMES.referralCode]}`),l(`   Wallet: ${e[u.FIELD_NAMES.wallet]}`)}}catch(t){l(`❌ Full registration data creation failed: ${t.message}`),l(`   Error code: ${t.code}`),l("\n   Testing individual fields...");let e=[];for(let[t,s]of Object.entries(r))try{let e=(0,a.H9)(n.db,"test_fields",`field_${t}_${Date.now()}`);await (0,a.BN)(e,{[t]:s})}catch(r){e.push(`${t}: ${r.message}`)}e.length>0?l(`   Problematic fields: ${e.join(", ")}`):l("   All individual fields work fine")}l("\n=== STEP 6: Alternative Collection Test ===");try{let r=(0,a.collection)(n.db,"debug_users"),s=await (0,a.gS)(r,{name:"Alternative Test",email:t,createdAt:a.Dc.now(),userId:e.uid});l(`✅ Alternative collection write works: ${s.id}`)}catch(e){l(`❌ Alternative collection failed: ${e.message}`)}l("\n=== STEP 7: Summary and Recommendations ==="),l("Based on the test results above:"),l(""),l("If basic Firestore write works but user document fails:"),l("  → Check Firestore security rules for users collection"),l("  → Verify authentication state is properly propagated"),l(""),l("If permission denied errors occur:"),l("  → Review firestore.rules file"),l("  → Ensure rules allow authenticated users to create their own documents"),l(""),l("If specific fields cause issues:"),l("  → Check for reserved field names or invalid data types"),l("  → Verify FIELD_NAMES constants are correct")}catch(e){l(`❌ Debug test failed: ${e.message}`),l(`   Error code: ${e.code}`)}finally{if(e)try{await (0,o.hG)(e),l("\n✅ Test user cleaned up")}catch(e){l(`
⚠️ Cleanup failed: ${e.message}`)}try{await (0,o.CI)(n.j2),l("✅ Signed out")}catch(e){l(`⚠️ Sign out failed: ${e.message}`)}d(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Debug Firestore Document Creation Issue"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("button",{onClick:c,disabled:r,className:"btn-primary mb-4",children:r?"Running Diagnostic...":"Run Firestore Diagnostic"}),(0,s.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,s.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap overflow-auto max-h-96",children:e||'Click "Run Firestore Diagnostic" to start...'})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},25726:(e,t,r)=>{Promise.resolve().then(r.bind(r,97634))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>d,j2:()=>u});var s=r(67989),i=r(63385),o=r(75535),a=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),u=(0,i.xI)(n),d=(0,o.aU)(n);(0,a.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72893:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=r(65239),i=r(48088),o=r(88170),a=r.n(o),n=r(30893),u={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>n[e]);r.d(t,u);let d={children:["",{children:["debug-firestore-issue",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,97634)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-firestore-issue\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-firestore-issue\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/debug-firestore-issue/page",pathname:"/debug-firestore-issue",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94182:(e,t,r)=>{Promise.resolve().then(r.bind(r,7604))},94735:e=>{"use strict";e.exports=require("events")},97634:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-firestore-issue\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-firestore-issue\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,8441,3582],()=>r(72893));module.exports=s})();