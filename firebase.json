{"functions": {"source": "functions", "runtime": "nodejs18", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, "hosting": {"public": "out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "!**/*.@(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|json|txt|xml)", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=300"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "**/*.@(woff|woff2|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "**/*.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}], "cleanUrls": true, "trailingSlash": false}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}}