/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { AdditionalUserInfo, UserCredential } from '../../model/public_types';
import { IdTokenResponse } from '../../model/id_token';
/**
 * Parse the `AdditionalUserInfo` from the ID token response.
 *
 */
export declare function _fromIdTokenResponse(idTokenResponse?: IdTokenResponse): AdditionalUserInfo | null;
/**
 * Extracts provider specific {@link AdditionalUserInfo} for the given credential.
 *
 * @param userCredential - The user credential.
 *
 * @public
 */
export declare function getAdditionalUserInfo(userCredential: UserCredential): AdditionalUserInfo | null;
