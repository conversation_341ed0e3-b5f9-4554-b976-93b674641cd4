'use client'

import { useState } from 'react'
import { usePWAInstall } from '@/hooks/usePWAInstall'
import Swal from 'sweetalert2'

interface InstallAppProps {
  variant?: 'homepage' | 'dashboard'
  className?: string
}

export default function InstallApp({ variant = 'homepage', className = '' }: InstallAppProps) {
  const { isInstallable, isInstalled, installApp, getInstallInstructions } = usePWAInstall()
  const [showInstructions, setShowInstructions] = useState(false)

  const handleInstall = async () => {
    const success = await installApp()
    
    if (success) {
      Swal.fire({
        icon: 'success',
        title: 'App Installed!',
        text: 'MyTube has been installed on your device. You can now access it from your home screen.',
        timer: 3000,
        showConfirmButton: false
      })
    } else if (!isInstallable) {
      setShowInstructions(true)
    }
  }

  const handleShowInstructions = () => {
    const instructions = getInstallInstructions()
    
    Swal.fire({
      title: `Install MyTube on ${instructions.browser}`,
      html: `
        <div class="text-left">
          <p class="mb-4 text-gray-600">Follow these steps to install MyTube as an app:</p>
          <ol class="list-decimal list-inside space-y-2">
            ${instructions.steps.map(step => `<li class="text-gray-700">${step}</li>`).join('')}
          </ol>
          <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <p class="text-sm text-blue-800">
              <i class="fas fa-info-circle mr-2"></i>
              Installing the app gives you faster access, offline capabilities, and a native app experience!
            </p>
          </div>
        </div>
      `,
      confirmButtonText: 'Got it!',
      confirmButtonColor: '#3b82f6'
    })
  }

  if (isInstalled) {
    return (
      <div className={`${className}`}>
        {variant === 'homepage' ? (
          <div className="glass-card p-6 text-center">
            <i className="fas fa-check-circle text-4xl text-green-400 mb-4"></i>
            <h3 className="text-xl font-bold text-white mb-2">App Installed!</h3>
            <p className="text-white/80">MyTube is installed on your device</p>
          </div>
        ) : (
          <div className="flex items-center text-green-400">
            <i className="fas fa-check-circle mr-2"></i>
            <span className="text-sm">App Installed</span>
          </div>
        )}
      </div>
    )
  }

  if (variant === 'homepage') {
    return (
      <div className={`glass-card p-8 hover:scale-105 transition-transform ${className}`}>
        <i className="fas fa-mobile-alt text-5xl text-purple-400 mb-4"></i>
        <h3 className="text-xl font-bold text-white mb-2">Install MyTube App</h3>
        <p className="text-white/80 mb-6">Get the best experience with our mobile app</p>
        
        <div className="space-y-3">
          {isInstallable ? (
            <button
              onClick={handleInstall}
              className="w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              <i className="fas fa-download mr-2"></i>
              Install Now
            </button>
          ) : (
            <button
              onClick={handleShowInstructions}
              className="w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              <i className="fas fa-info-circle mr-2"></i>
              How to Install
            </button>
          )}
        </div>
        
        <div className="mt-4 text-white/60 text-sm">
          <div className="flex items-center justify-center space-x-4">
            <span><i className="fas fa-bolt mr-1"></i>Faster</span>
            <span><i className="fas fa-wifi mr-1"></i>Offline</span>
            <span><i className="fas fa-home mr-1"></i>Home Screen</span>
          </div>
        </div>
      </div>
    )
  }

  // Dashboard variant
  return (
    <div className={`glass-card p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <i className="fas fa-mobile-alt text-purple-400 text-xl mr-3"></i>
          <div>
            <h4 className="text-white font-semibold">Install MyTube App</h4>
            <p className="text-white/60 text-sm">Get faster access & offline features</p>
          </div>
        </div>
        
        {isInstallable ? (
          <button
            onClick={handleInstall}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            <i className="fas fa-download mr-1"></i>
            Install
          </button>
        ) : (
          <button
            onClick={handleShowInstructions}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            <i className="fas fa-info-circle mr-1"></i>
            How to
          </button>
        )}
      </div>
    </div>
  )
}
