import type { Long } from '@grpc/proto-loader';
/**
 * SubchannelRef is a reference to a Subchannel.
 */
export interface SubchannelRef {
    /**
     * The globally unique id for this subchannel.  Must be a positive number.
     */
    'subchannel_id'?: (number | string | Long);
    /**
     * An optional name associated with the subchannel.
     */
    'name'?: (string);
}
/**
 * SubchannelRef is a reference to a Subchannel.
 */
export interface SubchannelRef__Output {
    /**
     * The globally unique id for this subchannel.  Must be a positive number.
     */
    'subchannel_id': (string);
    /**
     * An optional name associated with the subchannel.
     */
    'name': (string);
}
