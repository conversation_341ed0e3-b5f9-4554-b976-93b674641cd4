(()=>{var e={};e.id=3790,e.ids=[3790],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},9321:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o={children:["",{children:["transactions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,40390)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\transactions\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\transactions\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/transactions/page",pathname:"/transactions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11242:(e,t,s)=>{Promise.resolve().then(s.bind(s,40390))},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},40390:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\transactions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\transactions\\page.tsx","default")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},52820:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(60687),a=s(43210),i=s(85814),n=s.n(i),l=s(87979),c=s(3582),o=s(77567);function d(){let{user:e,loading:t}=(0,l.Nu)(),[s,i]=(0,a.useState)([]),[d,x]=(0,a.useState)([]),[u,m]=(0,a.useState)(!0),[p,h]=(0,a.useState)(1),[f,j]=(0,a.useState)(1),[g,b]=(0,a.useState)({type:"",status:"",dateFrom:"",dateTo:"",searchTerm:""}),[N,v]=(0,a.useState)(!1),w=async()=>{try{m(!0);let t=await (0,c.I0)(e.uid,100);i(t)}catch(e){console.error("Error loading transactions:",e),o.A.fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{m(!1)}},y=(e,t)=>{b(s=>({...s,[e]:t}))},S=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),q=e=>{switch(e){case"video_earning":return"fas fa-play-circle text-green-400";case"withdrawal":return"fas fa-download text-red-400";case"bonus":return"fas fa-gift text-yellow-400";case"referral":return"fas fa-users text-blue-400";default:return"fas fa-exchange-alt text-white"}},C=e=>{switch(e){case"video_earning":return"Video Earning";case"withdrawal":return"Withdrawal";case"bonus":return"Bonus";case"referral":return"Referral";default:return e.charAt(0).toUpperCase()+e.slice(1)}},T=(p-1)*20,k=d.slice(T,T+20);return t||u?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading transactions..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen p-4",children:[(0,r.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(n(),{href:"/wallet",className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Wallet"]}),(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Transaction History"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{onClick:()=>v(!N),className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-filter mr-2"}),"Filters"]}),(0,r.jsxs)("button",{onClick:w,className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsx)("div",{className:"glass-card p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Total Transactions"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:d.length})]}),(0,r.jsx)("i",{className:"fas fa-list text-blue-400 text-2xl"})]})}),(0,r.jsx)("div",{className:"glass-card p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Total Earned"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-400",children:S(d.filter(e=>e.amount>0).reduce((e,t)=>e+t.amount,0))})]}),(0,r.jsx)("i",{className:"fas fa-arrow-up text-green-400 text-2xl"})]})}),(0,r.jsx)("div",{className:"glass-card p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Total Withdrawn"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-400",children:S(Math.abs(d.filter(e=>e.amount<0).reduce((e,t)=>e+t.amount,0)))})]}),(0,r.jsx)("i",{className:"fas fa-arrow-down text-red-400 text-2xl"})]})}),(0,r.jsx)("div",{className:"glass-card p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"This Month"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:d.filter(e=>{let t=new Date,s=new Date(e.date);return s.getMonth()===t.getMonth()&&s.getFullYear()===t.getFullYear()}).length})]}),(0,r.jsx)("i",{className:"fas fa-calendar text-yellow-400 text-2xl"})]})})]}),N&&(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-filter mr-2"}),"Filter Transactions"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Search"}),(0,r.jsx)("input",{type:"text",value:g.searchTerm,onChange:e=>y("searchTerm",e.target.value),placeholder:"Search description or type...",className:"form-input"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Type"}),(0,r.jsxs)("select",{value:g.type,onChange:e=>y("type",e.target.value),className:"form-input",children:[(0,r.jsx)("option",{value:"",children:"All Types"}),(0,r.jsx)("option",{value:"video_earning",children:"Video Earning"}),(0,r.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,r.jsx)("option",{value:"bonus",children:"Bonus"}),(0,r.jsx)("option",{value:"referral",children:"Referral"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Status"}),(0,r.jsxs)("select",{value:g.status,onChange:e=>y("status",e.target.value),className:"form-input",children:[(0,r.jsx)("option",{value:"",children:"All Status"}),(0,r.jsx)("option",{value:"completed",children:"Completed"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"failed",children:"Failed"}),(0,r.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"From Date"}),(0,r.jsx)("input",{type:"date",value:g.dateFrom,onChange:e=>y("dateFrom",e.target.value),className:"form-input"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"To Date"}),(0,r.jsx)("input",{type:"date",value:g.dateTo,onChange:e=>y("dateTo",e.target.value),className:"form-input"})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsxs)("button",{onClick:()=>{if(0===d.length)return void o.A.fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=new Blob([["Date,Type,Description,Amount,Status",...d.map(e=>[e.date instanceof Date?e.date.toLocaleDateString():new Date(e.date).toLocaleDateString(),e.type,`"${e.description}"`,e.amount,e.status].join(","))].join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download=`transactions_${new Date().toISOString().split("T")[0]}.csv`,s.click(),window.URL.revokeObjectURL(t)},className:"btn-primary w-full",disabled:0===d.length,children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]})})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{onClick:()=>{b({type:"",status:"",dateFrom:"",dateTo:"",searchTerm:""})},className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-times mr-2"}),"Clear Filters"]}),(0,r.jsxs)("span",{className:"text-white/60 text-sm flex items-center",children:["Showing ",d.length," of ",s.length," transactions"]})]})]}),(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,r.jsx)("i",{className:"fas fa-history mr-2"}),"Transactions"]}),d.length>0&&(0,r.jsxs)("p",{className:"text-white/60 text-sm",children:["Page ",p," of ",f]})]}),0===d.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("i",{className:"fas fa-receipt text-white/30 text-6xl mb-4"}),(0,r.jsx)("p",{className:"text-white/60 text-lg mb-2",children:"No transactions found"}),(0,r.jsx)("p",{className:"text-white/40 text-sm",children:0===s.length?"You haven't made any transactions yet":"Try adjusting your filters"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"hidden md:block overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-white/20",children:[(0,r.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Date & Time"}),(0,r.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Type"}),(0,r.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Description"}),(0,r.jsx)("th",{className:"text-right text-white font-medium py-3 px-2",children:"Amount"}),(0,r.jsx)("th",{className:"text-center text-white font-medium py-3 px-2",children:"Status"})]})}),(0,r.jsx)("tbody",{children:k.map(e=>(0,r.jsxs)("tr",{className:"border-b border-white/10 hover:bg-white/5",children:[(0,r.jsxs)("td",{className:"py-4 px-2",children:[(0,r.jsx)("div",{className:"text-white text-sm",children:e.date.toLocaleDateString()}),(0,r.jsx)("div",{className:"text-white/60 text-xs",children:e.date.toLocaleTimeString()})]}),(0,r.jsx)("td",{className:"py-4 px-2",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:`${q(e.type)} mr-2`}),(0,r.jsx)("span",{className:"text-white text-sm",children:C(e.type)})]})}),(0,r.jsx)("td",{className:"py-4 px-2",children:(0,r.jsx)("p",{className:"text-white text-sm",children:e.description})}),(0,r.jsx)("td",{className:"py-4 px-2 text-right",children:(0,r.jsxs)("p",{className:`font-bold text-sm ${e.amount>0?"text-green-400":"text-red-400"}`,children:[e.amount>0?"+":"",S(e.amount)]})}),(0,r.jsx)("td",{className:"py-4 px-2 text-center",children:(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"completed"===e.status?"bg-green-400/20 text-green-400":"pending"===e.status?"bg-yellow-400/20 text-yellow-400":"failed"===e.status?"bg-red-400/20 text-red-400":"bg-gray-400/20 text-gray-400"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id))})]})}),(0,r.jsx)("div",{className:"md:hidden space-y-3",children:k.map(e=>(0,r.jsxs)("div",{className:"p-4 bg-white/10 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:`${q(e.type)} mr-2`}),(0,r.jsx)("span",{className:"text-white font-medium text-sm",children:C(e.type)})]}),(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"completed"===e.status?"bg-green-400/20 text-green-400":"pending"===e.status?"bg-yellow-400/20 text-yellow-400":"failed"===e.status?"bg-red-400/20 text-red-400":"bg-gray-400/20 text-gray-400"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,r.jsx)("p",{className:"text-white text-sm mb-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-white/60 text-xs",children:[e.date.toLocaleDateString()," ",e.date.toLocaleTimeString()]}),(0,r.jsxs)("p",{className:`font-bold text-sm ${e.amount>0?"text-green-400":"text-red-400"}`,children:[e.amount>0?"+":"",S(e.amount)]})]})]},e.id))}),f>1&&(0,r.jsxs)("div",{className:"flex items-center justify-center mt-6 gap-2",children:[(0,r.jsx)("button",{onClick:()=>h(e=>Math.max(1,e-1)),disabled:1===p,className:"glass-button px-3 py-2 text-white disabled:opacity-50",children:(0,r.jsx)("i",{className:"fas fa-chevron-left"})}),(0,r.jsx)("div",{className:"flex gap-1",children:Array.from({length:Math.min(5,f)},(e,t)=>{let s;return s=f<=5||p<=3?t+1:p>=f-2?f-4+t:p-2+t,(0,r.jsx)("button",{onClick:()=>h(s),className:`px-3 py-2 text-sm rounded ${p===s?"bg-blue-500 text-white":"glass-button text-white"}`,children:s},s)})}),(0,r.jsx)("button",{onClick:()=>h(e=>Math.min(f,e+1)),disabled:p===f,className:"glass-button px-3 py-2 text-white disabled:opacity-50",children:(0,r.jsx)("i",{className:"fas fa-chevron-right"})})]})]})]})]})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74290:(e,t,s)=>{Promise.resolve().then(s.bind(s,52820))},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[6204,6958,7567,8441,3582,7979],()=>s(9321));module.exports=r})();