'use client'

import { useState, useEffect } from 'react'
import { 
  loadVideosFromFile, 
  initializeVideoSystem, 
  getVideoStats, 
  clearVideoStorage,
  VideoData 
} from '@/lib/videoManager'

export default function TestVideosPage() {
  const [videos, setVideos] = useState<VideoData[]>([])
  const [stats, setStats] = useState({
    totalVideos: 0,
    currentBatch: 0,
    totalBatches: 0,
    videosInCurrentBatch: 0
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testLoadVideos = async () => {
    setLoading(true)
    setError(null)
    try {
      console.log('Loading videos from file...')
      const loadedVideos = await loadVideosFromFile()
      console.log('Videos loaded:', loadedVideos.length)
      setVideos(loadedVideos.slice(0, 10)) // Show first 10 for testing
      
      // Get stats
      const videoStats = getVideoStats()
      setStats(videoStats)
      
    } catch (err: any) {
      console.error('Error loading videos:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const testInitializeSystem = async () => {
    setLoading(true)
    setError(null)
    try {
      console.log('Initializing video system...')
      const currentBatch = await initializeVideoSystem()
      console.log('System initialized, current batch:', currentBatch.length)
      setVideos(currentBatch.slice(0, 10)) // Show first 10 for testing
      
      // Get stats
      const videoStats = getVideoStats()
      setStats(videoStats)
      
    } catch (err: any) {
      console.error('Error initializing system:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const clearStorage = () => {
    clearVideoStorage()
    setVideos([])
    setStats({
      totalVideos: 0,
      currentBatch: 0,
      totalBatches: 0,
      videosInCurrentBatch: 0
    })
    console.log('Storage cleared')
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Video System Test</h1>
        
        {/* Controls */}
        <div className="glass-card p-6 mb-6">
          <h2 className="text-lg font-semibold text-white mb-4">Test Controls</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={testLoadVideos}
              disabled={loading}
              className="btn-primary"
            >
              {loading ? 'Loading...' : 'Load Videos from File'}
            </button>
            <button
              onClick={testInitializeSystem}
              disabled={loading}
              className="btn-secondary"
            >
              {loading ? 'Loading...' : 'Initialize Video System'}
            </button>
            <button
              onClick={clearStorage}
              className="btn-danger"
            >
              Clear Storage
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="glass-card p-6 mb-6">
          <h2 className="text-lg font-semibold text-white mb-4">Video Statistics</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{stats.totalVideos}</p>
              <p className="text-white/60">Total Videos</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">{stats.currentBatch + 1}/{stats.totalBatches}</p>
              <p className="text-white/60">Current Batch</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-400">{stats.videosInCurrentBatch}</p>
              <p className="text-white/60">Videos in Batch</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-400">{videos.length}</p>
              <p className="text-white/60">Displayed</p>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="glass-card p-6 mb-6 border-red-500 border">
            <h2 className="text-lg font-semibold text-red-400 mb-2">Error</h2>
            <p className="text-white">{error}</p>
          </div>
        )}

        {/* Videos Display */}
        <div className="glass-card p-6">
          <h2 className="text-lg font-semibold text-white mb-4">
            Videos Preview (First 10)
          </h2>
          
          {videos.length === 0 ? (
            <p className="text-white/60 text-center py-8">
              No videos loaded. Click a button above to test.
            </p>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {videos.map((video, index) => (
                <div key={video.id} className="bg-white/10 rounded-lg p-4">
                  <div className="aspect-video mb-3">
                    <iframe
                      src={video.embedUrl}
                      title={video.title}
                      className="w-full h-full rounded"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  </div>
                  <h3 className="text-white font-medium text-sm mb-2">
                    {video.title}
                  </h3>
                  <div className="text-xs text-white/60 space-y-1">
                    <p>ID: {video.id}</p>
                    <p>Duration: {video.duration}s</p>
                    <p>Batch: {video.batchIndex}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
