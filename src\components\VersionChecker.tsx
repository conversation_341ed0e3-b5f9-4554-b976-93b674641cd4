'use client'

import { useEffect, useState } from 'react'
import { checkVersionAndClearCache, manualCacheClear, simpleCacheClear, getVersionInfo } from '@/lib/versionService'

interface VersionCheckerProps {
  showClearButton?: boolean
}

export default function VersionChecker({ showClearButton = false }: VersionCheckerProps) {
  const [versionInfo, setVersionInfo] = useState<any>(null)

  useEffect(() => {
    // Silently check version on component mount (no user disruption)
    const checkVersion = async () => {
      try {
        // Silent version check - no loading state shown to user
        await checkVersionAndClearCache()

        // Update version info for display
        setVersionInfo(getVersionInfo())
      } catch (error) {
        console.error('Error checking version:', error)
      }
    }

    checkVersion()
  }, [])

  const handleManualClear = async () => {
    try {
      // Show confirmation first
      const confirmed = confirm(
        'This will clear all cached data and reload the page to get the latest version. Continue?'
      )

      if (!confirmed) return

      // Try the full cache clear with timeout
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Timeout')), 10000)
      )

      try {
        await Promise.race([manualCacheClear(), timeoutPromise])
      } catch (error) {
        console.error('Cache clear failed, trying simple clear:', error)

        // Fallback: simple cache clear
        simpleCacheClear()
        alert('Cache cleared! The page will now reload.')
        window.location.reload()
      }
    } catch (error) {
      console.error('Error during manual cache clear:', error)

      // Last resort: just reload
      window.location.reload()
    }
  }

  // Don't render anything if not showing the clear button
  if (!showClearButton) {
    return null
  }

  return (
    <div className="version-checker">
      {showClearButton && (
        <button
          onClick={handleManualClear}
          className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200 flex items-center gap-2"
          title="Clear cache and data to get the latest version"
        >
          <i className="fas fa-sync-alt"></i>
          Clear Cache & Data
        </button>
      )}
      

      
      {versionInfo && (
        <div className="text-xs text-gray-400 mt-1">
          v{versionInfo.currentVersion}
        </div>
      )}
    </div>
  )
}
