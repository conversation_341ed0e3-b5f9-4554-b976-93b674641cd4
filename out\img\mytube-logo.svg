<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="redGradient" x1="0" y1="0" x2="200" y2="200" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FF0000"/>
      <stop offset="1" stop-color="#CC0000"/>
    </linearGradient>
  </defs>

  <!-- White circle background -->
  <circle cx="100" cy="100" r="95" fill="white" stroke="#FF0000" stroke-width="2"/>

  <!-- YouTube Play Button -->
  <rect x="50" y="50" width="100" height="100" rx="20" fill="url(#redGradient)"/>
  
  <!-- Play Triangle -->
  <path d="M135 100L80 130V70L135 100Z" fill="white"/>
  
  <!-- "MT" Text for MyTube -->
  <g transform="translate(70, 160)">
    <text font-family="Arial, sans-serif" font-weight="bold" font-size="24" fill="#FF0000">MT</text>
  </g>
</svg>
