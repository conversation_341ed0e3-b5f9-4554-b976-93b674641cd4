exports.id=8441,exports.ids=[8441],exports.modules={3152:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\ErrorBoundary.tsx","default")},13224:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.t.bind(t,46533,23))},14329:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(60687);t(43210);var n=t(85814),i=t.n(n),a=t(30474);function o({error:e,reset:s}){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)(a.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:80,height:80,className:"mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"Oops!"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-white mb-2",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-white/80 mb-8 max-w-md mx-auto",children:"We encountered an unexpected error. Please try again or contact support if the problem persists."}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("p",{className:"text-white/60 mb-4",children:"Need immediate help?"}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-envelope mr-2"}),"Email Support"]})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("button",{onClick:s,className:"btn-primary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-redo mr-2"}),"Try Again"]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(i(),{href:"/",className:"btn-secondary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-home mr-2"}),"Go Home"]}),(0,r.jsxs)(i(),{href:"/dashboard",className:"btn-secondary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-tachometer-alt mr-2"}),"Dashboard"]})]})]}),!1]})})}},26376:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.t.bind(t,49603,23))},26559:(e,s,t)=>{Promise.resolve().then(t.bind(t,3152)),Promise.resolve().then(t.bind(t,98046))},33152:(e,s,t)=>{Promise.resolve().then(t.bind(t,14329))},45016:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});var r=t(60687),n=t(43210);function i(){let[e,s]=(0,n.useState)(null),[t,i]=(0,n.useState)(!1),a=async()=>{if(!e)return;e.prompt();let{outcome:t}=await e.userChoice;"accepted"===t?console.log("User accepted the install prompt"):console.log("User dismissed the install prompt"),s(null),i(!1)};return t?(0,r.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,r.jsxs)("button",{onClick:a,className:"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Install App"]})}):null}},54413:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(37413),n=t(4536),i=t.n(n),a=t(53384);function o(){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)(a.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:80,height:80,className:"mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-6xl font-bold text-white mb-4",children:"404"}),(0,r.jsx)("h2",{className:"text-2xl font-semibold text-white mb-2",children:"Page Not Found"}),(0,r.jsx)("p",{className:"text-white/80 mb-8 max-w-md mx-auto",children:"The page you're looking for doesn't exist or has been moved."}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("p",{className:"text-white/60 mb-4",children:"Need help finding what you're looking for?"}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-envelope mr-2"}),"Email Support"]})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(i(),{href:"/",className:"btn-primary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-home mr-2"}),"Go Home"]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(i(),{href:"/dashboard",className:"btn-secondary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-tachometer-alt mr-2"}),"Dashboard"]}),(0,r.jsxs)(i(),{href:"/work",className:"btn-secondary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-play-circle mr-2"}),"Watch Videos"]})]})]})]})})}},54431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx","default")},61135:()=>{},63511:(e,s,t)=>{Promise.resolve().then(t.bind(t,95758)),Promise.resolve().then(t.bind(t,45016))},67393:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(37413);function n(){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white/80",children:"Loading MyTube..."})]})})}},69600:(e,s,t)=>{Promise.resolve().then(t.bind(t,54431))},78335:()=>{},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>l,viewport:()=>c});var r=t(37413),n=t(77247),i=t.n(n);t(61135);var a=t(98046),o=t(3152);let l={title:"MyTube - Watch Videos & Earn",description:"Watch videos and earn money. Complete daily video watching tasks to earn rewards.",keywords:"video watching, earn money, online earning, video tasks, rewards",authors:[{name:"MyTube Team"}],manifest:"/manifest.json",icons:{icon:"/img/mytube-favicon.svg",apple:"/img/mytube-favicon.svg"}},c={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,themeColor:"#FF0000"};function d({children:e}){return(0,r.jsxs)("html",{lang:"en",className:i().variable,children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),(0,r.jsx)("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"}),(0,r.jsx)("script",{src:"https://cdn.jsdelivr.net/npm/sweetalert2@11",async:!0})]}),(0,r.jsxs)("body",{className:`${i().className} antialiased`,children:[(0,r.jsx)("div",{className:"animated-bg"}),(0,r.jsx)(o.default,{children:e}),(0,r.jsx)(a.default,{})]})]})}},95758:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var r=t(60687),n=t(43210),i=t.n(n);class a extends i().Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"glass-card p-8 text-center max-w-md",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle text-red-400 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-2",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-white/80 mb-4",children:"An error occurred while loading this page. Please refresh and try again."}),(0,r.jsxs)("button",{onClick:()=>window.location.reload(),className:"btn-primary",children:[(0,r.jsx)("i",{className:"fas fa-refresh mr-2"}),"Refresh Page"]})]})}):this.props.children}}let o=a},96487:()=>{},97187:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},97867:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},98046:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\components\\\\PWAInstaller.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\components\\PWAInstaller.tsx","default")}};