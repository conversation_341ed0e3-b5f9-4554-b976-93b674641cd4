(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[993,6779],{3737:(e,t,a)=>{"use strict";function i(e,t,a){if(!e||0===e.length)return void alert("No data to export");let i=a||Object.keys(e[0]),s=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],n=new Blob(["\uFEFF"+[i.join(","),...e.map(e=>i.map(t=>{let a=e[t];if(null==a)return"";let i=s.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):i&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),o=document.createElement("a");if(void 0!==o.download){let e=URL.createObjectURL(n);o.setAttribute("href",e),o.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),o.style.visibility="hidden",document.body.appendChild(o),o.click(),document.body.removeChild(o)}}function s(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function o(e){return e.map(e=>{var t,a,i,s;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(i=e.bankDetails)?void 0:i.accountNumber)||""),"IFSC Code":(null==(s=e.bankDetails)?void 0:s.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function r(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>i,Fz:()=>s,Pe:()=>r,dB:()=>o,sL:()=>n})},4880:(e,t,a)=>{Promise.resolve().then(a.bind(a,9119))},6273:(e,t,a)=>{"use strict";a.d(t,{x8:()=>p});var i=a(2144),s=a(6104);let n=(0,i.Qg)(s.Cn,"getUserDashboardData"),o=(0,i.Qg)(s.Cn,"submitVideoBatch"),r=(0,i.Qg)(s.Cn,"processWithdrawalRequest"),l=(0,i.Qg)(s.Cn,"getUserNotifications"),c=(0,i.Qg)(s.Cn,"getUserTransactions"),d=(0,i.Qg)(s.Cn,"getAdminWithdrawals"),u=(0,i.Qg)(s.Cn,"getAdminDashboardStats"),m=(0,i.Qg)(s.Cn,"getAdminUsers"),f=(0,i.Qg)(s.Cn,"getAdminNotifications"),g=(0,i.Qg)(s.Cn,"createAdminNotification");async function h(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function...");let t=await n({userId:e});if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Dashboard data loaded via optimized function"),e.data}throw Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),e}}async function x(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let p={getDashboardData:async function(e){try{return await h(e)}catch(l){console.warn("⚠️ Optimized function failed, falling back to direct calls");let{getUserData:t,getWalletData:i,getVideoCountData:s}=await Promise.resolve().then(a.bind(a,3592)),[n,o,r]=await Promise.all([t(e),i(e),s(e)]);return{userData:n,walletData:o,videoData:r}}},submitVideoBatch:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await o({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await r(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await l({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{console.log("\uD83D\uDE80 Using optimized transactions function...");let i=await c({userId:e,limit:t,type:a});if(i.data&&"object"==typeof i.data&&"success"in i.data){let e=i.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function...");let t=await d({showAllWithdrawals:e});if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data}throw Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),e}},getAdminDashboardStats:async function(){try{return await x()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await a.e(6779).then(a.bind(a,6779));return await e()}},getAdminUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await m(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await f({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await g(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{return await n({userId:"test"}),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),!1}}}},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>d,I0:()=>m,TK:()=>h,getAdminDashboardStats:()=>r,getAllPendingWithdrawals:()=>f,getAllWithdrawals:()=>g,hG:()=>x,lo:()=>l,nQ:()=>u,updateWithdrawalStatus:()=>p,x5:()=>c});var i=a(5317),s=a(6104),n=a(3592);let o=new Map;async function r(){let e="dashboard-stats",t=function(e){let t=o.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=i.Dc.fromDate(t),r=await (0,i.getDocs)((0,i.collection)(s.db,n.COLLECTIONS.users)),l=r.size,c=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.users),(0,i._M)(n.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,i.getDocs)(c)).size,u=0,m=0,f=0,g=0;r.forEach(e=>{var a;let i=e.data();u+=i[n.FIELD_NAMES.totalVideos]||0,m+=i[n.FIELD_NAMES.wallet]||0;let s=null==(a=i[n.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();s&&s.toDateString()===t.toDateString()&&(f+=i[n.FIELD_NAMES.todayVideos]||0)});try{let e=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.transactions),(0,i._M)(n.FIELD_NAMES.type,"==","video_earning"),(0,i.AB)(1e3));(await (0,i.getDocs)(e)).forEach(e=>{var a;let i=e.data(),s=null==(a=i[n.FIELD_NAMES.date])?void 0:a.toDate();s&&s>=t&&(g+=i[n.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let h=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.withdrawals),(0,i._M)("status","==","pending")),x=(await (0,i.getDocs)(h)).size,p=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.withdrawals),(0,i._M)("date",">=",a)),y=(await (0,i.getDocs)(p)).size,w={totalUsers:l,totalVideos:u,totalEarnings:m,pendingWithdrawals:x,todayUsers:d,todayVideos:f,todayEarnings:g,todayWithdrawals:y};return o.set(e,{data:w,timestamp:Date.now()}),w}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.users),(0,i.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,i.AB)(e));t&&(a=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.users),(0,i.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,i.HM)(t),(0,i.AB)(e)));let o=await (0,i.getDocs)(a);return{users:o.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.users),(0,i.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,i.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[n.FIELD_NAMES.name]||"").toLowerCase(),i=String(e[n.FIELD_NAMES.email]||"").toLowerCase(),s=String(e[n.FIELD_NAMES.mobile]||"").toLowerCase(),o=String(e[n.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||i.includes(t)||s.includes(t)||o.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.users),(0,i.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,i.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[n.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.users));return(await (0,i.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.transactions),(0,i.My)(n.FIELD_NAMES.date,"desc"),(0,i.AB)(e));t&&(a=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.transactions),(0,i.My)(n.FIELD_NAMES.date,"desc"),(0,i.HM)(t),(0,i.AB)(e)));let o=await (0,i.getDocs)(a);return{transactions:o.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[n.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:o.docs[o.docs.length-1]||null,hasMore:o.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function f(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.withdrawals),(0,i._M)("status","==","pending"),(0,i.My)("date","desc")),t=(await (0,i.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," pending withdrawals")),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,i.P)((0,i.collection)(s.db,n.COLLECTIONS.withdrawals),(0,i.My)("date","desc")),t=(await (0,i.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total withdrawals")),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function h(e,t){try{await (0,i.mZ)((0,i.H9)(s.db,n.COLLECTIONS.users,e),t),o.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function x(e){try{await (0,i.kd)((0,i.H9)(s.db,n.COLLECTIONS.users,e)),o.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function p(e,t,r){try{let l=await (0,i.x7)((0,i.H9)(s.db,n.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=l.data(),m={status:t,updatedAt:i.Dc.now()};if(r&&(m.adminNotes=r),await (0,i.mZ)((0,i.H9)(s.db,n.COLLECTIONS.withdrawals,e),m),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}o.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},9119:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var i=a(5155),s=a(2115),n=a(6874),o=a.n(n),r=a(6681),l=a(3592),c=a(6273),d=a(6779),u=a(3737),m=a(4752),f=a.n(m);function g(){let{user:e,loading:t,isAdmin:a}=(0,r.wC)(),[n,m]=(0,s.useState)([]),[g,h]=(0,s.useState)([]),[x,p]=(0,s.useState)(!0),[y,w]=(0,s.useState)(!1),[b,N]=(0,s.useState)(!1),[v,D]=(0,s.useState)([]),[j,E]=(0,s.useState)(!1),[S,C]=(0,s.useState)({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]});(0,s.useEffect)(()=>{a&&L()},[a]);let L=async()=>{try{p(!0);try{console.log("\uD83D\uDE80 Loading admin notifications with optimized functions...");let[e,t]=await Promise.all([c.x8.getAdminNotifications(50,"all"),c.x8.getAdminUsers({limit:100})]);m(e),h(t.users),console.log("✅ Admin notifications loaded via optimized functions")}catch(a){console.warn("⚠️ Optimized functions failed, using fallback:",a);let[e,t]=await Promise.all([(0,l._f)(50),(0,d.lo)()]);m(e),h(t.users)}}catch(e){console.error("Error loading data:",e),f().fire({icon:"error",title:"Error",text:"Failed to load data. Please try again."})}finally{p(!1)}},A=async()=>{try{N(!0);try{await c.x8.createAdminNotification({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[]})}catch(t){console.warn("⚠️ Optimized notification creation failed, using fallback:",t),await (0,l.z8)({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"})}f().fire({icon:"success",title:"Test Notification Sent!",text:"Test notification sent to all users. Check user dashboards to verify delivery.",timer:3e3,showConfirmButton:!1}),L()}catch(e){console.error("Error sending test notification:",e),f().fire({icon:"error",title:"Test Failed",text:"Failed to send test notification. Please try again."})}finally{N(!1)}},I=async()=>{try{if(!S.title.trim()||!S.message.trim())return void f().fire({icon:"error",title:"Validation Error",text:"Please fill in both title and message."});if("specific"===S.targetUsers&&0===S.selectedUserIds.length)return void f().fire({icon:"error",title:"Validation Error",text:"Please select at least one user for specific targeting."});N(!0),console.log("Sending notification:",{title:S.title.trim(),message:S.message.trim(),type:S.type,targetUsers:S.targetUsers,userIds:"specific"===S.targetUsers?S.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"});try{await c.x8.createAdminNotification({title:S.title.trim(),message:S.message.trim(),type:S.type,targetUsers:S.targetUsers,userIds:"specific"===S.targetUsers?S.selectedUserIds:[]})}catch(t){console.warn("⚠️ Optimized notification creation failed, using fallback:",t),await (0,l.z8)({title:S.title.trim(),message:S.message.trim(),type:S.type,targetUsers:S.targetUsers,userIds:"specific"===S.targetUsers?S.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"})}f().fire({icon:"success",title:"Notification Sent!",text:"Notification sent to ".concat("all"===S.targetUsers?"all users":"".concat(S.selectedUserIds.length," selected users"),"."),timer:3e3,showConfirmButton:!1}),C({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]}),w(!1),L()}catch(e){console.error("Error sending notification:",e),f().fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{N(!1)}},U=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},k=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return"".concat(e," minute").concat(e>1?"s":""," ago")}if(t<86400){let e=Math.floor(t/3600);return"".concat(e," hour").concat(e>1?"s":""," ago")}{let e=Math.floor(t/86400);return"".concat(e," day").concat(e>1?"s":""," ago")}},T=async(e,t)=>{if((await f().fire({icon:"warning",title:"Delete Notification",text:'Are you sure you want to delete "'.concat(t,'"? This action cannot be undone.'),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await (0,l.fP)(e),m(t=>t.filter(t=>t.id!==e)),f().fire({icon:"success",title:"Notification Deleted",text:"Notification has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notification:",e),f().fire({icon:"error",title:"Delete Failed",text:"Failed to delete notification. Please try again."})}finally{E(!1)}},M=async()=>{if(0===v.length)return void f().fire({icon:"warning",title:"No Selection",text:"Please select notifications to delete."});if((await f().fire({icon:"warning",title:"Delete Selected Notifications",text:"Are you sure you want to delete ".concat(v.length," selected notifications? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete All",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await Promise.all(v.map(e=>(0,l.fP)(e))),m(e=>e.filter(e=>!v.includes(e.id))),D([]),f().fire({icon:"success",title:"Notifications Deleted",text:"".concat(v.length," notifications have been deleted successfully"),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notifications:",e),f().fire({icon:"error",title:"Delete Failed",text:"Failed to delete some notifications. Please try again."})}finally{E(!1)}},F=e=>{D(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return t||x?(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})}):(0,i.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,i.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(o(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("span",{className:"text-gray-700",children:["Total: ",n.length,v.length>0&&(0,i.jsxs)("span",{className:"ml-2 text-blue-600",children:["(",v.length," selected)"]})]}),v.length>0&&(0,i.jsxs)("button",{onClick:M,disabled:j,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-trash mr-2"}),"Delete Selected (",v.length,")"]}),(0,i.jsxs)("button",{onClick:A,disabled:b||j,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-vial mr-2"}),"Test Notification"]}),(0,i.jsxs)("button",{onClick:()=>{if(0===n.length)return void f().fire({icon:"warning",title:"No Data",text:"No notifications to export."});let e=(0,u.Pe)(n);(0,u.Bf)(e,"notifications"),f().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(n.length," notifications to CSV file."),timer:2e3,showConfirmButton:!1})},disabled:j,className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,i.jsxs)("button",{onClick:()=>w(!0),disabled:j,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-plus mr-2"}),"Send Notification"]}),(0,i.jsxs)("button",{onClick:L,disabled:j,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,i.jsx)("div",{className:"p-6",children:(0,i.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===n.length?(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-6xl mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications sent yet"}),(0,i.jsx)("p",{className:"text-gray-500 mb-4",children:"Start by sending your first notification to users"}),(0,i.jsxs)("button",{onClick:()=>w(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,i.jsx)("i",{className:"fas fa-plus mr-2"}),"Send First Notification"]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"bg-gray-50 px-6 py-3 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",checked:v.length===n.length&&n.length>0,onChange:()=>{v.length===n.length?D([]):D(n.map(e=>e.id).filter(Boolean))},className:"mr-3"}),(0,i.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:["Select All (",n.length," notifications)"]})]}),v.length>0&&(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)("span",{className:"text-sm text-gray-600",children:[v.length," selected"]}),(0,i.jsxs)("button",{onClick:M,disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete Selected"]})]})]})}),(0,i.jsx)("div",{className:"divide-y divide-gray-200",children:n.map(e=>{var t;return(0,i.jsx)("div",{className:"p-6 hover:bg-gray-50 ".concat(v.includes(e.id)?"bg-blue-50":""),children:(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,i.jsx)("input",{type:"checkbox",checked:v.includes(e.id),onChange:()=>F(e.id),className:"mr-3"})}),(0,i.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,i.jsx)("i",{className:U(e.type)})}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.title,(0,i.jsx)("span",{className:"ml-2 px-2 py-1 text-xs font-bold bg-red-100 text-red-800 rounded-full",children:"\uD83D\uDEA8 BLOCKING"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("success"===e.type?"bg-green-100 text-green-800":"warning"===e.type?"bg-yellow-100 text-yellow-800":"error"===e.type?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.type.charAt(0).toUpperCase()+e.type.slice(1)}),(0,i.jsx)("button",{onClick:()=>T(e.id,e.title),disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50 p-1",title:"Delete notification",children:(0,i.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,i.jsx)("p",{className:"text-gray-700 mt-2",children:e.message}),(0,i.jsx)("div",{className:"flex items-center justify-between mt-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,i.jsxs)("span",{children:[(0,i.jsx)("i",{className:"fas fa-user mr-1"}),"By: ",e.createdBy]}),(0,i.jsxs)("span",{children:[(0,i.jsx)("i",{className:"fas fa-users mr-1"}),"Target: ","all"===e.targetUsers?"All Users":"".concat((null==(t=e.userIds)?void 0:t.length)||0," Selected Users")]}),(0,i.jsxs)("span",{children:[(0,i.jsx)("i",{className:"fas fa-clock mr-1"}),k(e.createdAt)]})]})})]})]})},e.id)})})]})})}),y&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Send Notification"}),(0,i.jsx)("button",{onClick:()=>w(!1),className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),(0,i.jsx)("input",{type:"text",value:S.title,onChange:e=>C(t=>({...t,title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification title..."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,i.jsx)("textarea",{value:S.message,onChange:e=>C(t=>({...t,message:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification message..."})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,i.jsxs)("select",{value:S.type,onChange:e=>C(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"info",children:"Info"}),(0,i.jsx)("option",{value:"success",children:"Success"}),(0,i.jsx)("option",{value:"warning",children:"Warning"}),(0,i.jsx)("option",{value:"error",children:"Error"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Target"}),(0,i.jsxs)("select",{value:S.targetUsers,onChange:e=>C(t=>({...t,targetUsers:e.target.value,selectedUserIds:"all"===e.target.value?[]:t.selectedUserIds})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"all",children:"All Users"}),(0,i.jsx)("option",{value:"specific",children:"Specific Users"})]})]})]}),(0,i.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("i",{className:"fas fa-exclamation-triangle text-red-500 mt-1"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"\uD83D\uDEA8 All Notifications are Blocking (Mandatory)"}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Users must acknowledge this notification before they can continue with any activities (watching videos, accessing dashboard features, etc.)"})]})]})}),"specific"===S.targetUsers&&(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Users"}),(0,i.jsx)("div",{className:"max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2",children:g.map(e=>(0,i.jsxs)("label",{className:"flex items-center p-2 hover:bg-gray-50 rounded",children:[(0,i.jsx)("input",{type:"checkbox",checked:S.selectedUserIds.includes(e.id),onChange:t=>{t.target.checked?C(t=>({...t,selectedUserIds:[...t.selectedUserIds,e.id]})):C(t=>({...t,selectedUserIds:t.selectedUserIds.filter(t=>t!==e.id)}))},className:"mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-gray-900",children:e.name||"Unknown User"}),(0,i.jsxs)("div",{className:"text-sm text-gray-500",children:[e.email||"No email"," • ",e.plan||"No plan"]})]})]},e.id))}),(0,i.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[S.selectedUserIds.length," user(s) selected"]})]})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,i.jsx)("button",{onClick:()=>w(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,i.jsx)("button",{onClick:I,disabled:b,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:b?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Notification"]})})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>t(4880)),_N_E=e.O()}]);