(()=>{var e={};e.id=5094,e.ids=[5094],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30570:(e,t,r)=>{Promise.resolve().then(r.bind(r,49326))},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{Cn:()=>d,db:()=>l,j2:()=>p});var s=r(67989),i=r(63385),o=r(75535),n=r(70146),a=r(24791);let u=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),p=(0,i.xI)(u),l=(0,o.aU)(u);(0,n.c7)(u);let d=(0,a.Uz)(u)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},49326:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-simple-registration\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-simple-registration\\page.tsx","default")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>l,routeModule:()=>c,tree:()=>p});var s=r(65239),i=r(48088),o=r(88170),n=r.n(o),a=r(30893),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let p={children:["",{children:["test-simple-registration",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49326)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-simple-registration\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-simple-registration\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-simple-registration/page",pathname:"/test-simple-registration",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},86696:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),o=r(63385),n=r(75535),a=r(33784);function u(){let[e,t]=(0,i.useState)(""),[r,u]=(0,i.useState)(!1),p=e=>{t(t=>t+e+"\n"),console.log(e)},l=async()=>{u(!0),t("");try{p("\uD83E\uDDEA Testing Simple Registration...");let e=`simple-test-${Date.now()}@example.com`;p("Step 1: Creating Firebase Auth user...");let t=(await (0,o.eJ)(a.j2,e,"test123456")).user;p(`✅ User created: ${t.uid}`),p("Step 2: Creating simple Firestore document...");let r={name:"Simple Test User",email:e,plan:"Trial",wallet:0,created:new Date().toISOString()},s=(0,n.H9)(a.db,"users",t.uid);await (0,n.BN)(s,r),p("✅ Document created successfully"),p("Step 3: Verifying document...");let i=await (0,n.x7)(s);i.exists()?(p("✅ Document verified successfully"),p(`Data: ${JSON.stringify(i.data(),null,2)}`)):p("❌ Document not found"),p("Step 4: Cleaning up..."),await t.delete(),p("✅ Test user deleted"),p("\n\uD83C\uDF89 Simple registration test completed successfully!")}catch(e){p(`❌ Error: ${e.message}`),p(`❌ Code: ${e.code}`),p(`❌ Full error: ${JSON.stringify(e,null,2)}`)}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"\uD83E\uDDEA Simple Registration Test"}),(0,s.jsx)("p",{className:"text-white/80 mb-6",children:"This test uses simple field names to check if the issue is with complex field mapping."}),(0,s.jsx)("button",{onClick:l,disabled:r,className:"btn-primary mb-6",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Testing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-test-tube mr-2"}),"Test Simple Registration"]})}),e&&(0,s.jsxs)("div",{className:"bg-black/50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-2",children:"Test Results:"}),(0,s.jsx)("pre",{className:"text-green-400 text-sm whitespace-pre-wrap font-mono overflow-x-auto",children:e})]})]})})})}},91645:e=>{"use strict";e.exports=require("net")},93618:(e,t,r)=>{Promise.resolve().then(r.bind(r,86696))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,6958,8441],()=>r(84517));module.exports=s})();