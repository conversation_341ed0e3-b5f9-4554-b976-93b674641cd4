(()=>{var e={};e.id=2162,e.ids=[2162],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3340:(e,t,s)=>{Promise.resolve().then(s.bind(s,10038))},4573:e=>{"use strict";e.exports=require("node:buffer")},10038:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(60687),i=s(43210),o=s(85814),a=s.n(o),n=s(30474),l=s(63385),c=s(33784),d=s(87979),u=s(77567);function p(){let{user:e,loading:t}=(0,d.hD)(),[s,o]=(0,i.useState)(""),[p,x]=(0,i.useState)(!1),[m,h]=(0,i.useState)(!1),f=async e=>{if(e.preventDefault(),!s.trim())return void u.A.fire({icon:"error",title:"Email Required",text:"Please enter your email address"});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))return void u.A.fire({icon:"error",title:"Invalid Email",text:"Please enter a valid email address"});x(!0);try{await (0,l.J1)(c.j2,s.trim().toLowerCase(),{url:`${window.location.origin}/login`,handleCodeInApp:!1}),h(!0),u.A.fire({icon:"success",title:"Reset Email Sent!",html:`
          <p>We've sent a password reset link to:</p>
          <p class="font-semibold text-blue-600">${s}</p>
          <p class="mt-4 text-sm text-gray-600">
            Please check your email and click the link to reset your password.
            If you don't see the email, check your spam folder.
          </p>
        `,confirmButtonText:"Got it!",confirmButtonColor:"#3b82f6"})}catch(t){console.error("Password reset error:",t);let e="An error occurred while sending the reset email";switch(t.code){case"auth/user-not-found":e="No account found with this email address. Please check your email or create a new account.";break;case"auth/invalid-email":e="Invalid email address format";break;case"auth/too-many-requests":e="Too many reset attempts. Please wait a few minutes before trying again.";break;case"auth/network-request-failed":e="Network error. Please check your internet connection and try again.";break;default:e=t.message||"Failed to send reset email"}u.A.fire({icon:"error",title:"Reset Failed",text:e})}finally{x(!1)}};return t?(0,r.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,r.jsx)("div",{className:"spinner"}),(0,r.jsx)("p",{className:"text-white mt-4",children:"Loading..."})]}):(0,r.jsx)("main",{className:"min-h-screen flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(a(),{href:"/",className:"inline-block",children:(0,r.jsx)(n.default,{src:"/logo.png",alt:"MyTube",width:120,height:120,className:"mx-auto mb-4"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Reset Password"}),(0,r.jsx)("p",{className:"text-white/80",children:m?"Check your email for reset instructions":"Enter your email to receive a password reset link"})]}),m?(0,r.jsxs)("div",{className:"glass-card p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("i",{className:"fas fa-check text-green-400 text-2xl"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Email Sent!"}),(0,r.jsxs)("p",{className:"text-white/80 mb-6",children:["We've sent a password reset link to ",(0,r.jsx)("span",{className:"font-semibold text-blue-400",children:s})]}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("button",{onClick:()=>{h(!1),o("")},className:"w-full btn-secondary",children:[(0,r.jsx)("i",{className:"fas fa-redo mr-2"}),"Send to Different Email"]})})]}):(0,r.jsxs)("form",{onSubmit:f,className:"glass-card p-8 space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("i",{className:"fas fa-envelope absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,r.jsx)("input",{type:"email",id:"email",value:s,onChange:e=>o(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Enter your email address",disabled:p})]})]}),(0,r.jsx)("button",{type:"submit",disabled:p,className:"w-full btn-primary flex items-center justify-center",children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Sending Reset Email..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Reset Email"]})})]}),(0,r.jsxs)("div",{className:"mt-6 text-center space-y-3",children:[(0,r.jsxs)(a(),{href:"/login",className:"text-white/80 hover:text-white transition-colors flex items-center justify-center",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Login"]}),(0,r.jsxs)("div",{className:"text-white/60",children:["Don't have an account?"," ",(0,r.jsx)(a(),{href:"/register",className:"text-white font-semibold hover:underline",children:"Sign up here"})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsxs)("div",{className:"glass-card p-4",children:[(0,r.jsxs)("h4",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-question-circle mr-2"}),"Need Help?"]}),(0,r.jsx)("p",{className:"text-white/60 text-sm mb-3",children:"If you don't receive the email within a few minutes:"}),(0,r.jsxs)("ul",{className:"text-white/60 text-sm space-y-1 text-left",children:[(0,r.jsx)("li",{children:"• Check your spam/junk folder"}),(0,r.jsx)("li",{children:"• Make sure you entered the correct email"}),(0,r.jsx)("li",{children:"• Wait a few minutes and try again"}),(0,r.jsx)("li",{children:"• Contact support if the problem persists"})]})]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13612:(e,t,s)=>{Promise.resolve().then(s.bind(s,36200))},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32453:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(65239),i=s(48088),o=s(88170),a=s.n(o),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36200)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\forgot-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\forgot-password\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},36200:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\forgot-password\\page.tsx","default")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[6204,6958,7567,8441,7979],()=>s(32453));module.exports=r})();