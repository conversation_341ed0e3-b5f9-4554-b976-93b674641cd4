{"version": 3, "file": "index.esm5.js", "sources": ["../../src/platformLoggerService.ts", "../../src/logger.ts", "../../src/constants.ts", "../../src/internal.ts", "../../src/errors.ts", "../../src/firebaseApp.ts", "../../src/firebaseServerApp.ts", "../../src/api.ts", "../../src/indexeddb.ts", "../../src/heartbeatService.ts", "../../src/registerCoreComponents.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ComponentContainer,\n  ComponentType,\n  Provider,\n  Name\n} from '@firebase/component';\nimport { PlatformLoggerService, VersionService } from './types';\n\nexport class PlatformLoggerServiceImpl implements PlatformLoggerService {\n  constructor(private readonly container: ComponentContainer) {}\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString(): string {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers\n      .map(provider => {\n        if (isVersionServiceProvider(provider)) {\n          const service = provider.getImmediate() as VersionService;\n          return `${service.library}/${service.version}`;\n        } else {\n          return null;\n        }\n      })\n      .filter(logString => logString)\n      .join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider: Provider<Name>): boolean {\n  const component = provider.getComponent();\n  return component?.type === ComponentType.VERSION;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name as appName } from '../package.json';\nimport { name as appCompatName } from '../../app-compat/package.json';\nimport { name as analyticsCompatName } from '../../../packages/analytics-compat/package.json';\nimport { name as analyticsName } from '../../../packages/analytics/package.json';\nimport { name as appCheckCompatName } from '../../../packages/app-check-compat/package.json';\nimport { name as appCheckName } from '../../../packages/app-check/package.json';\nimport { name as authName } from '../../../packages/auth/package.json';\nimport { name as authCompatName } from '../../../packages/auth-compat/package.json';\nimport { name as databaseName } from '../../../packages/database/package.json';\nimport { name as dataconnectName } from '../../../packages/data-connect/package.json';\nimport { name as databaseCompatName } from '../../../packages/database-compat/package.json';\nimport { name as functionsName } from '../../../packages/functions/package.json';\nimport { name as functionsCompatName } from '../../../packages/functions-compat/package.json';\nimport { name as installationsName } from '../../../packages/installations/package.json';\nimport { name as installationsCompatName } from '../../../packages/installations-compat/package.json';\nimport { name as messagingName } from '../../../packages/messaging/package.json';\nimport { name as messagingCompatName } from '../../../packages/messaging-compat/package.json';\nimport { name as performanceName } from '../../../packages/performance/package.json';\nimport { name as performanceCompatName } from '../../../packages/performance-compat/package.json';\nimport { name as remoteConfigName } from '../../../packages/remote-config/package.json';\nimport { name as remoteConfigCompatName } from '../../../packages/remote-config-compat/package.json';\nimport { name as storageName } from '../../../packages/storage/package.json';\nimport { name as storageCompatName } from '../../../packages/storage-compat/package.json';\nimport { name as firestoreName } from '../../../packages/firestore/package.json';\nimport { name as vertexName } from '../../../packages/vertexai/package.json';\nimport { name as firestoreCompatName } from '../../../packages/firestore-compat/package.json';\nimport { name as packageName } from '../../../packages/firebase/package.json';\n\n/**\n * The default app name\n *\n * @internal\n */\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\nexport const PLATFORM_LOG_STRING = {\n  [appName]: 'fire-core',\n  [appCompatName]: 'fire-core-compat',\n  [analyticsName]: 'fire-analytics',\n  [analyticsCompatName]: 'fire-analytics-compat',\n  [appCheckName]: 'fire-app-check',\n  [appCheckCompatName]: 'fire-app-check-compat',\n  [authName]: 'fire-auth',\n  [authCompatName]: 'fire-auth-compat',\n  [databaseName]: 'fire-rtdb',\n  [dataconnectName]: 'fire-data-connect',\n  [databaseCompatName]: 'fire-rtdb-compat',\n  [functionsName]: 'fire-fn',\n  [functionsCompatName]: 'fire-fn-compat',\n  [installationsName]: 'fire-iid',\n  [installationsCompatName]: 'fire-iid-compat',\n  [messagingName]: 'fire-fcm',\n  [messagingCompatName]: 'fire-fcm-compat',\n  [performanceName]: 'fire-perf',\n  [performanceCompatName]: 'fire-perf-compat',\n  [remoteConfigName]: 'fire-rc',\n  [remoteConfigCompatName]: 'fire-rc-compat',\n  [storageName]: 'fire-gcs',\n  [storageCompatName]: 'fire-gcs-compat',\n  [firestoreName]: 'fire-fst',\n  [firestoreCompatName]: 'fire-fst-compat',\n  [vertexName]: 'fire-vertex',\n  'fire-js': 'fire-js', // Platform identifier for JS SDK.\n  [packageName]: 'fire-js-all'\n} as const;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseServerApp\n} from './public-types';\nimport { Component, Provider, Name } from '@firebase/component';\nimport { logger } from './logger';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\n\n/**\n * @internal\n */\nexport const _apps = new Map<string, FirebaseApp>();\n\n/**\n * @internal\n */\nexport const _serverApps = new Map<string, FirebaseServerApp>();\n\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const _components = new Map<string, Component<any>>();\n\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nexport function _addComponent<T extends Name>(\n  app: FirebaseApp,\n  component: Component<T>\n): void {\n  try {\n    (app as FirebaseAppImpl).container.addComponent(component);\n  } catch (e) {\n    logger.debug(\n      `Component ${component.name} failed to register with FirebaseApp ${app.name}`,\n      e\n    );\n  }\n}\n\n/**\n *\n * @internal\n */\nexport function _addOrOverwriteComponent(\n  app: FirebaseApp,\n  component: Component\n): void {\n  (app as FirebaseAppImpl).container.addOrOverwriteComponent(component);\n}\n\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nexport function _registerComponent<T extends Name>(\n  component: Component<T>\n): boolean {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(\n      `There were multiple attempts to register component ${componentName}.`\n    );\n\n    return false;\n  }\n\n  _components.set(componentName, component);\n\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app as FirebaseAppImpl, component);\n  }\n\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp as FirebaseServerAppImpl, component);\n  }\n\n  return true;\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nexport function _getProvider<T extends Name>(\n  app: FirebaseApp,\n  name: T\n): Provider<T> {\n  const heartbeatController = (app as FirebaseAppImpl).container\n    .getProvider('heartbeat')\n    .getImmediate({ optional: true });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return (app as FirebaseAppImpl).container.getProvider(name);\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nexport function _removeServiceInstance<T extends Name>(\n  app: FirebaseApp,\n  name: T,\n  instanceIdentifier: string = DEFAULT_ENTRY_NAME\n): void {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nexport function _isFirebaseApp(\n  obj: FirebaseApp | FirebaseOptions\n): obj is FirebaseApp {\n  return (obj as FirebaseApp).options !== undefined;\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerApp(\n  obj: FirebaseApp | FirebaseServerApp\n): obj is FirebaseServerApp {\n  return (obj as FirebaseServerApp).settings !== undefined;\n}\n\n/**\n * Test only\n *\n * @internal\n */\nexport function _clearComponents(): void {\n  _components.clear();\n}\n\n/**\n * Exported in order to be used in app-compat package\n */\nexport { DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  BAD_APP_NAME = 'bad-app-name',\n  DUPLICATE_APP = 'duplicate-app',\n  APP_DELETED = 'app-deleted',\n  SERVER_APP_DELETED = 'server-app-deleted',\n  NO_OPTIONS = 'no-options',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument',\n  INVALID_LOG_ARGUMENT = 'invalid-log-argument',\n  IDB_OPEN = 'idb-open',\n  IDB_GET = 'idb-get',\n  IDB_WRITE = 'idb-set',\n  IDB_DELETE = 'idb-delete',\n  FINALIZATION_REGISTRY_NOT_SUPPORTED = 'finalization-registry-not-supported',\n  INVALID_SERVER_APP_ENVIRONMENT = 'invalid-server-app-environment'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call initializeApp() first',\n  [AppError.BAD_APP_NAME]: \"Illegal App name: '{$appName}'\",\n  [AppError.DUPLICATE_APP]:\n    \"Firebase App named '{$appName}' already exists with different options or config\",\n  [AppError.APP_DELETED]: \"Firebase App named '{$appName}' already deleted\",\n  [AppError.SERVER_APP_DELETED]: 'Firebase Server App has been deleted',\n  [AppError.NO_OPTIONS]:\n    'Need to provide options, when not being deployed to hosting via source.',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.',\n  [AppError.INVALID_LOG_ARGUMENT]:\n    'First argument to `onLog` must be null or a function.',\n  [AppError.IDB_OPEN]:\n    'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_GET]:\n    'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_WRITE]:\n    'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_DELETE]:\n    'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]:\n    'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [AppError.INVALID_SERVER_APP_ENVIRONMENT]:\n    'FirebaseServerApp is not for use in browser environments.'\n};\n\ninterface ErrorParams {\n  [AppError.NO_APP]: { appName: string };\n  [AppError.BAD_APP_NAME]: { appName: string };\n  [AppError.DUPLICATE_APP]: { appName: string };\n  [AppError.APP_DELETED]: { appName: string };\n  [AppError.INVALID_APP_ARGUMENT]: { appName: string };\n  [AppError.IDB_OPEN]: { originalErrorMessage?: string };\n  [AppError.IDB_GET]: { originalErrorMessage?: string };\n  [AppError.IDB_WRITE]: { originalErrorMessage?: string };\n  [AppError.IDB_DELETE]: { originalErrorMessage?: string };\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]: { appName?: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseAppSettings\n} from './public-types';\nimport {\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { ERROR_FACTORY, AppError } from './errors';\n\nexport class FirebaseAppImpl implements FirebaseApp {\n  protected readonly _options: FirebaseOptions;\n  protected readonly _name: string;\n  /**\n   * Original config values passed in as a constructor parameter.\n   * It is only used to compare with another config object to support idempotent initializeApp().\n   *\n   * Updating automaticDataCollectionEnabled on the App instance will not change its value in _config.\n   */\n  private readonly _config: Required<FirebaseAppSettings>;\n  private _automaticDataCollectionEnabled: boolean;\n  protected _isDeleted = false;\n  private readonly _container: ComponentContainer;\n\n  constructor(\n    options: FirebaseOptions,\n    config: Required<FirebaseAppSettings>,\n    container: ComponentContainer\n  ) {\n    this._options = { ...options };\n    this._config = { ...config };\n    this._name = config.name;\n    this._automaticDataCollectionEnabled =\n      config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(\n      new Component('app', () => this, ComponentType.PUBLIC)\n    );\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val: boolean) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    this.checkDestroyed();\n    return this._name;\n  }\n\n  get options(): FirebaseOptions {\n    this.checkDestroyed();\n    return this._options;\n  }\n\n  get config(): Required<FirebaseAppSettings> {\n    this.checkDestroyed();\n    return this._config;\n  }\n\n  get container(): ComponentContainer {\n    return this._container;\n  }\n\n  get isDeleted(): boolean {\n    return this._isDeleted;\n  }\n\n  set isDeleted(val: boolean) {\n    this._isDeleted = val;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.APP_DELETED, { appName: this._name });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseAppSettings,\n  FirebaseServerApp,\n  FirebaseServerAppSettings,\n  FirebaseOptions\n} from './public-types';\nimport { deleteApp, registerVersion } from './api';\nimport { ComponentContainer } from '@firebase/component';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { name as packageName, version } from '../package.json';\n\nexport class FirebaseServerAppImpl\n  extends FirebaseAppImpl\n  implements FirebaseServerApp\n{\n  private readonly _serverConfig: FirebaseServerAppSettings;\n  private _finalizationRegistry: FinalizationRegistry<object> | null;\n  private _refCount: number;\n\n  constructor(\n    options: FirebaseOptions | FirebaseAppImpl,\n    serverConfig: FirebaseServerAppSettings,\n    name: string,\n    container: ComponentContainer\n  ) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled =\n      serverConfig.automaticDataCollectionEnabled !== undefined\n        ? serverConfig.automaticDataCollectionEnabled\n        : false;\n\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config: Required<FirebaseAppSettings> = {\n      name,\n      automaticDataCollectionEnabled\n    };\n\n    if ((options as FirebaseOptions).apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options as FirebaseOptions, config, container);\n    } else {\n      const appImpl: FirebaseAppImpl = options as FirebaseAppImpl;\n      super(appImpl.options, config, container);\n    }\n\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = {\n      automaticDataCollectionEnabled,\n      ...serverConfig\n    };\n\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n\n    registerVersion(packageName, version, 'serverapp');\n  }\n\n  toJSON(): undefined {\n    return undefined;\n  }\n\n  get refCount(): number {\n    return this._refCount;\n  }\n\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj: object | undefined): void {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n\n  // Decrement the reference count.\n  decRefCount(): number {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  private automaticCleanup(): void {\n    void deleteApp(this);\n  }\n\n  get settings(): FirebaseServerAppSettings {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.SERVER_APP_DELETED);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseServerApp,\n  FirebaseOptions,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings\n} from './public-types';\nimport { DEFAULT_ENTRY_NAME, PLATFORM_LOG_STRING } from './constants';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport {\n  ComponentContainer,\n  Component,\n  Name,\n  ComponentType\n} from '@firebase/component';\nimport { version } from '../../firebase/package.json';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\nimport {\n  _apps,\n  _components,\n  _isFirebaseApp,\n  _registerComponent,\n  _serverApps\n} from './internal';\nimport { logger } from './logger';\nimport {\n  LogLevelString,\n  setLogLevel as setLogLevelImpl,\n  LogCallback,\n  LogOptions,\n  setUserLogHandler\n} from '@firebase/logger';\nimport {\n  deepEqual,\n  getDefaultAppConfig,\n  isBrowser,\n  isWebWorker\n} from '@firebase/util';\n\nexport { FirebaseError } from '@firebase/util';\n\n/**\n * The current SDK version.\n *\n * @public\n */\nexport const SDK_VERSION = version;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseApp} instance.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize default app\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeApp({\n *   apiKey: \"AIza....\",                             // Auth / General Use\n *   authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *   databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *   storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *   messagingSenderId: \"123456789\"                  // Cloud Messaging\n * });\n * ```\n *\n * @example\n * ```javascript\n *\n * // Initialize another app\n * const otherApp = initializeApp({\n *   databaseURL: \"https://<OTHER_DATABASE_NAME>.firebaseio.com\",\n *   storageBucket: \"<OTHER_STORAGE_BUCKET>.appspot.com\"\n * }, \"otherApp\");\n * ```\n *\n * @param options - Options to configure the app's services.\n * @param name - Optional name of the app to initialize. If no name\n *   is provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The initialized app.\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  name?: string\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @param options - Options to configure the app's services.\n * @param config - FirebaseApp Configuration\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  config?: FirebaseAppSettings\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @public\n */\nexport function initializeApp(): FirebaseApp;\nexport function initializeApp(\n  _options?: FirebaseOptions,\n  rawConfig = {}\n): FirebaseApp {\n  let options = _options;\n\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = { name };\n  }\n\n  const config: Required<FirebaseAppSettings> = {\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: false,\n    ...rawConfig\n  };\n  const name = config.name;\n\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(AppError.BAD_APP_NAME, {\n      appName: String(name)\n    });\n  }\n\n  options ||= getDefaultAppConfig();\n\n  if (!options) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  const existingApp = _apps.get(name) as FirebaseAppImpl;\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (\n      deepEqual(options, existingApp.options) &&\n      deepEqual(config, existingApp.config)\n    ) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(AppError.DUPLICATE_APP, { appName: name });\n    }\n  }\n\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseAppImpl(options, config, container);\n\n  _apps.set(name, newApp);\n\n  return newApp;\n}\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * The `FirebaseServerApp` is similar to `FirebaseApp`, but is intended for execution in\n * server side rendering environments only. Initialization will fail if invoked from a\n * browser environment.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize an instance of `FirebaseServerApp`.\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeServerApp({\n *     apiKey: \"AIza....\",                             // Auth / General Use\n *     authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *     databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *     storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *     messagingSenderId: \"123456789\"                  // Cloud Messaging\n *   },\n *   {\n *    authIdToken: \"Your Auth ID Token\"\n *   });\n * ```\n *\n * @param options - `Firebase.AppOptions` to configure the app's services, or a\n *   a `FirebaseApp` instance which contains the `AppOptions` within.\n * @param config - `FirebaseServerApp` configuration.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @public\n */\nexport function initializeServerApp(\n  options: FirebaseOptions | FirebaseApp,\n  config: FirebaseServerAppSettings\n): FirebaseServerApp;\n\nexport function initializeServerApp(\n  _options: FirebaseOptions | FirebaseApp,\n  _serverAppConfig: FirebaseServerAppSettings\n): FirebaseServerApp {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(AppError.INVALID_SERVER_APP_ENVIRONMENT);\n  }\n\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = false;\n  }\n\n  let appOptions: FirebaseOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = {\n    ..._serverAppConfig,\n    ...appOptions\n  };\n\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n\n  const hashCode = (s: string): number => {\n    return [...s].reduce(\n      (hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0,\n      0\n    );\n  };\n\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\n        AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED,\n        {}\n      );\n    }\n  }\n\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString) as FirebaseServerApp;\n  if (existingApp) {\n    (existingApp as FirebaseServerAppImpl).incRefCount(\n      _serverAppConfig.releaseOnDeref\n    );\n    return existingApp;\n  }\n\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseServerAppImpl(\n    appOptions,\n    _serverAppConfig,\n    nameString,\n    container\n  );\n\n  _serverApps.set(nameString, newApp);\n\n  return newApp;\n}\n\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nexport function getApp(name: string = DEFAULT_ENTRY_NAME): FirebaseApp {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n  }\n\n  return app;\n}\n\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nexport function getApps(): FirebaseApp[] {\n  return Array.from(_apps.values());\n}\n\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nexport async function deleteApp(app: FirebaseApp): Promise<void> {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app as FirebaseServerAppImpl;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n\n  if (cleanupProviders) {\n    await Promise.all(\n      (app as FirebaseAppImpl).container\n        .getProviders()\n        .map(provider => provider.delete())\n    );\n    (app as FirebaseAppImpl).isDeleted = true;\n  }\n}\n\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nexport function registerVersion(\n  libraryKeyOrName: string,\n  version: string,\n  variant?: string\n): void {\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = PLATFORM_LOG_STRING[libraryKeyOrName] ?? libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [\n      `Unable to register library \"${library}\" with version \"${version}\":`\n    ];\n    if (libraryMismatch) {\n      warning.push(\n        `library name \"${library}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(\n        `version name \"${version}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(\n    new Component(\n      `${library}-version` as Name,\n      () => ({ library, version }),\n      ComponentType.VERSION\n    )\n  );\n}\n\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nexport function onLog(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(AppError.INVALID_LOG_ARGUMENT);\n  }\n  setUserLogHandler(logCallback, options);\n}\n\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nexport function setLogLevel(logLevel: LogLevelString): void {\n  setLogLevelImpl(logLevel);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { DBSchema, openDB, IDBPDatabase } from 'idb';\nimport { AppError, ERROR_FACTORY } from './errors';\nimport { FirebaseApp } from './public-types';\nimport { HeartbeatsInIndexedDB } from './types';\nimport { logger } from './logger';\n\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\n\ninterface AppDB extends DBSchema {\n  'firebase-heartbeat-store': {\n    key: string;\n    value: HeartbeatsInIndexedDB;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<AppDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<AppDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB<AppDB>(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(AppError.IDB_OPEN, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\n\nexport async function readHeartbeatsFromIndexedDB(\n  app: FirebaseApp\n): Promise<HeartbeatsInIndexedDB | undefined> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_GET, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nexport async function writeHeartbeatsToIndexedDB(\n  app: FirebaseApp,\n  heartbeatObject: HeartbeatsInIndexedDB\n): Promise<void> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_WRITE, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.name}!${app.options.appId}`;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ComponentContainer } from '@firebase/component';\nimport {\n  base64urlEncodeWithoutPadding,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport {\n  readHeartbeatsFromIndexedDB,\n  writeHeartbeatsToIndexedDB\n} from './indexeddb';\nimport { FirebaseApp } from './public-types';\nimport {\n  HeartbeatsByUserAgent,\n  HeartbeatService,\n  HeartbeatsInIndexedDB,\n  HeartbeatStorage,\n  SingleDateHeartbeat\n} from './types';\nimport { logger } from './logger';\n\nconst MAX_HEADER_BYTES = 1024;\n// 30 days\nconst STORED_HEARTBEAT_RETENTION_MAX_MILLIS = 30 * 24 * 60 * 60 * 1000;\n\nexport class HeartbeatServiceImpl implements HeartbeatService {\n  /**\n   * The persistence layer for heartbeats\n   * Leave public for easier testing.\n   */\n  _storage: HeartbeatStorageImpl;\n\n  /**\n   * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n   * the header string.\n   * Stores one record per date. This will be consolidated into the standard\n   * format of one record per user agent string before being sent as a header.\n   * Populated from indexedDB when the controller is instantiated and should\n   * be kept in sync with indexedDB.\n   * Leave public for easier testing.\n   */\n  _heartbeatsCache: HeartbeatsInIndexedDB | null = null;\n\n  /**\n   * the initialization promise for populating heartbeatCache.\n   * If getHeartbeatsHeader() is called before the promise resolves\n   * (heartbeatsCache == null), it should wait for this promise\n   * Leave public for easier testing.\n   */\n  _heartbeatsCachePromise: Promise<HeartbeatsInIndexedDB>;\n  constructor(private readonly container: ComponentContainer) {\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  async triggerHeartbeat(): Promise<void> {\n    try {\n      const platformLogger = this.container\n        .getProvider('platform-logger')\n        .getImmediate();\n\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (this._heartbeatsCache?.heartbeats == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (this._heartbeatsCache?.heartbeats == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (\n        this._heartbeatsCache.lastSentHeartbeatDate === date ||\n        this._heartbeatsCache.heartbeats.some(\n          singleDateHeartbeat => singleDateHeartbeat.date === date\n        )\n      ) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({ date, agent });\n      }\n      // Remove entries older than 30 days.\n      this._heartbeatsCache.heartbeats =\n        this._heartbeatsCache.heartbeats.filter(singleDateHeartbeat => {\n          const hbTimestamp = new Date(singleDateHeartbeat.date).valueOf();\n          const now = Date.now();\n          return now - hbTimestamp <= STORED_HEARTBEAT_RETENTION_MAX_MILLIS;\n        });\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  async getHeartbeatsHeader(): Promise<string> {\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (\n        this._heartbeatsCache?.heartbeats == null ||\n        this._heartbeatsCache.heartbeats.length === 0\n      ) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(\n        this._heartbeatsCache.heartbeats\n      );\n      const headerString = base64urlEncodeWithoutPadding(\n        JSON.stringify({ version: 2, heartbeats: heartbeatsToSend })\n      );\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\n\nfunction getUTCDateString(): string {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\n\nexport function extractHeartbeatsForHeader(\n  heartbeatsCache: SingleDateHeartbeat[],\n  maxSize = MAX_HEADER_BYTES\n): {\n  heartbeatsToSend: HeartbeatsByUserAgent[];\n  unsentEntries: SingleDateHeartbeat[];\n} {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend: HeartbeatsByUserAgent[] = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(\n      hb => hb.agent === singleDateHeartbeat.agent\n    );\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\n\nexport class HeartbeatStorageImpl implements HeartbeatStorage {\n  private _canUseIndexedDBPromise: Promise<boolean>;\n  constructor(public app: FirebaseApp) {\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck(): Promise<boolean> {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable()\n        .then(() => true)\n        .catch(() => false);\n    }\n  }\n  /**\n   * Read all heartbeats.\n   */\n  async read(): Promise<HeartbeatsInIndexedDB> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return { heartbeats: [] };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject?.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return { heartbeats: [] };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [\n          ...existingHeartbeatsObject.heartbeats,\n          ...heartbeatsObject.heartbeats\n        ]\n      });\n    }\n  }\n}\n\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nexport function countBytes(heartbeatsCache: HeartbeatsByUserAgent[]): number {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })\n  ).length;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Component, ComponentType } from '@firebase/component';\nimport { PlatformLoggerServiceImpl } from './platformLoggerService';\nimport { name, version } from '../package.json';\nimport { _registerComponent } from './internal';\nimport { registerVersion } from './api';\nimport { HeartbeatServiceImpl } from './heartbeatService';\n\nexport function registerCoreComponents(variant?: string): void {\n  _registerComponent(\n    new Component(\n      'platform-logger',\n      container => new PlatformLoggerServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n  _registerComponent(\n    new Component(\n      'heartbeat',\n      container => new HeartbeatServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n\n  // Register `app` package.\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n", "/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerCoreComponents } from './registerCoreComponents';\n\nexport * from './api';\nexport * from './internal';\nexport * from './public-types';\n\nregisterCoreComponents('__RUNTIME_ENV__');\n"], "names": ["_a", "appName", "appCompatName", "analyticsName", "analyticsCompatName", "appCheckName", "appCheckCompatName", "authName", "authCompatName", "databaseName", "dataconnectName", "databaseCompatName", "functionsName", "functionsCompatName", "installationsName", "installationsCompatName", "messagingName", "messagingCompatName", "performanceName", "performanceCompatName", "remoteConfigName", "remoteConfigCompatName", "storageName", "storageCompatName", "firestoreName", "firestoreCompatName", "vertexName", "packageName", "version", "setLogLevelImpl", "name"], "mappings": ";;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAUH,IAAA,yBAAA,kBAAA,YAAA;AACE,IAAA,SAAA,yBAAA,CAA6B,SAA6B,EAAA;QAA7B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;KAAI;;;AAG9D,IAAA,yBAAA,CAAA,SAAA,CAAA,qBAAqB,GAArB,YAAA;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;;;AAGhD,QAAA,OAAO,SAAS;aACb,GAAG,CAAC,UAAA,QAAQ,EAAA;AACX,YAAA,IAAI,wBAAwB,CAAC,QAAQ,CAAC,EAAE;AACtC,gBAAA,IAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,EAAoB,CAAC;gBAC1D,OAAO,EAAA,CAAA,MAAA,CAAG,OAAO,CAAC,OAAO,cAAI,OAAO,CAAC,OAAO,CAAE,CAAC;AAChD,aAAA;AAAM,iBAAA;AACL,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AACH,SAAC,CAAC;aACD,MAAM,CAAC,UAAA,SAAS,EAAA,EAAI,OAAA,SAAS,CAAA,EAAA,CAAC;aAC9B,IAAI,CAAC,GAAG,CAAC,CAAC;KACd,CAAA;IACH,OAAC,yBAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AACD;;;;;;;AAOG;AACH,SAAS,wBAAwB,CAAC,QAAwB,EAAA;AACxD,IAAA,IAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;IAC1C,OAAO,CAAA,SAAS,KAAT,IAAA,IAAA,SAAS,uBAAT,SAAS,CAAE,IAAI,MAAA,SAAA,6BAA2B;AACnD;;;;;ACzDA;;;;;;;;;;;;;;;AAeG;AAII,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBjD;;;;;;;;;;;;;;;AAeG;;AA8BH;;;;AAIG;AACI,IAAM,kBAAkB,GAAG,YAAY;AAEvC,IAAM,mBAAmB,IAAGA,IAAA,GAAA,EAAA;IACjCA,IAAC,CAAAC,MAAO,IAAG,WAAW;IACtBD,IAAC,CAAAE,MAAa,IAAG,kBAAkB;IACnCF,IAAC,CAAAG,MAAa,IAAG,gBAAgB;IACjCH,IAAC,CAAAI,MAAmB,IAAG,uBAAuB;IAC9CJ,IAAC,CAAAK,MAAY,IAAG,gBAAgB;IAChCL,IAAC,CAAAM,MAAkB,IAAG,uBAAuB;IAC7CN,IAAC,CAAAO,MAAQ,IAAG,WAAW;IACvBP,IAAC,CAAAQ,MAAc,IAAG,kBAAkB;IACpCR,IAAC,CAAAS,MAAY,IAAG,WAAW;IAC3BT,IAAC,CAAAU,MAAe,IAAG,mBAAmB;IACtCV,IAAC,CAAAW,MAAkB,IAAG,kBAAkB;IACxCX,IAAC,CAAAY,MAAa,IAAG,SAAS;IAC1BZ,IAAC,CAAAa,MAAmB,IAAG,gBAAgB;IACvCb,IAAC,CAAAc,MAAiB,IAAG,UAAU;IAC/Bd,IAAC,CAAAe,MAAuB,IAAG,iBAAiB;IAC5Cf,IAAC,CAAAgB,MAAa,IAAG,UAAU;IAC3BhB,IAAC,CAAAiB,MAAmB,IAAG,iBAAiB;IACxCjB,IAAC,CAAAkB,MAAe,IAAG,WAAW;IAC9BlB,IAAC,CAAAmB,MAAqB,IAAG,kBAAkB;IAC3CnB,IAAC,CAAAoB,MAAgB,IAAG,SAAS;IAC7BpB,IAAC,CAAAqB,MAAsB,IAAG,gBAAgB;IAC1CrB,IAAC,CAAAsB,MAAW,IAAG,UAAU;IACzBtB,IAAC,CAAAuB,MAAiB,IAAG,iBAAiB;IACtCvB,IAAC,CAAAwB,MAAa,IAAG,UAAU;IAC3BxB,IAAC,CAAAyB,MAAmB,IAAG,iBAAiB;IACxCzB,IAAC,CAAA0B,MAAU,IAAG,aAAa;AAC3B,IAAA1B,IAAA,CAAA,SAAA,CAAS,GAAE,SAAS;IACpBA,IAAC,CAAA2B,IAAW,IAAG,aAAa;AACpB,IAAA3B,IAAA,CAAA;;ACjFV;;;;;;;;;;;;;;;AAeG;AAaH;;AAEG;AACU,IAAA,KAAK,GAAG,IAAI,GAAG,GAAwB;AAEpD;;AAEG;AACU,IAAA,WAAW,GAAG,IAAI,GAAG,GAA8B;AAEhE;;;;AAIG;AACH;AACa,IAAA,WAAW,GAAG,IAAI,GAAG,GAA2B;AAE7D;;;;AAIG;AACa,SAAA,aAAa,CAC3B,GAAgB,EAChB,SAAuB,EAAA;IAEvB,IAAI;AACD,QAAA,GAAuB,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAC5D,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,CAAC,KAAK,CACV,YAAA,CAAA,MAAA,CAAa,SAAS,CAAC,IAAI,EAAwC,uCAAA,CAAA,CAAA,MAAA,CAAA,GAAG,CAAC,IAAI,CAAE,EAC7E,CAAC,CACF,CAAC;AACH,KAAA;AACH,CAAC;AAED;;;AAGG;AACa,SAAA,wBAAwB,CACtC,GAAgB,EAChB,SAAoB,EAAA;AAEnB,IAAA,GAAuB,CAAC,SAAS,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;AACxE,CAAC;AAED;;;;;;AAMG;AACG,SAAU,kBAAkB,CAChC,SAAuB,EAAA;;AAEvB,IAAA,IAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC;AACrC,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;AAClC,QAAA,MAAM,CAAC,KAAK,CACV,6DAAsD,aAAa,EAAA,GAAA,CAAG,CACvE,CAAC;AAEF,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,WAAW,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;;;QAG1C,KAAkB,IAAA,KAAA,QAAA,CAAA,KAAK,CAAC,MAAM,EAAE,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAE,CAAA,EAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA;AAA7B,YAAA,IAAM,GAAG,GAAA,EAAA,CAAA,KAAA,CAAA;AACZ,YAAA,aAAa,CAAC,GAAsB,EAAE,SAAS,CAAC,CAAC;AAClD,SAAA;;;;;;;;;;QAED,KAAwB,IAAA,KAAA,QAAA,CAAA,WAAW,CAAC,MAAM,EAAE,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAE,CAAA,EAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA;AAAzC,YAAA,IAAM,SAAS,GAAA,EAAA,CAAA,KAAA,CAAA;AAClB,YAAA,aAAa,CAAC,SAAkC,EAAE,SAAS,CAAC,CAAC;AAC9D,SAAA;;;;;;;;;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,YAAY,CAC1B,GAAgB,EAChB,IAAO,EAAA;AAEP,IAAA,IAAM,mBAAmB,GAAI,GAAuB,CAAC,SAAS;SAC3D,WAAW,CAAC,WAAW,CAAC;AACxB,SAAA,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AACpC,IAAA,IAAI,mBAAmB,EAAE;AACvB,QAAA,KAAK,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;AAC7C,KAAA;IACD,OAAQ,GAAuB,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC9D,CAAC;AAED;;;;;;;AAOG;SACa,sBAAsB,CACpC,GAAgB,EAChB,IAAO,EACP,kBAA+C,EAAA;AAA/C,IAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,kBAA+C,GAAA,kBAAA,CAAA,EAAA;IAE/C,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,cAAc,CAC5B,GAAkC,EAAA;AAElC,IAAA,OAAQ,GAAmB,CAAC,OAAO,KAAK,SAAS,CAAC;AACpD,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,oBAAoB,CAClC,GAAoC,EAAA;AAEpC,IAAA,OAAQ,GAAyB,CAAC,QAAQ,KAAK,SAAS,CAAC;AAC3D,CAAC;AAED;;;;AAIG;SACa,gBAAgB,GAAA;IAC9B,WAAW,CAAC,KAAK,EAAE,CAAC;AACtB;;ACtLA;;;;;;;;;;;;;;;AAeG;;AAqBH,IAAM,MAAM,IAAA,EAAA,GAAA,EAAA;AACV,IAAA,EAAA,CAAA,QAAA,uBAAA,GACE,kDAAkD;QAClD,4BAA4B;AAC9B,IAAA,EAAA,CAAA,cAAA,6BAAA,GAAyB,gCAAgC;AACzD,IAAA,EAAA,CAAA,eAAA,8BAAA,GACE,iFAAiF;AACnF,IAAA,EAAA,CAAA,aAAA,4BAAA,GAAwB,iDAAiD;AACzE,IAAA,EAAA,CAAA,oBAAA,mCAAA,GAA+B,sCAAsC;AACrE,IAAA,EAAA,CAAA,YAAA,2BAAA,GACE,yEAAyE;AAC3E,IAAA,EAAA,CAAA,sBAAA,qCAAA,GACE,sDAAsD;QACtD,wBAAwB;AAC1B,IAAA,EAAA,CAAA,sBAAA,qCAAA,GACE,uDAAuD;AACzD,IAAA,EAAA,CAAA,UAAA,yBAAA,GACE,+EAA+E;AACjF,IAAA,EAAA,CAAA,SAAA,wBAAA,GACE,oFAAoF;AACtF,IAAA,EAAA,CAAA,SAAA,0BAAA,GACE,kFAAkF;AACpF,IAAA,EAAA,CAAA,YAAA,2BAAA,GACE,qFAAqF;AACvF,IAAA,EAAA,CAAA,qCAAA,oDAAA,GACE,yGAAyG;AAC3G,IAAA,EAAA,CAAA,gCAAA,+CAAA,GACE,2DAA2D;OAC9D,CAAC;AAeK,IAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,KAAK,EACL,UAAU,EACV,MAAM,CACP;;ACnFD;;;;;;;;;;;;;;;AAeG;AAcH,IAAA,eAAA,kBAAA,YAAA;AAcE,IAAA,SAAA,eAAA,CACE,OAAwB,EACxB,MAAqC,EACrC,SAA6B,EAAA;QAH/B,IAcC,KAAA,GAAA,IAAA,CAAA;QAjBS,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;AAQ3B,QAAA,IAAI,CAAC,QAAQ,GAAQ,QAAA,CAAA,EAAA,EAAA,OAAO,CAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,GAAQ,QAAA,CAAA,EAAA,EAAA,MAAM,CAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,+BAA+B;YAClC,MAAM,CAAC,8BAA8B,CAAC;AACxC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CACzB,IAAI,SAAS,CAAC,KAAK,EAAE,cAAM,OAAA,KAAI,GAAA,EAAA,QAAA,4BAAuB,CACvD,CAAC;KACH;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,eAA8B,CAAA,SAAA,EAAA,gCAAA,EAAA;AAAlC,QAAA,GAAA,EAAA,YAAA;YACE,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,+BAA+B,CAAC;SAC7C;AAED,QAAA,GAAA,EAAA,UAAmC,GAAY,EAAA;YAC7C,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,+BAA+B,GAAG,GAAG,CAAC;SAC5C;;;AALA,KAAA,CAAA,CAAA;AAOD,IAAA,MAAA,CAAA,cAAA,CAAI,eAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAAR,QAAA,GAAA,EAAA,YAAA;YACE,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,KAAK,CAAC;SACnB;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,eAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAAX,QAAA,GAAA,EAAA,YAAA;YACE,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,eAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAAV,QAAA,GAAA,EAAA,YAAA;YACE,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,OAAO,CAAC;SACrB;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,eAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AAAb,QAAA,GAAA,EAAA,YAAA;YACE,OAAO,IAAI,CAAC,UAAU,CAAC;SACxB;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,eAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AAAb,QAAA,GAAA,EAAA,YAAA;YACE,OAAO,IAAI,CAAC,UAAU,CAAC;SACxB;AAED,QAAA,GAAA,EAAA,UAAc,GAAY,EAAA;AACxB,YAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;SACvB;;;AAJA,KAAA,CAAA,CAAA;AAMD;;;AAGG;AACO,IAAA,eAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,YAAA;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,aAAA,6BAAuB,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AAC3E,SAAA;KACF,CAAA;IACH,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;ACzGD;;;;;;;;;;;;;;;AAeG;AAcH,IAAA,qBAAA,kBAAA,UAAA,MAAA,EAAA;IACU,SAAe,CAAA,qBAAA,EAAA,MAAA,CAAA,CAAA;AAOvB,IAAA,SAAA,qBAAA,CACE,OAA0C,EAC1C,YAAuC,EACvC,IAAY,EACZ,SAA6B,EAAA;QAJ/B,IAgDC,KAAA,GAAA,IAAA,CAAA;;AAzCC,QAAA,IAAM,8BAA8B,GAClC,YAAY,CAAC,8BAA8B,KAAK,SAAS;cACrD,YAAY,CAAC,8BAA8B;cAC3C,KAAK,CAAC;;AAGZ,QAAA,IAAM,MAAM,GAAkC;AAC5C,YAAA,IAAI,EAAA,IAAA;AACJ,YAAA,8BAA8B,EAAA,8BAAA;SAC/B,CAAC;AAEF,QAAA,IAAK,OAA2B,CAAC,MAAM,KAAK,SAAS,EAAE;;AAErD,YAAA,KAAA,GAAA,kBAAM,OAA0B,EAAE,MAAM,EAAE,SAAS,CAAC,IAAC,IAAA,CAAA;AACtD,SAAA;AAAM,aAAA;YACL,IAAM,OAAO,GAAoB,OAA0B,CAAC;YAC5D,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,IAAC,IAAA,CAAA;AAC3C,SAAA;;QAGD,KAAI,CAAC,aAAa,GAChB,QAAA,CAAA,EAAA,8BAA8B,gCAAA,EAC3B,EAAA,YAAY,CAChB,CAAC;AAEF,QAAA,KAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAClC,QAAA,IAAI,OAAO,oBAAoB,KAAK,WAAW,EAAE;AAC/C,YAAA,KAAI,CAAC,qBAAqB,GAAG,IAAI,oBAAoB,CAAC,YAAA;gBACpD,KAAI,CAAC,gBAAgB,EAAE,CAAC;AAC1B,aAAC,CAAC,CAAC;AACJ,SAAA;AAED,QAAA,KAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,KAAI,CAAC,WAAW,CAAC,KAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;;;AAIpD,QAAA,KAAI,CAAC,aAAa,CAAC,cAAc,GAAG,SAAS,CAAC;AAC9C,QAAA,YAAY,CAAC,cAAc,GAAG,SAAS,CAAC;AAExC,QAAA,eAAe,CAAC2B,MAAW,EAAEC,SAAO,EAAE,WAAW,CAAC,CAAC;;KACpD;AAED,IAAA,qBAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;AACE,QAAA,OAAO,SAAS,CAAC;KAClB,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,qBAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAAZ,QAAA,GAAA,EAAA,YAAA;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;;;AAAA,KAAA,CAAA,CAAA;;;IAID,qBAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,GAAuB,EAAA;QACjC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;AACR,SAAA;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;YAC5D,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAChD,SAAA;KACF,CAAA;;AAGD,IAAA,qBAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,CAAC,CAAC;AACV,SAAA;AACD,QAAA,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;KACzB,CAAA;;;;AAKO,IAAA,qBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAxB,YAAA;AACE,QAAA,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC;KACtB,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,qBAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAAZ,QAAA,GAAA,EAAA,YAAA;YACE,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,aAAa,CAAC;SAC3B;;;AAAA,KAAA,CAAA,CAAA;AAED;;;AAGG;AACO,IAAA,qBAAA,CAAA,SAAA,CAAA,cAAc,GAAxB,YAAA;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,oBAAA,mCAA6B,CAAC;AACzD,SAAA;KACF,CAAA;IACH,OAAC,qBAAA,CAAA;AAAD,CA3GA,CACU,eAAe,CA0GxB,CAAA;;ACxID;;;;;;;;;;;;;;;AAeG;AA4CH;;;;AAIG;AACI,IAAM,WAAW,GAAG,QAAQ;AAoEnB,SAAA,aAAa,CAC3B,QAA0B,EAC1B,SAAc,EAAA;;AAAd,IAAA,IAAA,SAAA,KAAA,KAAA,CAAA,EAAA,EAAA,SAAc,GAAA,EAAA,CAAA,EAAA;IAEd,IAAI,OAAO,GAAG,QAAQ,CAAC;AAEvB,IAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QACjC,IAAM,MAAI,GAAG,SAAS,CAAC;AACvB,QAAA,SAAS,GAAG,EAAE,IAAI,EAAA,MAAA,EAAE,CAAC;AACtB,KAAA;AAED,IAAA,IAAM,MAAM,GAAA,QAAA,CAAA,EACV,IAAI,EAAE,kBAAkB,EACxB,8BAA8B,EAAE,KAAK,EAAA,EAClC,SAAS,CACb,CAAC;AACF,IAAA,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;AAEzB,IAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,EAAE;QACrC,MAAM,aAAa,CAAC,MAAM,CAAwB,cAAA,8BAAA;AAChD,YAAA,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC;AACtB,SAAA,CAAC,CAAC;AACJ,KAAA;AAED,IAAA,OAAO,KAAP,OAAO,GAAK,mBAAmB,EAAE,CAAC,CAAA;IAElC,IAAI,CAAC,OAAO,EAAE;AACZ,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,YAAA,2BAAqB,CAAC;AACjD,KAAA;IAED,IAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAoB,CAAC;AACvD,IAAA,IAAI,WAAW,EAAE;;AAEf,QAAA,IACE,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC;AACvC,YAAA,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,EACrC;AACA,YAAA,OAAO,WAAW,CAAC;AACpB,SAAA;AAAM,aAAA;YACL,MAAM,aAAa,CAAC,MAAM,CAAyB,eAAA,+BAAA,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE,SAAA;AACF,KAAA;AAED,IAAA,IAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;;QAC/C,KAAwB,IAAA,KAAA,QAAA,CAAA,WAAW,CAAC,MAAM,EAAE,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAE,CAAA,EAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA;AAAzC,YAAA,IAAM,SAAS,GAAA,EAAA,CAAA,KAAA,CAAA;AAClB,YAAA,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACnC,SAAA;;;;;;;;;IAED,IAAM,MAAM,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;AAE/D,IAAA,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAExB,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAgDe,SAAA,mBAAmB,CACjC,QAAuC,EACvC,gBAA2C,EAAA;;AAE3C,IAAA,IAAI,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;;AAEjC,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,gCAAA,+CAAyC,CAAC;AACrE,KAAA;AAED,IAAA,IAAI,gBAAgB,CAAC,8BAA8B,KAAK,SAAS,EAAE;AACjE,QAAA,gBAAgB,CAAC,8BAA8B,GAAG,KAAK,CAAC;AACzD,KAAA;AAED,IAAA,IAAI,UAA2B,CAAC;AAChC,IAAA,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE;AAC5B,QAAA,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC;AAC/B,KAAA;AAAM,SAAA;QACL,UAAU,GAAG,QAAQ,CAAC;AACvB,KAAA;;AAGD,IAAA,IAAM,OAAO,GACR,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,gBAAgB,CAChB,EAAA,UAAU,CACd,CAAC;;;AAIF,IAAA,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;QACxC,OAAO,OAAO,CAAC,cAAc,CAAC;AAC/B,KAAA;IAED,IAAM,QAAQ,GAAG,UAAC,CAAS,EAAA;AACzB,QAAA,OAAO,aAAI,CAAA,EAAA,EAAA,MAAA,CAAA,CAAC,CAAE,EAAA,KAAA,CAAA,CAAA,MAAM,CAClB,UAAC,IAAI,EAAE,CAAC,IAAK,OAAA,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAA3C,EAA2C,EACxD,CAAC,CACF,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,IAAI,gBAAgB,CAAC,cAAc,KAAK,SAAS,EAAE;AACjD,QAAA,IAAI,OAAO,oBAAoB,KAAK,WAAW,EAAE;AAC/C,YAAA,MAAM,aAAa,CAAC,MAAM,CAExB,qCAAA,qDAAA,EAAE,CACH,CAAC;AACH,SAAA;AACF,KAAA;AAED,IAAA,IAAM,UAAU,GAAG,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IAC1D,IAAM,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAsB,CAAC;AACrE,IAAA,IAAI,WAAW,EAAE;AACd,QAAA,WAAqC,CAAC,WAAW,CAChD,gBAAgB,CAAC,cAAc,CAChC,CAAC;AACF,QAAA,OAAO,WAAW,CAAC;AACpB,KAAA;AAED,IAAA,IAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC;;QACrD,KAAwB,IAAA,KAAA,QAAA,CAAA,WAAW,CAAC,MAAM,EAAE,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAE,CAAA,EAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA;AAAzC,YAAA,IAAM,SAAS,GAAA,EAAA,CAAA,KAAA,CAAA;AAClB,YAAA,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACnC,SAAA;;;;;;;;;AAED,IAAA,IAAM,MAAM,GAAG,IAAI,qBAAqB,CACtC,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,SAAS,CACV,CAAC;AAEF,IAAA,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAEpC,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACG,SAAU,MAAM,CAAC,IAAiC,EAAA;AAAjC,IAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAiC,GAAA,kBAAA,CAAA,EAAA;IACtD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5B,IAAI,CAAC,GAAG,IAAI,IAAI,KAAK,kBAAkB,IAAI,mBAAmB,EAAE,EAAE;QAChE,OAAO,aAAa,EAAE,CAAC;AACxB,KAAA;IACD,IAAI,CAAC,GAAG,EAAE;QACR,MAAM,aAAa,CAAC,MAAM,CAAkB,QAAA,wBAAA,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAChE,KAAA;AAED,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;AAGG;SACa,OAAO,GAAA;IACrB,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;AACpC,CAAC;AAED;;;;;;;;;;;;;;;;AAgBG;AACG,SAAgB,SAAS,CAAC,GAAgB,EAAA;;;;;;oBAC1C,gBAAgB,GAAG,KAAK,CAAC;AACvB,oBAAA,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AACtB,oBAAA,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBACnB,gBAAgB,GAAG,IAAI,CAAC;AACxB,wBAAA,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpB,qBAAA;AAAM,yBAAA,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBAC1B,iBAAiB,GAAG,GAA4B,CAAC;AACvD,wBAAA,IAAI,iBAAiB,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;AACxC,4BAAA,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;4BACzB,gBAAgB,GAAG,IAAI,CAAC;AACzB,yBAAA;AACF,qBAAA;AAEG,oBAAA,IAAA,CAAA,gBAAgB,EAAhB,OAAgB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAClB,oBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,GAAG,CACd,GAAuB,CAAC,SAAS;AAC/B,6BAAA,YAAY,EAAE;AACd,6BAAA,GAAG,CAAC,UAAA,QAAQ,EAAA,EAAI,OAAA,QAAQ,CAAC,MAAM,EAAE,CAAA,EAAA,CAAC,CACtC,CAAA,CAAA;;AAJD,oBAAA,EAAA,CAAA,IAAA,EAIC,CAAC;AACD,oBAAA,GAAuB,CAAC,SAAS,GAAG,IAAI,CAAC;;;;;;AAE7C,CAAA;AAED;;;;;;;AAOG;SACa,eAAe,CAC7B,gBAAwB,EACxB,OAAe,EACf,OAAgB,EAAA;;;;IAIhB,IAAI,OAAO,GAAG,CAAA,EAAA,GAAA,mBAAmB,CAAC,gBAAgB,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,gBAAgB,CAAC;AACxE,IAAA,IAAI,OAAO,EAAE;AACX,QAAA,OAAO,IAAI,GAAA,CAAA,MAAA,CAAI,OAAO,CAAE,CAAC;AAC1B,KAAA;IACD,IAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/C,IAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/C,IAAI,eAAe,IAAI,eAAe,EAAE;AACtC,QAAA,IAAM,OAAO,GAAG;YACd,+BAA+B,CAAA,MAAA,CAAA,OAAO,EAAmB,oBAAA,CAAA,CAAA,MAAA,CAAA,OAAO,EAAI,KAAA,CAAA;SACrE,CAAC;AACF,QAAA,IAAI,eAAe,EAAE;AACnB,YAAA,OAAO,CAAC,IAAI,CACV,yBAAiB,OAAO,EAAA,sDAAA,CAAmD,CAC5E,CAAC;AACH,SAAA;QACD,IAAI,eAAe,IAAI,eAAe,EAAE;AACtC,YAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,SAAA;AACD,QAAA,IAAI,eAAe,EAAE;AACnB,YAAA,OAAO,CAAC,IAAI,CACV,yBAAiB,OAAO,EAAA,sDAAA,CAAmD,CAC5E,CAAC;AACH,SAAA;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/B,OAAO;AACR,KAAA;IACD,kBAAkB,CAChB,IAAI,SAAS,CACX,UAAG,OAAO,EAAA,UAAA,CAAkB,EAC5B,YAAA,EAAM,QAAC,EAAE,OAAO,EAAA,OAAA,EAAE,OAAO,EAAA,OAAA,EAAE,EAArB,EAAsB,EAE7B,SAAA,6BAAA,CACF,CAAC;AACJ,CAAC;AAED;;;;;;AAMG;AACa,SAAA,KAAK,CACnB,WAA+B,EAC/B,OAAoB,EAAA;IAEpB,IAAI,WAAW,KAAK,IAAI,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;AAC7D,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,sBAAA,qCAA+B,CAAC;AAC3D,KAAA;AACD,IAAA,iBAAiB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,WAAW,CAAC,QAAwB,EAAA;IAClDC,aAAe,CAAC,QAAQ,CAAC,CAAC;AAC5B;;AC3dA;;;;;;;;;;;;;;;AAeG;AASH,IAAM,OAAO,GAAG,6BAA6B,CAAC;AAC9C,IAAM,UAAU,GAAG,CAAC,CAAC;AACrB,IAAM,UAAU,GAAG,0BAA0B,CAAC;AAS9C,IAAI,SAAS,GAAwC,IAAI,CAAC;AAC1D,SAAS,YAAY,GAAA;IACnB,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,SAAS,GAAG,MAAM,CAAQ,OAAO,EAAE,UAAU,EAAE;AAC7C,YAAA,OAAO,EAAE,UAAC,EAAE,EAAE,UAAU,EAAA;;;;;;AAMtB,gBAAA,QAAQ,UAAU;AAChB,oBAAA,KAAK,CAAC;wBACJ,IAAI;AACF,4BAAA,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;AAClC,yBAAA;AAAC,wBAAA,OAAO,CAAC,EAAE;;;;AAIV,4BAAA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACjB,yBAAA;AACJ,iBAAA;aACF;AACF,SAAA,CAAC,CAAC,KAAK,CAAC,UAAA,CAAC,EAAA;YACR,MAAM,aAAa,CAAC,MAAM,CAAoB,UAAA,0BAAA;gBAC5C,oBAAoB,EAAE,CAAC,CAAC,OAAO;AAChC,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AACJ,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEK,SAAgB,2BAA2B,CAC/C,GAAgB,EAAA;;;;;;;oBAGH,OAAM,CAAA,CAAA,YAAA,YAAY,EAAE,CAAA,CAAA;;AAAzB,oBAAA,EAAE,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;AACzB,oBAAA,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AACvB,oBAAA,OAAA,CAAA,CAAA,YAAM,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA,CAAA;;AAA9D,oBAAA,MAAM,GAAG,EAAqD,CAAA,IAAA,EAAA,CAAA;;;oBAGpE,OAAM,CAAA,CAAA,YAAA,EAAE,CAAC,IAAI,CAAA,CAAA;;;;AAAb,oBAAA,EAAA,CAAA,IAAA,EAAa,CAAC;AACd,oBAAA,OAAA,CAAA,CAAA,aAAO,MAAM,CAAC,CAAA;;;oBAEd,IAAI,GAAC,YAAY,aAAa,EAAE;AAC9B,wBAAA,MAAM,CAAC,IAAI,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC;AACxB,qBAAA;AAAM,yBAAA;AACC,wBAAA,WAAW,GAAG,aAAa,CAAC,MAAM,CAAmB,SAAA,yBAAA;AACzD,4BAAA,oBAAoB,EAAG,GAAW,KAAA,IAAA,IAAX,GAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAC,CAAY,OAAO;AAC5C,yBAAA,CAAC,CAAC;AACH,wBAAA,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAClC,qBAAA;;;;;;AAEJ,CAAA;AAEqB,SAAA,0BAA0B,CAC9C,GAAgB,EAChB,eAAsC,EAAA;;;;;;;oBAGzB,OAAM,CAAA,CAAA,YAAA,YAAY,EAAE,CAAA,CAAA;;AAAzB,oBAAA,EAAE,GAAG,EAAoB,CAAA,IAAA,EAAA,CAAA;oBACzB,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AAC7C,oBAAA,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;oBAC/C,OAAM,CAAA,CAAA,YAAA,WAAW,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA,CAAA;;AAAvD,oBAAA,EAAA,CAAA,IAAA,EAAuD,CAAC;oBACxD,OAAM,CAAA,CAAA,YAAA,EAAE,CAAC,IAAI,CAAA,CAAA;;AAAb,oBAAA,EAAA,CAAA,IAAA,EAAa,CAAC;;;;oBAEd,IAAI,GAAC,YAAY,aAAa,EAAE;AAC9B,wBAAA,MAAM,CAAC,IAAI,CAAC,GAAC,CAAC,OAAO,CAAC,CAAC;AACxB,qBAAA;AAAM,yBAAA;AACC,wBAAA,WAAW,GAAG,aAAa,CAAC,MAAM,CAAqB,SAAA,2BAAA;AAC3D,4BAAA,oBAAoB,EAAG,GAAW,KAAA,IAAA,IAAX,GAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAC,CAAY,OAAO;AAC5C,yBAAA,CAAC,CAAC;AACH,wBAAA,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAClC,qBAAA;;;;;;AAEJ,CAAA;AAED,SAAS,UAAU,CAAC,GAAgB,EAAA;IAClC,OAAO,EAAA,CAAA,MAAA,CAAG,GAAG,CAAC,IAAI,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAE,CAAC;AAC5C;;ACjHA;;;;;;;;;;;;;;;AAeG;AAsBH,IAAM,gBAAgB,GAAG,IAAI,CAAC;AAC9B;AACA,IAAM,qCAAqC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAEvE,IAAA,oBAAA,kBAAA,YAAA;AAyBE,IAAA,SAAA,oBAAA,CAA6B,SAA6B,EAAA;QAA1D,IAOC,KAAA,GAAA,IAAA,CAAA;QAP4B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;AAlB1D;;;;;;;;AAQG;QACH,IAAgB,CAAA,gBAAA,GAAiC,IAAI,CAAC;AAUpD,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,UAAA,MAAM,EAAA;AAC7D,YAAA,KAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;AAC/B,YAAA,OAAO,MAAM,CAAC;AAChB,SAAC,CAAC,CAAC;KACJ;AAED;;;;;;AAMG;AACG,IAAA,oBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAtB,YAAA;;;;;;;;wBAEU,cAAc,GAAG,IAAI,CAAC,SAAS;6BAClC,WAAW,CAAC,iBAAiB,CAAC;AAC9B,6BAAA,YAAY,EAAE,CAAC;AAIZ,wBAAA,KAAK,GAAG,cAAc,CAAC,qBAAqB,EAAE,CAAC;wBAC/C,MAAO,GAAA,gBAAgB,EAAE,CAAC;8BAC5B,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,KAAI,IAAI,CAAA,EAAzC,OAAyC,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3C,wBAAA,EAAA,GAAA,IAAI,CAAA;wBAAoB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,uBAAuB,CAAA,CAAA;;wBAA1D,EAAK,CAAA,gBAAgB,GAAG,EAAA,CAAA,IAAA,EAAkC,CAAC;;wBAE3D,IAAI,CAAA,MAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,KAAI,IAAI,EAAE;4BAC7C,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,yBAAA;;;;;AAIH,wBAAA,IACE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,KAAK,MAAI;AACpD,4BAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CACnC,UAAA,mBAAmB,IAAI,OAAA,mBAAmB,CAAC,IAAI,KAAK,MAAI,CAAjC,EAAiC,CACzD,EACD;4BACA,OAAO,CAAA,CAAA,YAAA,CAAA;AACR,yBAAA;AAAM,6BAAA;;AAEL,4BAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,QAAA,EAAE,KAAK,EAAA,KAAA,EAAE,CAAC,CAAC;AACxD,yBAAA;;wBAED,IAAI,CAAC,gBAAgB,CAAC,UAAU;4BAC9B,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,UAAA,mBAAmB,EAAA;AACzD,gCAAA,IAAM,WAAW,GAAG,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;AACjE,gCAAA,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,gCAAA,OAAO,GAAG,GAAG,WAAW,IAAI,qCAAqC,CAAC;AACpE,6BAAC,CAAC,CAAC;wBACL,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAA;;;AAEtD,wBAAA,MAAM,CAAC,IAAI,CAAC,GAAC,CAAC,CAAC;;;;;;AAElB,KAAA,CAAA;AAED;;;;;;AAMG;AACG,IAAA,oBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAzB,YAAA;;;;;;;;AAEQ,wBAAA,IAAA,EAAA,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAA,EAA9B,OAA8B,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBAChC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,uBAAuB,CAAA,CAAA;;AAAlC,wBAAA,EAAA,CAAA,IAAA,EAAkC,CAAC;;;;wBAGrC,IACE,CAAA,MAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,KAAI,IAAI;4BACzC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAC7C;AACA,4BAAA,OAAA,CAAA,CAAA,aAAO,EAAE,CAAC,CAAA;AACX,yBAAA;wBACK,IAAI,GAAG,gBAAgB,EAAE,CAAC;AAE1B,wBAAA,EAAA,GAAsC,0BAA0B,CACpE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACjC,EAFO,gBAAgB,GAAA,EAAA,CAAA,gBAAA,EAAE,aAAa,mBAAA,CAErC;AACI,wBAAA,YAAY,GAAG,6BAA6B,CAChD,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,gBAAgB,EAAE,CAAC,CAC7D,CAAC;;AAEF,wBAAA,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAC/C,wBAAA,IAAA,EAAA,aAAa,CAAC,MAAM,GAAG,CAAC,CAAA,EAAxB,OAAwB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;AAE1B,wBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,aAAa,CAAC;;;;wBAIjD,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA,CAAA;;;;;AAApD,wBAAA,EAAA,CAAA,IAAA,EAAoD,CAAC;;;AAErD,wBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,EAAE,CAAC;;wBAEtC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;;AAEtD,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,YAAY,CAAC,CAAA;;;AAEpB,wBAAA,MAAM,CAAC,IAAI,CAAC,GAAC,CAAC,CAAC;AACf,wBAAA,OAAA,CAAA,CAAA,aAAO,EAAE,CAAC,CAAA;;;;;AAEb,KAAA,CAAA;IACH,OAAC,oBAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED,SAAS,gBAAgB,GAAA;AACvB,IAAA,IAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;;IAEzB,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9C,CAAC;AAEe,SAAA,0BAA0B,CACxC,eAAsC,EACtC,OAA0B,EAAA;;AAA1B,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA0B,GAAA,gBAAA,CAAA,EAAA;;;IAO1B,IAAM,gBAAgB,GAA4B,EAAE,CAAC;;AAErD,IAAA,IAAI,aAAa,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;4BACjC,mBAAmB,EAAA;;QAE5B,IAAM,cAAc,GAAG,gBAAgB,CAAC,IAAI,CAC1C,UAAA,EAAE,EAAI,EAAA,OAAA,EAAE,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK,CAAtC,EAAsC,CAC7C,CAAC;QACF,IAAI,CAAC,cAAc,EAAE;;YAEnB,gBAAgB,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,mBAAmB,CAAC,KAAK;AAChC,gBAAA,KAAK,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC;AAClC,aAAA,CAAC,CAAC;AACH,YAAA,IAAI,UAAU,CAAC,gBAAgB,CAAC,GAAG,OAAO,EAAE;;;gBAG1C,gBAAgB,CAAC,GAAG,EAAE,CAAC;;AAExB,aAAA;AACF,SAAA;AAAM,aAAA;YACL,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;;;AAGpD,YAAA,IAAI,UAAU,CAAC,gBAAgB,CAAC,GAAG,OAAO,EAAE;AAC1C,gBAAA,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;;AAE5B,aAAA;AACF,SAAA;;;AAGD,QAAA,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;;AA5BzC,QAAA,KAAkC,IAAA,iBAAA,GAAA,QAAA,CAAA,eAAe,CAAA,EAAA,mBAAA,GAAA,iBAAA,CAAA,IAAA,EAAA,EAAA,CAAA,mBAAA,CAAA,IAAA,EAAA,mBAAA,GAAA,iBAAA,CAAA,IAAA,EAAA,EAAA;AAA5C,YAAA,IAAM,mBAAmB,GAAA,mBAAA,CAAA,KAAA,CAAA;kCAAnB,mBAAmB,CAAA,CAAA;;;AA6B7B,SAAA;;;;;;;;;IACD,OAAO;AACL,QAAA,gBAAgB,EAAA,gBAAA;AAChB,QAAA,aAAa,EAAA,aAAA;KACd,CAAC;AACJ,CAAC;AAED,IAAA,oBAAA,kBAAA,YAAA;AAEE,IAAA,SAAA,oBAAA,CAAmB,GAAgB,EAAA;QAAhB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;AACjC,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACpE;AACK,IAAA,oBAAA,CAAA,SAAA,CAAA,4BAA4B,GAAlC,YAAA;;;gBACE,IAAI,CAAC,oBAAoB,EAAE,EAAE;AAC3B,oBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;AACd,iBAAA;AAAM,qBAAA;AACL,oBAAA,OAAA,CAAA,CAAA,aAAO,yBAAyB,EAAE;AAC/B,6BAAA,IAAI,CAAC,YAAM,EAAA,OAAA,IAAI,CAAA,EAAA,CAAC;AAChB,6BAAA,KAAK,CAAC,YAAM,EAAA,OAAA,KAAK,CAAL,EAAK,CAAC,CAAC,CAAA;AACvB,iBAAA;;;AACF,KAAA,CAAA;AACD;;AAEG;AACG,IAAA,oBAAA,CAAA,SAAA,CAAA,IAAI,GAAV,YAAA;;;;;4BAC0B,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,uBAAuB,CAAA,CAAA;;AAApD,wBAAA,eAAe,GAAG,EAAkC,CAAA,IAAA,EAAA,CAAA;6BACtD,CAAC,eAAe,EAAhB,OAAgB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAClB,wBAAA,OAAA,CAAA,CAAA,aAAO,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAA;AAEC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAA;;AAAhE,wBAAA,kBAAkB,GAAG,EAA2C,CAAA,IAAA,EAAA,CAAA;AACtE,wBAAA,IAAI,kBAAkB,KAAlB,IAAA,IAAA,kBAAkB,uBAAlB,kBAAkB,CAAE,UAAU,EAAE;AAClC,4BAAA,OAAA,CAAA,CAAA,aAAO,kBAAkB,CAAC,CAAA;AAC3B,yBAAA;AAAM,6BAAA;AACL,4BAAA,OAAA,CAAA,CAAA,aAAO,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAA;AAC3B,yBAAA;;;;;AAEJ,KAAA,CAAA;;IAEK,oBAAS,CAAA,SAAA,CAAA,SAAA,GAAf,UAAgB,gBAAuC,EAAA;;;;;;4BAC7B,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,uBAAuB,CAAA,CAAA;;AAApD,wBAAA,eAAe,GAAG,EAAkC,CAAA,IAAA,EAAA,CAAA;6BACtD,CAAC,eAAe,EAAhB,OAAgB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBAClB,OAAO,CAAA,CAAA,YAAA,CAAA;AAE0B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;AAA5C,wBAAA,wBAAwB,GAAG,EAAiB,CAAA,IAAA,EAAA,CAAA;AAClD,wBAAA,OAAA,CAAA,CAAA,aAAO,0BAA0B,CAAC,IAAI,CAAC,GAAG,EAAE;gCAC1C,qBAAqB,EACnB,MAAA,gBAAgB,CAAC,qBAAqB,MACtC,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,wBAAwB,CAAC,qBAAqB;gCAChD,UAAU,EAAE,gBAAgB,CAAC,UAAU;AACxC,6BAAA,CAAC,CAAC,CAAA;;;;AAEN,KAAA,CAAA;;IAEK,oBAAG,CAAA,SAAA,CAAA,GAAA,GAAT,UAAU,gBAAuC,EAAA;;;;;;4BACvB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,uBAAuB,CAAA,CAAA;;AAApD,wBAAA,eAAe,GAAG,EAAkC,CAAA,IAAA,EAAA,CAAA;6BACtD,CAAC,eAAe,EAAhB,OAAgB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBAClB,OAAO,CAAA,CAAA,YAAA,CAAA;AAE0B,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA;;AAA5C,wBAAA,wBAAwB,GAAG,EAAiB,CAAA,IAAA,EAAA,CAAA;AAClD,wBAAA,OAAA,CAAA,CAAA,aAAO,0BAA0B,CAAC,IAAI,CAAC,GAAG,EAAE;gCAC1C,qBAAqB,EACnB,MAAA,gBAAgB,CAAC,qBAAqB,MACtC,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,wBAAwB,CAAC,qBAAqB;gCAChD,UAAU,EAAA,aAAA,CAAA,aAAA,CAAA,EAAA,EAAA,MAAA,CACL,wBAAwB,CAAC,UAAU,kBACnC,gBAAgB,CAAC,UAAU,CAC/B,EAAA,KAAA,CAAA;AACF,6BAAA,CAAC,CAAC,CAAA;;;;AAEN,KAAA,CAAA;IACH,OAAC,oBAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;;;;AAIG;AACG,SAAU,UAAU,CAAC,eAAwC,EAAA;;AAEjE,IAAA,OAAO,6BAA6B;;AAElC,IAAA,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC,CAC5D,CAAC,MAAM,CAAC;AACX;;AChTA;;;;;;;;;;;;;;;AAeG;AASG,SAAU,sBAAsB,CAAC,OAAgB,EAAA;AACrD,IAAA,kBAAkB,CAChB,IAAI,SAAS,CACX,iBAAiB,EACjB,UAAA,SAAS,EAAA,EAAI,OAAA,IAAI,yBAAyB,CAAC,SAAS,CAAC,GAAA,EAAA,SAAA,6BAEtD,CACF,CAAC;AACF,IAAA,kBAAkB,CAChB,IAAI,SAAS,CACX,WAAW,EACX,UAAA,SAAS,EAAA,EAAI,OAAA,IAAI,oBAAoB,CAAC,SAAS,CAAC,GAAA,EAAA,SAAA,6BAEjD,CACF,CAAC;;AAGF,IAAA,eAAe,CAACC,MAAI,EAAEF,SAAO,EAAE,OAAO,CAAC,CAAC;;AAExC,IAAA,eAAe,CAACE,MAAI,EAAEF,SAAO,EAAE,MAAkB,CAAC,CAAC;;AAEnD,IAAA,eAAe,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AACjC;;AC9CA;;;;;AAKG;AAyBH,sBAAsB,CAAC,EAAiB,CAAC;;;;"}