(()=>{var e={};e.id=784,e.ids=[784],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19278:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),i=r(43210),o=r(63385),n=r(75535),a=r(33784),u=r(3582);function d(){let[e,t]=(0,i.useState)(""),[r,d]=(0,i.useState)(!1),c=e=>{t(t=>t+e+"\n")},l=async()=>{c("=== STEP 1: Testing Collection Access ===");try{let e=(0,n.collection)(a.db,u.COLLECTIONS.users);c("✅ Collection reference created");let t=(0,n.P)(e,(0,n._M)(u.FIELD_NAMES.email,"==","<EMAIL>"));c("✅ Query created");let r=await (0,n.getDocs)(t);c(`✅ Query executed, found ${r.size} documents`)}catch(e){c(`❌ Collection access failed: ${e.message}`),c(`❌ Error code: ${e.code}`)}},p=async()=>{c("\n=== STEP 2: Testing Count Operation ===");try{let e=(0,n.collection)(a.db,u.COLLECTIONS.users),t=(await (0,n.d_)(e)).data().count;c(`✅ Count operation successful: ${t} users`)}catch(e){c(`❌ Count operation failed: ${e.message}`),c(`❌ Error code: ${e.code}`)}},x=async()=>{c("\n=== STEP 3: Testing Auth User Creation ===");try{let e=`test${Date.now()}@example.com`;c(`Creating auth user with email: ${e}`);let t=(await (0,o.eJ)(a.j2,e,"test123456")).user;c(`✅ Auth user created: ${t.uid}`),c(`   Email: ${t.email}`),c(`   Email verified: ${t.emailVerified}`),c(`   Auth token available: ${!!a.j2.currentUser}`),await new Promise(e=>setTimeout(e,1e3)),c("\n=== STEP 4: Testing Document Creation ===");let r=Date.now().toString().slice(-4),s=Math.random().toString(36).substring(2,4).toUpperCase(),i=`MY${r}${s}`;c(`Generated referral code: ${i}`);let d={[u.FIELD_NAMES.name]:"Test User",[u.FIELD_NAMES.email]:e,[u.FIELD_NAMES.mobile]:"9876543210",[u.FIELD_NAMES.referralCode]:i,[u.FIELD_NAMES.referredBy]:"",[u.FIELD_NAMES.referralBonusCredited]:!1,[u.FIELD_NAMES.plan]:"Trial",[u.FIELD_NAMES.planExpiry]:null,[u.FIELD_NAMES.activeDays]:1,[u.FIELD_NAMES.joinedDate]:new Date,[u.FIELD_NAMES.wallet]:0,[u.FIELD_NAMES.totalVideos]:0,[u.FIELD_NAMES.todayVideos]:0,[u.FIELD_NAMES.lastVideoDate]:null,[u.FIELD_NAMES.videoDuration]:30,status:"active"};c(`Document path: ${u.COLLECTIONS.users}/${t.uid}`),c(`Current auth user: ${a.j2.currentUser?.uid}`),c(`Auth state: ${a.j2.currentUser?"authenticated":"not authenticated"}`);let l=(0,n.H9)(a.db,u.COLLECTIONS.users,t.uid);try{c("Attempting setDoc..."),await (0,n.BN)(l,d),c("✅ User document created successfully"),(await (0,n.x7)(l)).exists()?c("✅ Document verification successful"):c("❌ Document verification failed")}catch(e){c(`❌ setDoc failed: ${e.message}`),c(`❌ setDoc error code: ${e.code}`),c(`❌ Full setDoc error: ${JSON.stringify(e,null,2)}`)}try{await t.delete(),c("✅ Test user deleted")}catch(e){c(`⚠️ User deletion failed: ${e.message}`)}}catch(e){c(`❌ Auth/Document creation failed: ${e.message}`),c(`❌ Error code: ${e.code}`),c(`❌ Full error: ${JSON.stringify(e,null,2)}`)}},m=async()=>{d(!0),t("");try{await l(),await p(),await x(),c("\n=== ALL TESTS COMPLETED ===")}catch(e){c(`
❌ Test suite failed: ${e.message}`)}finally{d(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Debug Registration"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("button",{onClick:m,disabled:r,className:"btn-primary mb-4",children:r?"Running Tests...":"Run Registration Debug Tests"}),(0,s.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,s.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap",children:e||'Click "Run Registration Debug Tests" to start...'})})]})]})})}},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{Cn:()=>l,db:()=>c,j2:()=>d});var s=r(67989),i=r(63385),o=r(75535),n=r(70146),a=r(24791);let u=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),d=(0,i.xI)(u),c=(0,o.aU)(u);(0,n.c7)(u);let l=(0,a.Uz)(u)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37144:(e,t,r)=>{Promise.resolve().then(r.bind(r,94556))},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},61949:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),i=r(48088),o=r(88170),n=r.n(o),a=r(30893),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let d={children:["",{children:["debug-registration",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94556)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-registration\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-registration\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/debug-registration/page",pathname:"/debug-registration",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71992:(e,t,r)=>{Promise.resolve().then(r.bind(r,19278))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94556:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\debug-registration\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\debug-registration\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,6958,8441,3582],()=>r(61949));module.exports=s})();