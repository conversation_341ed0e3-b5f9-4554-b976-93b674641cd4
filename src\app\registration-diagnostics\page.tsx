'use client'

import { useState, useEffect } from 'react'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS, generateUniqueReferralCode } from '@/lib/dataService'

export default function RegistrationDiagnostics() {
  const [logs, setLogs] = useState<string[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [testEmail, setTestEmail] = useState('')

  const addLog = (message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') => {
    const timestamp = new Date().toLocaleTimeString()
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'
    const logMessage = `[${timestamp}] ${emoji} ${message}`
    setLogs(prev => [...prev, logMessage])
    console.log(logMessage)
  }

  const clearLogs = () => {
    setLogs([])
  }

  const runDiagnostics = async () => {
    setIsRunning(true)
    clearLogs()

    try {
      const email = testEmail || `diagnostic${Date.now()}@test.com`
      const password = 'test123456'
      const name = 'Diagnostic Test User'
      const mobile = '9876543210'

      addLog('🚀 Starting Registration Diagnostics', 'info')
      addLog(`📧 Test Email: ${email}`)
      addLog(`🔧 Firebase Project: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}`)

      // Step 1: Check Firebase Configuration
      addLog('\n=== STEP 1: Firebase Configuration Check ===')
      addLog(`API Key: ${process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? 'Present' : 'Missing'}`)
      addLog(`Auth Domain: ${process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}`)
      addLog(`Project ID: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}`)
      addLog(`App ID: ${process.env.NEXT_PUBLIC_FIREBASE_APP_ID ? 'Present' : 'Missing'}`)

      // Step 2: Test Firebase Auth
      addLog('\n=== STEP 2: Firebase Auth Test ===')
      let user: any = null
      try {
        addLog('Creating Firebase Auth user...')
        const userCredential = await createUserWithEmailAndPassword(auth, email, password)
        user = userCredential.user
        addLog(`Auth user created successfully: ${user.uid}`, 'success')
        addLog(`User email: ${user.email}`)
        addLog(`Email verified: ${user.emailVerified}`)
      } catch (authError: any) {
        addLog(`Auth creation failed: ${authError.message}`, 'error')
        addLog(`Auth error code: ${authError.code}`, 'error')
        throw authError
      }

      // Step 3: Wait for Auth State
      addLog('\n=== STEP 3: Auth State Propagation ===')
      addLog('Waiting 2 seconds for auth state to propagate...')
      await new Promise(resolve => setTimeout(resolve, 2000))
      addLog(`Current auth user: ${auth.currentUser?.uid}`)
      addLog(`Auth state matches: ${auth.currentUser?.uid === user.uid}`, 
        auth.currentUser?.uid === user.uid ? 'success' : 'warning')

      // Step 4: Generate Referral Code
      addLog('\n=== STEP 4: Referral Code Generation ===')
      let referralCode: string = ''
      try {
        addLog('Generating referral code...')
        referralCode = await generateUniqueReferralCode()
        addLog(`Referral code generated: ${referralCode}`, 'success')
      } catch (refError: any) {
        addLog(`Referral code generation failed: ${refError.message}`, 'error')
        addLog(`Using fallback referral code...`, 'warning')
        referralCode = `MYN${Date.now().toString().slice(-4)}`
        addLog(`Fallback referral code: ${referralCode}`)
      }

      // Step 5: Prepare User Data
      addLog('\n=== STEP 5: User Data Preparation ===')
      const userData = {
        [FIELD_NAMES.name]: name,
        [FIELD_NAMES.email]: email.toLowerCase(),
        [FIELD_NAMES.mobile]: mobile,
        [FIELD_NAMES.referralCode]: referralCode,
        [FIELD_NAMES.referredBy]: '',
        [FIELD_NAMES.referralBonusCredited]: false,
        [FIELD_NAMES.plan]: 'Trial',
        [FIELD_NAMES.planExpiry]: null,
        [FIELD_NAMES.activeDays]: 1,
        [FIELD_NAMES.joinedDate]: Timestamp.now(),
        [FIELD_NAMES.wallet]: 0,
        [FIELD_NAMES.totalVideos]: 0,
        [FIELD_NAMES.todayVideos]: 0,
        [FIELD_NAMES.lastVideoDate]: null,
        [FIELD_NAMES.videoDuration]: 30,
        status: 'active'
      }
      addLog(`User data prepared with ${Object.keys(userData).length} fields`, 'success')
      addLog(`Data fields: ${Object.keys(userData).join(', ')}`)

      // Step 6: Create Firestore Document
      addLog('\n=== STEP 6: Firestore Document Creation ===')
      try {
        const userDocRef = doc(db, COLLECTIONS.users, user.uid)
        addLog(`Document path: ${userDocRef.path}`)
        addLog(`Collection: ${COLLECTIONS.users}`)
        addLog(`Document ID: ${user.uid}`)
        
        addLog('Attempting to create Firestore document...')
        await setDoc(userDocRef, userData)
        addLog('Firestore document created successfully!', 'success')
      } catch (firestoreError: any) {
        addLog(`Firestore creation failed: ${firestoreError.message}`, 'error')
        addLog(`Firestore error code: ${firestoreError.code}`, 'error')
        addLog(`Full error: ${JSON.stringify(firestoreError, null, 2)}`, 'error')
        throw firestoreError
      }

      // Step 7: Verify Document
      addLog('\n=== STEP 7: Document Verification ===')
      try {
        const userDocRef = doc(db, COLLECTIONS.users, user.uid)
        const verifyDoc = await getDoc(userDocRef)
        if (verifyDoc.exists()) {
          const data = verifyDoc.data()
          addLog('Document verification successful!', 'success')
          addLog(`Retrieved ${Object.keys(data).length} fields`)
          addLog(`Name: ${data[FIELD_NAMES.name]}`)
          addLog(`Email: ${data[FIELD_NAMES.email]}`)
          addLog(`Plan: ${data[FIELD_NAMES.plan]}`)
        } else {
          addLog('Document does not exist after creation!', 'error')
          throw new Error('Document verification failed')
        }
      } catch (verifyError: any) {
        addLog(`Document verification failed: ${verifyError.message}`, 'error')
        throw verifyError
      }

      addLog('\n🎉 All diagnostics passed! Registration should work.', 'success')

    } catch (error: any) {
      addLog(`\n💥 Diagnostics failed at: ${error.message}`, 'error')
      addLog(`Error code: ${error.code || 'N/A'}`, 'error')
      addLog(`Error stack: ${error.stack}`, 'error')
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="glass-card p-8">
          <h1 className="text-3xl font-bold text-white mb-6">Registration Diagnostics</h1>
          
          <div className="mb-6 space-y-4">
            <div>
              <label className="block text-white font-medium mb-2">
                Test Email (optional - will auto-generate if empty)
              </label>
              <input
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="form-input"
              />
            </div>
            
            <div className="flex space-x-4">
              <button
                onClick={runDiagnostics}
                disabled={isRunning}
                className="btn-primary"
              >
                {isRunning ? 'Running Diagnostics...' : 'Run Registration Diagnostics'}
              </button>
              
              <button
                onClick={clearLogs}
                disabled={isRunning}
                className="btn-secondary"
              >
                Clear Logs
              </button>
            </div>
          </div>

          <div className="bg-black/30 rounded-lg p-4 max-h-96 overflow-y-auto">
            <div className="text-white font-mono text-sm space-y-1">
              {logs.length === 0 ? (
                <div className="text-white/60">Click "Run Registration Diagnostics" to start...</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="whitespace-pre-wrap">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-500/20 rounded-lg">
            <h3 className="text-white font-bold mb-2">What this test does:</h3>
            <ul className="text-white/80 text-sm space-y-1">
              <li>• Checks Firebase configuration</li>
              <li>• Tests Firebase Auth user creation</li>
              <li>• Verifies auth state propagation</li>
              <li>• Tests referral code generation</li>
              <li>• Attempts Firestore document creation</li>
              <li>• Verifies document was created successfully</li>
            </ul>
          </div>

          <div className="mt-4 space-x-4">
            <a href="/register" className="btn-primary inline-block">
              Go to Registration
            </a>
            <a href="/debug-registration-simple" className="btn-secondary inline-block">
              Debug Registration
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
