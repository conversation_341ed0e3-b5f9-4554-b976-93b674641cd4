(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[993,6779],{3737:(e,t,a)=>{"use strict";function s(e,t,a){if(!e||0===e.length)return void alert("No data to export");let s=a||Object.keys(e[0]),i=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],r=new Blob(["\uFEFF"+[s.join(","),...e.map(e=>s.map(t=>{let a=e[t];if(null==a)return"";let s=i.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):s&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let e=URL.createObjectURL(r);n.setAttribute("href",e),n.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)}}function i(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function r(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function n(e){return e.map(e=>{var t,a,s,i;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(s=e.bankDetails)?void 0:s.accountNumber)||""),"IFSC Code":(null==(i=e.bankDetails)?void 0:i.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function l(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>s,Fz:()=>i,Pe:()=>l,dB:()=>n,sL:()=>r})},4880:(e,t,a)=>{Promise.resolve().then(a.bind(a,9119))},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>d,I0:()=>m,Pn:()=>l,TK:()=>h,getAllPendingWithdrawals:()=>g,getAllWithdrawals:()=>x,hG:()=>f,lo:()=>o,nQ:()=>u,updateWithdrawalStatus:()=>y,x5:()=>c});var s=a(5317),i=a(6104),r=a(3592);let n=new Map;async function l(){let e="dashboard-stats",t=function(e){let t=n.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=s.Dc.fromDate(t),l=await (0,s.getDocs)((0,s.collection)(i.db,r.COLLECTIONS.users)),o=l.size,c=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s._M)(r.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,s.getDocs)(c)).size,u=0,m=0,g=0,x=0;l.forEach(e=>{var a;let s=e.data();u+=s[r.FIELD_NAMES.totalVideos]||0,m+=s[r.FIELD_NAMES.wallet]||0;let i=null==(a=s[r.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();i&&i.toDateString()===t.toDateString()&&(g+=s[r.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s._M)(r.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.getDocs)(e)).forEach(e=>{var a;let s=e.data(),i=null==(a=s[r.FIELD_NAMES.date])?void 0:a.toDate();i&&i>=t&&(x+=s[r.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let h=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),f=(await (0,s.getDocs)(h)).size,y=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s._M)("date",">=",a)),p=(await (0,s.getDocs)(y)).size,b={totalUsers:o,totalVideos:u,totalEarnings:m,pendingWithdrawals:f,todayUsers:d,todayVideos:g,todayEarnings:x,todayWithdrawals:p};return n.set(e,{data:b,timestamp:Date.now()}),b}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let n=await (0,s.getDocs)(a);return{users:n.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[r.FIELD_NAMES.name]||"").toLowerCase(),s=String(e[r.FIELD_NAMES.email]||"").toLowerCase(),i=String(e[r.FIELD_NAMES.mobile]||"").toLowerCase(),n=String(e[r.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||s.includes(t)||i.includes(t)||n.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users),(0,s.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[r.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.users));return(await (0,s.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s.My)(r.FIELD_NAMES.date,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.transactions),(0,s.My)(r.FIELD_NAMES.date,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let n=await (0,s.getDocs)(a);return{transactions:n.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[r.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending"),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," pending withdrawals")),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function x(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,s.P)((0,s.collection)(i.db,r.COLLECTIONS.withdrawals),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total withdrawals")),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function h(e,t){try{await (0,s.mZ)((0,s.H9)(i.db,r.COLLECTIONS.users,e),t),n.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function f(e){try{await (0,s.kd)((0,s.H9)(i.db,r.COLLECTIONS.users,e)),n.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function y(e,t,l){try{let o=await (0,s.x7)((0,s.H9)(i.db,r.COLLECTIONS.withdrawals,e));if(!o.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=o.data(),m={status:t,updatedAt:s.Dc.now()};if(l&&(m.adminNotes=l),await (0,s.mZ)((0,s.H9)(i.db,r.COLLECTIONS.withdrawals,e),m),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}n.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},9119:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(5155),i=a(2115),r=a(6874),n=a.n(r),l=a(6681),o=a(3592),c=a(6779),d=a(3737),u=a(4752),m=a.n(u);function g(){let{user:e,loading:t,isAdmin:a}=(0,l.wC)(),[r,u]=(0,i.useState)([]),[g,x]=(0,i.useState)([]),[h,f]=(0,i.useState)(!0),[y,p]=(0,i.useState)(!1),[b,N]=(0,i.useState)(!1),[D,w]=(0,i.useState)([]),[v,j]=(0,i.useState)(!1),[E,S]=(0,i.useState)({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]});(0,i.useEffect)(()=>{a&&L()},[a]);let L=async()=>{try{f(!0);let[e,t]=await Promise.all([(0,o._f)(50),(0,c.lo)()]);u(e),x(t.users)}catch(e){console.error("Error loading data:",e),m().fire({icon:"error",title:"Error",text:"Failed to load data. Please try again."})}finally{f(!1)}},C=async()=>{try{N(!0),await (0,o.z8)({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),m().fire({icon:"success",title:"Test Notification Sent!",text:"Test notification sent to all users. Check user dashboards to verify delivery.",timer:3e3,showConfirmButton:!1}),L()}catch(e){console.error("Error sending test notification:",e),m().fire({icon:"error",title:"Test Failed",text:"Failed to send test notification. Please try again."})}finally{N(!1)}},A=async()=>{try{if(!E.title.trim()||!E.message.trim())return void m().fire({icon:"error",title:"Validation Error",text:"Please fill in both title and message."});if("specific"===E.targetUsers&&0===E.selectedUserIds.length)return void m().fire({icon:"error",title:"Validation Error",text:"Please select at least one user for specific targeting."});N(!0),console.log("Sending notification:",{title:E.title.trim(),message:E.message.trim(),type:E.type,targetUsers:E.targetUsers,userIds:"specific"===E.targetUsers?E.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),await (0,o.z8)({title:E.title.trim(),message:E.message.trim(),type:E.type,targetUsers:E.targetUsers,userIds:"specific"===E.targetUsers?E.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),m().fire({icon:"success",title:"Notification Sent!",text:"Notification sent to ".concat("all"===E.targetUsers?"all users":"".concat(E.selectedUserIds.length," selected users"),"."),timer:3e3,showConfirmButton:!1}),S({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]}),p(!1),L()}catch(e){console.error("Error sending notification:",e),m().fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{N(!1)}},I=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},k=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return"".concat(e," minute").concat(e>1?"s":""," ago")}if(t<86400){let e=Math.floor(t/3600);return"".concat(e," hour").concat(e>1?"s":""," ago")}{let e=Math.floor(t/86400);return"".concat(e," day").concat(e>1?"s":""," ago")}},M=async(e,t)=>{if((await m().fire({icon:"warning",title:"Delete Notification",text:'Are you sure you want to delete "'.concat(t,'"? This action cannot be undone.'),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{j(!0),await (0,o.fP)(e),u(t=>t.filter(t=>t.id!==e)),m().fire({icon:"success",title:"Notification Deleted",text:"Notification has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notification:",e),m().fire({icon:"error",title:"Delete Failed",text:"Failed to delete notification. Please try again."})}finally{j(!1)}},T=async()=>{if(0===D.length)return void m().fire({icon:"warning",title:"No Selection",text:"Please select notifications to delete."});if((await m().fire({icon:"warning",title:"Delete Selected Notifications",text:"Are you sure you want to delete ".concat(D.length," selected notifications? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete All",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{j(!0),await Promise.all(D.map(e=>(0,o.fP)(e))),u(e=>e.filter(e=>!D.includes(e.id))),w([]),m().fire({icon:"success",title:"Notifications Deleted",text:"".concat(D.length," notifications have been deleted successfully"),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notifications:",e),m().fire({icon:"error",title:"Delete Failed",text:"Failed to delete some notifications. Please try again."})}finally{j(!1)}},U=e=>{w(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return t||h?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(n(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",r.length,D.length>0&&(0,s.jsxs)("span",{className:"ml-2 text-blue-600",children:["(",D.length," selected)"]})]}),D.length>0&&(0,s.jsxs)("button",{onClick:T,disabled:v,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-trash mr-2"}),"Delete Selected (",D.length,")"]}),(0,s.jsxs)("button",{onClick:C,disabled:b||v,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-vial mr-2"}),"Test Notification"]}),(0,s.jsxs)("button",{onClick:()=>{if(0===r.length)return void m().fire({icon:"warning",title:"No Data",text:"No notifications to export."});let e=(0,d.Pe)(r);(0,d.Bf)(e,"notifications"),m().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(r.length," notifications to CSV file."),timer:2e3,showConfirmButton:!1})},disabled:v,className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:()=>p(!0),disabled:v,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Send Notification"]}),(0,s.jsxs)("button",{onClick:L,disabled:v,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===r.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-6xl mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications sent yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Start by sending your first notification to users"}),(0,s.jsxs)("button",{onClick:()=>p(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Send First Notification"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-gray-50 px-6 py-3 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:D.length===r.length&&r.length>0,onChange:()=>{D.length===r.length?w([]):w(r.map(e=>e.id).filter(Boolean))},className:"mr-3"}),(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:["Select All (",r.length," notifications)"]})]}),D.length>0&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[D.length," selected"]}),(0,s.jsxs)("button",{onClick:T,disabled:v,className:"text-red-600 hover:text-red-800 disabled:opacity-50",children:[(0,s.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete Selected"]})]})]})}),(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:r.map(e=>{var t;return(0,s.jsx)("div",{className:"p-6 hover:bg-gray-50 ".concat(D.includes(e.id)?"bg-blue-50":""),children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("input",{type:"checkbox",checked:D.includes(e.id),onChange:()=>U(e.id),className:"mr-3"})}),(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,s.jsx)("i",{className:I(e.type)})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.title,(0,s.jsx)("span",{className:"ml-2 px-2 py-1 text-xs font-bold bg-red-100 text-red-800 rounded-full",children:"\uD83D\uDEA8 BLOCKING"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("success"===e.type?"bg-green-100 text-green-800":"warning"===e.type?"bg-yellow-100 text-yellow-800":"error"===e.type?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.type.charAt(0).toUpperCase()+e.type.slice(1)}),(0,s.jsx)("button",{onClick:()=>M(e.id,e.title),disabled:v,className:"text-red-600 hover:text-red-800 disabled:opacity-50 p-1",title:"Delete notification",children:(0,s.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,s.jsx)("p",{className:"text-gray-700 mt-2",children:e.message}),(0,s.jsx)("div",{className:"flex items-center justify-between mt-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-user mr-1"}),"By: ",e.createdBy]}),(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-users mr-1"}),"Target: ","all"===e.targetUsers?"All Users":"".concat((null==(t=e.userIds)?void 0:t.length)||0," Selected Users")]}),(0,s.jsxs)("span",{children:[(0,s.jsx)("i",{className:"fas fa-clock mr-1"}),k(e.createdAt)]})]})})]})]})},e.id)})})]})})}),y&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Send Notification"}),(0,s.jsx)("button",{onClick:()=>p(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),(0,s.jsx)("input",{type:"text",value:E.title,onChange:e=>S(t=>({...t,title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification title..."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,s.jsx)("textarea",{value:E.message,onChange:e=>S(t=>({...t,message:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification message..."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,s.jsxs)("select",{value:E.type,onChange:e=>S(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"info",children:"Info"}),(0,s.jsx)("option",{value:"success",children:"Success"}),(0,s.jsx)("option",{value:"warning",children:"Warning"}),(0,s.jsx)("option",{value:"error",children:"Error"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Target"}),(0,s.jsxs)("select",{value:E.targetUsers,onChange:e=>S(t=>({...t,targetUsers:e.target.value,selectedUserIds:"all"===e.target.value?[]:t.selectedUserIds})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"All Users"}),(0,s.jsx)("option",{value:"specific",children:"Specific Users"})]})]})]}),(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle text-red-500 mt-1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"\uD83D\uDEA8 All Notifications are Blocking (Mandatory)"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Users must acknowledge this notification before they can continue with any activities (watching videos, accessing dashboard features, etc.)"})]})]})}),"specific"===E.targetUsers&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Users"}),(0,s.jsx)("div",{className:"max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2",children:g.map(e=>(0,s.jsxs)("label",{className:"flex items-center p-2 hover:bg-gray-50 rounded",children:[(0,s.jsx)("input",{type:"checkbox",checked:E.selectedUserIds.includes(e.id),onChange:t=>{t.target.checked?S(t=>({...t,selectedUserIds:[...t.selectedUserIds,e.id]})):S(t=>({...t,selectedUserIds:t.selectedUserIds.filter(t=>t!==e.id)}))},className:"mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name||"Unknown User"}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[e.email||"No email"," • ",e.plan||"No plan"]})]})]},e.id))}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[E.selectedUserIds.length," user(s) selected"]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,s.jsx)("button",{onClick:()=>p(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,s.jsx)("button",{onClick:A,disabled:b,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Notification"]})})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,6681,8441,1684,7358],()=>t(4880)),_N_E=e.O()}]);