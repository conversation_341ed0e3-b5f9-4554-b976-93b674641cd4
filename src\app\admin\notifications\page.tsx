'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAdmin } from '@/hooks/useAuth'
import { addNotification, getAllNotifications, deleteNotification, Notification } from '@/lib/dataService'
import { getUsers } from '@/lib/adminDataService'
import { downloadCSV, formatNotificationsForExport } from '@/lib/csvExport'
import Swal from 'sweetalert2'

interface User {
  id: string
  name?: string
  email?: string
  mobile?: string
  plan?: string
  status?: string
  joinedDate?: Date
  planExpiry?: Date
  [key: string]: any // Allow additional properties
}

export default function AdminNotificationsPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [dataLoading, setDataLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [isDeleting, setIsDeleting] = useState(false)
  
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: 'info' as 'info' | 'success' | 'warning' | 'error',
    targetUsers: 'all' as 'all' | 'specific',
    selectedUserIds: [] as string[]
  })

  useEffect(() => {
    if (isAdmin) {
      loadData()
    }
  }, [isAdmin])

  const loadData = async () => {
    try {
      setDataLoading(true)
      const [notificationsData, usersResponse] = await Promise.all([
        getAllNotifications(50),
        getUsers()
      ])
      setNotifications(notificationsData)
      setUsers(usersResponse.users)
    } catch (error) {
      console.error('Error loading data:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to load data. Please try again.',
      })
    } finally {
      setDataLoading(false)
    }
  }

  const handleSendTestNotification = async () => {
    try {
      setIsSending(true)

      await addNotification({
        title: 'Test Notification',
        message: 'This is a test notification to verify the system is working correctly.',
        type: 'info',
        targetUsers: 'all',
        userIds: [],
        createdBy: user?.email || 'admin'
      })

      Swal.fire({
        icon: 'success',
        title: 'Test Notification Sent!',
        text: 'Test notification sent to all users. Check user dashboards to verify delivery.',
        timer: 3000,
        showConfirmButton: false
      })

      // Reload notifications
      loadData()
    } catch (error) {
      console.error('Error sending test notification:', error)
      Swal.fire({
        icon: 'error',
        title: 'Test Failed',
        text: 'Failed to send test notification. Please try again.',
      })
    } finally {
      setIsSending(false)
    }
  }

  const handleSendNotification = async () => {
    try {
      if (!formData.title.trim() || !formData.message.trim()) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Please fill in both title and message.',
        })
        return
      }

      if (formData.targetUsers === 'specific' && formData.selectedUserIds.length === 0) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Please select at least one user for specific targeting.',
        })
        return
      }

      setIsSending(true)

      console.log('Sending notification:', {
        title: formData.title.trim(),
        message: formData.message.trim(),
        type: formData.type,
        targetUsers: formData.targetUsers,
        userIds: formData.targetUsers === 'specific' ? formData.selectedUserIds : [],
        createdBy: user?.email || 'admin'
      })

      await addNotification({
        title: formData.title.trim(),
        message: formData.message.trim(),
        type: formData.type,
        targetUsers: formData.targetUsers,
        userIds: formData.targetUsers === 'specific' ? formData.selectedUserIds : [],
        createdBy: user?.email || 'admin'
      })

      Swal.fire({
        icon: 'success',
        title: 'Notification Sent!',
        text: `Notification sent to ${formData.targetUsers === 'all' ? 'all users' : `${formData.selectedUserIds.length} selected users`}.`,
        timer: 3000,
        showConfirmButton: false
      })

      // Reset form and close modal
      setFormData({
        title: '',
        message: '',
        type: 'info',
        targetUsers: 'all',
        selectedUserIds: []
      })
      setShowCreateModal(false)

      // Reload notifications
      loadData()
    } catch (error) {
      console.error('Error sending notification:', error)
      Swal.fire({
        icon: 'error',
        title: 'Send Failed',
        text: 'Failed to send notification. Please try again.',
      })
    } finally {
      setIsSending(false)
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return 'fas fa-check-circle text-green-500'
      case 'warning':
        return 'fas fa-exclamation-triangle text-yellow-500'
      case 'error':
        return 'fas fa-times-circle text-red-500'
      default:
        return 'fas fa-info-circle text-blue-500'
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return 'Just now'
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `${hours} hour${hours > 1 ? 's' : ''} ago`
    } else {
      const days = Math.floor(diffInSeconds / 86400)
      return `${days} day${days > 1 ? 's' : ''} ago`
    }
  }

  const handleDeleteNotification = async (notificationId: string, title: string) => {
    const result = await Swal.fire({
      icon: 'warning',
      title: 'Delete Notification',
      text: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
      showCancelButton: true,
      confirmButtonText: 'Yes, Delete',
      confirmButtonColor: '#dc2626',
      cancelButtonText: 'Cancel'
    })

    if (result.isConfirmed) {
      try {
        setIsDeleting(true)
        await deleteNotification(notificationId)

        // Remove from local state
        setNotifications(prev => prev.filter(n => n.id !== notificationId))

        Swal.fire({
          icon: 'success',
          title: 'Notification Deleted',
          text: 'Notification has been deleted successfully',
          timer: 2000,
          showConfirmButton: false
        })
      } catch (error) {
        console.error('Error deleting notification:', error)
        Swal.fire({
          icon: 'error',
          title: 'Delete Failed',
          text: 'Failed to delete notification. Please try again.',
        })
      } finally {
        setIsDeleting(false)
      }
    }
  }

  const handleBulkDelete = async () => {
    if (selectedNotifications.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Selection',
        text: 'Please select notifications to delete.',
      })
      return
    }

    const result = await Swal.fire({
      icon: 'warning',
      title: 'Delete Selected Notifications',
      text: `Are you sure you want to delete ${selectedNotifications.length} selected notifications? This action cannot be undone.`,
      showCancelButton: true,
      confirmButtonText: 'Yes, Delete All',
      confirmButtonColor: '#dc2626',
      cancelButtonText: 'Cancel'
    })

    if (result.isConfirmed) {
      try {
        setIsDeleting(true)

        // Delete all selected notifications
        await Promise.all(selectedNotifications.map(id => deleteNotification(id)))

        // Remove from local state
        setNotifications(prev => prev.filter(n => !selectedNotifications.includes(n.id!)))
        setSelectedNotifications([])

        Swal.fire({
          icon: 'success',
          title: 'Notifications Deleted',
          text: `${selectedNotifications.length} notifications have been deleted successfully`,
          timer: 2000,
          showConfirmButton: false
        })
      } catch (error) {
        console.error('Error deleting notifications:', error)
        Swal.fire({
          icon: 'error',
          title: 'Delete Failed',
          text: 'Failed to delete some notifications. Please try again.',
        })
      } finally {
        setIsDeleting(false)
      }
    }
  }

  const handleSelectAll = () => {
    if (selectedNotifications.length === notifications.length) {
      setSelectedNotifications([])
    } else {
      setSelectedNotifications(notifications.map(n => n.id!).filter(Boolean))
    }
  }

  const handleSelectNotification = (notificationId: string) => {
    setSelectedNotifications(prev => {
      if (prev.includes(notificationId)) {
        return prev.filter(id => id !== notificationId)
      } else {
        return [...prev, notificationId]
      }
    })
  }

  const handleExportNotifications = () => {
    if (notifications.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data',
        text: 'No notifications to export.',
      })
      return
    }

    const exportData = formatNotificationsForExport(notifications)
    downloadCSV(exportData, 'notifications')

    Swal.fire({
      icon: 'success',
      title: 'Export Complete',
      text: `Exported ${notifications.length} notifications to CSV file.`,
      timer: 2000,
      showConfirmButton: false
    })
  }

  if (loading || dataLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading notifications...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Link
              href="/admin"
              className="text-gray-500 hover:text-gray-700"
            >
              <i className="fas fa-arrow-left text-xl"></i>
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">
              Total: {notifications.length}
              {selectedNotifications.length > 0 && (
                <span className="ml-2 text-blue-600">
                  ({selectedNotifications.length} selected)
                </span>
              )}
            </span>

            {selectedNotifications.length > 0 && (
              <button
                onClick={handleBulkDelete}
                disabled={isDeleting}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                <i className="fas fa-trash mr-2"></i>
                Delete Selected ({selectedNotifications.length})
              </button>
            )}

            <button
              onClick={handleSendTestNotification}
              disabled={isSending || isDeleting}
              className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            >
              <i className="fas fa-vial mr-2"></i>
              Test Notification
            </button>
            <button
              onClick={handleExportNotifications}
              disabled={isDeleting}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            >
              <i className="fas fa-download mr-2"></i>
              Export CSV
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              disabled={isDeleting}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            >
              <i className="fas fa-plus mr-2"></i>
              Send Notification
            </button>
            <button
              onClick={loadData}
              disabled={isDeleting}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            >
              <i className="fas fa-sync-alt mr-2"></i>
              Refresh
            </button>
          </div>
        </div>
      </header>

      {/* Notifications List */}
      <div className="p-6">
        <div className="bg-white rounded-lg shadow overflow-hidden">
          {notifications.length === 0 ? (
            <div className="text-center py-12">
              <i className="fas fa-bell-slash text-gray-300 text-6xl mb-4"></i>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications sent yet</h3>
              <p className="text-gray-500 mb-4">Start by sending your first notification to users</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg"
              >
                <i className="fas fa-plus mr-2"></i>
                Send First Notification
              </button>
            </div>
          ) : (
            <>
              {/* Bulk Actions Header */}
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedNotifications.length === notifications.length && notifications.length > 0}
                      onChange={handleSelectAll}
                      className="mr-3"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Select All ({notifications.length} notifications)
                    </span>
                  </label>

                  {selectedNotifications.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">
                        {selectedNotifications.length} selected
                      </span>
                      <button
                        onClick={handleBulkDelete}
                        disabled={isDeleting}
                        className="text-red-600 hover:text-red-800 disabled:opacity-50"
                      >
                        <i className="fas fa-trash mr-1"></i>
                        Delete Selected
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div className="divide-y divide-gray-200">
                {notifications.map((notification) => (
                  <div key={notification.id} className={`p-6 hover:bg-gray-50 ${selectedNotifications.includes(notification.id!) ? 'bg-blue-50' : ''}`}>
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 mt-1">
                        <input
                          type="checkbox"
                          checked={selectedNotifications.includes(notification.id!)}
                          onChange={() => handleSelectNotification(notification.id!)}
                          className="mr-3"
                        />
                      </div>

                      <div className="flex-shrink-0 mt-1">
                        <i className={getNotificationIcon(notification.type)}></i>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-medium text-gray-900 flex items-center">
                            {notification.title}
                            <span className="ml-2 px-2 py-1 text-xs font-bold bg-red-100 text-red-800 rounded-full">
                              🚨 BLOCKING
                            </span>
                          </h3>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              notification.type === 'success' ? 'bg-green-100 text-green-800' :
                              notification.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                              notification.type === 'error' ? 'bg-red-100 text-red-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
                            </span>
                            <button
                              onClick={() => handleDeleteNotification(notification.id!, notification.title)}
                              disabled={isDeleting}
                              className="text-red-600 hover:text-red-800 disabled:opacity-50 p-1"
                              title="Delete notification"
                            >
                              <i className="fas fa-trash"></i>
                            </button>
                          </div>
                        </div>

                        <p className="text-gray-700 mt-2">
                          {notification.message}
                        </p>

                        <div className="flex items-center justify-between mt-4">
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>
                              <i className="fas fa-user mr-1"></i>
                              By: {notification.createdBy}
                            </span>
                            <span>
                              <i className="fas fa-users mr-1"></i>
                              Target: {notification.targetUsers === 'all' ? 'All Users' : `${notification.userIds?.length || 0} Selected Users`}
                            </span>
                            <span>
                              <i className="fas fa-clock mr-1"></i>
                              {formatTimeAgo(notification.createdAt)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Create Notification Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Send Notification</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <i className="fas fa-times text-xl"></i>
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter notification title..."
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
                <textarea
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter notification message..."
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="info">Info</option>
                    <option value="success">Success</option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Target</label>
                  <select
                    value={formData.targetUsers}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      targetUsers: e.target.value as any,
                      selectedUserIds: e.target.value === 'all' ? [] : prev.selectedUserIds
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Users</option>
                    <option value="specific">Specific Users</option>
                  </select>
                </div>
              </div>

              {/* All Notifications are Blocking */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <i className="fas fa-exclamation-triangle text-red-500 mt-1"></i>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      🚨 All Notifications are Blocking (Mandatory)
                    </h4>
                    <p className="text-sm text-gray-600 mt-1">
                      Users must acknowledge this notification before they can continue with any activities (watching videos, accessing dashboard features, etc.)
                    </p>
                  </div>
                </div>
              </div>
              
              {formData.targetUsers === 'specific' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Select Users</label>
                  <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2">
                    {users.map((user) => (
                      <label key={user.id} className="flex items-center p-2 hover:bg-gray-50 rounded">
                        <input
                          type="checkbox"
                          checked={formData.selectedUserIds.includes(user.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData(prev => ({
                                ...prev,
                                selectedUserIds: [...prev.selectedUserIds, user.id]
                              }))
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                selectedUserIds: prev.selectedUserIds.filter(id => id !== user.id)
                              }))
                            }
                          }}
                          className="mr-3"
                        />
                        <div>
                          <div className="font-medium text-gray-900">{user.name || 'Unknown User'}</div>
                          <div className="text-sm text-gray-500">{user.email || 'No email'} • {user.plan || 'No plan'}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    {formData.selectedUserIds.length} user(s) selected
                  </p>
                </div>
              )}
            </div>
            
            <div className="flex justify-end space-x-4 mt-6">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={handleSendNotification}
                disabled={isSending}
                className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
              >
                {isSending ? (
                  <>
                    <div className="spinner w-4 h-4 mr-2 inline-block"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <i className="fas fa-paper-plane mr-2"></i>
                    Send Notification
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
