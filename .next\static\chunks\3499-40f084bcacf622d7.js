"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3499,9567],{6572:(e,t,a)=>{a.d(t,{l:()=>n});var r=a(2115),o=a(9567);function n(e){let{userId:t,checkInterval:a=3e4,enabled:n=!0}=e,[s,l]=(0,r.useState)({blocked:!1,lastChecked:new Date}),[c,i]=(0,r.useState)(!1),d=(0,r.useCallback)(async()=>{if(t&&n)try{i(!0);let e=await (0,o.q8)(t);return l({blocked:e.blocked,reason:e.reason,lastChecked:new Date}),e}catch(e){return console.error("Error checking leave status:",e),l(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{i(!1)}},[t,n]);return(0,r.useEffect)(()=>{t&&n&&d()},[t,n,d]),(0,r.useEffect)(()=>{if(!t||!n||a<=0)return;let e=setInterval(()=>{d()},a);return()=>clearInterval(e)},[t,n,a,d]),{leaveStatus:s,isChecking:c,checkLeaveStatus:d,isBlocked:s.blocked}}},7460:(e,t,a)=>{a.d(t,{J:()=>n});var r=a(2115),o=a(3592);function n(e){let[t,a]=(0,r.useState)(!1),[n,s]=(0,r.useState)(!0);(0,r.useEffect)(()=>{e?l():s(!1)},[e]);let l=async()=>{try{s(!0);let t=await (0,o.iA)(e);a(t)}catch(e){console.error("Error checking for blocking notifications:",e),a(!1)}finally{s(!1)}};return{hasBlockingNotifications:t,isChecking:n,checkForBlockingNotifications:l,markAllAsRead:()=>{a(!1)}}}},8647:(e,t,a)=>{a.d(t,{A:()=>s});var r=a(5155),o=a(2115),n=a(3592);function s(e){let{userId:t,onAllRead:a}=e,[s,l]=(0,o.useState)([]),[c,i]=(0,o.useState)(0),[d,u]=(0,o.useState)(!0);(0,o.useEffect)(()=>{t&&g()},[t]);let g=async()=>{try{u(!0);let e=await (0,n.AX)(t);l(e),0===e.length&&a()}catch(e){console.error("Error loading notifications:",e),a()}finally{u(!1)}},m=async()=>{let e=s[c];(null==e?void 0:e.id)&&(await (0,n.bA)(e.id,t),c<s.length-1?i(c+1):a())};if(d)return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===s.length)return null;let f=s[c];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(f.type)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,r.jsxs)("p",{className:"text-blue-100 text-sm",children:[c+1," of ",s.length," notifications"]})]})]}),(0,r.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,r.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:f.title}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,r.jsx)("p",{className:"text-gray-800 leading-relaxed",children:f.message})}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,r.jsxs)("span",{children:["From: ",f.createdBy]}),(0,r.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?"".concat(Math.floor(t/60)," minutes ago"):t<86400?"".concat(Math.floor(t/3600)," hours ago"):"".concat(Math.floor(t/86400)," days ago")})(f.createdAt)})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[c+1,"/",s.length]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((c+1)/s.length*100,"%")}})})]}),(0,r.jsxs)("button",{onClick:m,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,r.jsx)("i",{className:"fas fa-check"}),(0,r.jsx)("span",{children:c<s.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("i",{className:"fas fa-info-circle"}),(0,r.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}},9567:(e,t,a)=>{a.d(t,{applyUserLeave:()=>u,cancelUserLeave:()=>m,createAdminLeave:()=>s,debugAdminLeaveStatus:()=>c,deleteAdminLeave:()=>i,getAdminLeaves:()=>l,getUserLeaves:()=>g,getUserMonthlyLeaveCount:()=>f,isAdminLeaveDay:()=>d,isUserOnLeave:()=>h,q8:()=>v});var r=a(6104),o=a(5317);let n={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(e){try{return(await (0,o.gS)((0,o.collection)(r.db,n.adminLeaves),{...e,date:o.Dc.fromDate(e.date),createdAt:o.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function l(){try{let e=(0,o.P)((0,o.collection)(r.db,n.adminLeaves),(0,o.My)("date","asc")),t=(await (0,o.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function c(){try{let e=new Date;console.log("\uD83D\uDD0D Debug: Checking admin leave status for today:",e.toDateString());let t=await d(e);console.log("\uD83D\uDCCA Debug: Admin leave result:",t);let a=await l();console.log("\uD83D\uDCC5 Debug: All admin leaves in database:",a);let r=a.filter(t=>t.date.toDateString()===e.toDateString());console.log("\uD83D\uDCC5 Debug: Today's admin leaves:",r)}catch(e){console.error("❌ Debug: Error checking admin leave status:",e)}}async function i(e){try{await (0,o.kd)((0,o.H9)(r.db,n.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function d(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let s=(0,o.P)((0,o.collection)(r.db,n.adminLeaves),(0,o._M)("date",">=",o.Dc.fromDate(t)),(0,o._M)("date","<=",o.Dc.fromDate(a))),l=await (0,o.getDocs)(s),c=!l.empty;return c?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),c}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function u(e){try{let t,a,s,l=new Date,c=l.getFullYear(),i=l.getMonth()+1,d=await f(e.userId,c,i),u="pending";return d<4&&(u="approved",t="system",s=o.Dc.now(),a="Auto-approved: ".concat(d+1,"/").concat(4," monthly leaves used")),{id:(await (0,o.gS)((0,o.collection)(r.db,n.userLeaves),{...e,date:o.Dc.fromDate(e.date),status:u,appliedAt:o.Dc.now(),...t&&{reviewedBy:t},...s&&{reviewedAt:s},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function g(e){try{let t=(0,o.P)((0,o.collection)(r.db,n.userLeaves),(0,o._M)("userId","==",e),(0,o.My)("date","desc"));return(await (0,o.getDocs)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:null==(t=e.data().reviewedAt)?void 0:t.toDate()}})}catch(e){throw console.error("Error getting user leaves:",e),e}}async function m(e){try{await (0,o.kd)((0,o.H9)(r.db,n.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function f(e,t,a){try{let s=new Date(t,a-1,1),l=new Date(t,a,0,23,59,59,999),c=(0,o.P)((0,o.collection)(r.db,n.userLeaves),(0,o._M)("userId","==",e),(0,o._M)("status","==","approved"),(0,o._M)("date",">=",o.Dc.fromDate(s)),(0,o._M)("date","<=",o.Dc.fromDate(l)));return(await (0,o.getDocs)(c)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function h(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let s=new Date(t);s.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",s.toISOString());let l=(0,o.P)((0,o.collection)(r.db,n.userLeaves),(0,o._M)("userId","==",e),(0,o._M)("status","==","approved"),(0,o._M)("date",">=",o.Dc.fromDate(a)),(0,o._M)("date","<=",o.Dc.fromDate(s))),c=await (0,o.getDocs)(l),i=!c.empty;return i?console.log("\uD83D\uDC64 Found user leave(s) for today:",c.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),i}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function v(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await d(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await h(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}}}]);