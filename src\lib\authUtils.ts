import { auth } from './firebase'
import Swal from 'sweetalert2'

/**
 * Preserve user session data before logout (for auto-logout recovery)
 */
export function preserveUserSessionData(userId: string) {
  try {
    const today = new Date().toDateString()
    const sessionKey = `video_session_${userId}_${today}`
    const watchTimesKey = `watch_times_${userId}_${today}`
    const dailyWatchTimesKey = `daily_watch_times_${userId}_${today}`

    // Get current session data
    const sessionCount = localStorage.getItem(sessionKey)
    const watchTimes = localStorage.getItem(watchTimesKey)
    const dailyWatchTimes = localStorage.getItem(dailyWatchTimesKey)

    // Store in backup keys that won't be cleared
    if (sessionCount) {
      localStorage.setItem(`backup_${sessionKey}`, sessionCount)
    }
    if (watchTimes) {
      localStorage.setItem(`backup_${watchTimesKey}`, watchTimes)
    }
    if (dailyWatchTimes) {
      localStorage.setItem(`backup_${dailyWatchTimesKey}`, dailyWatchTimes)
    }

    // Store timestamp of backup
    localStorage.setItem(`backup_timestamp_${userId}`, Date.now().toString())

    console.log('Session data preserved for user:', userId, {
      sessionCount,
      watchTimesCount: watchTimes ? JSON.parse(watchTimes).length : 0,
      dailyWatchTimesCount: dailyWatchTimes ? JSON.parse(dailyWatchTimes).length : 0
    })
  } catch (error) {
    console.error('Error preserving session data:', error)
  }
}

/**
 * Restore user session data after login (for auto-logout recovery)
 */
export function restoreUserSessionData(userId: string) {
  try {
    const today = new Date().toDateString()
    const sessionKey = `video_session_${userId}_${today}`
    const watchTimesKey = `watch_times_${userId}_${today}`
    const dailyWatchTimesKey = `daily_watch_times_${userId}_${today}`

    // Check if we have backup data from today
    const backupTimestamp = localStorage.getItem(`backup_timestamp_${userId}`)
    if (!backupTimestamp) return false

    const backupTime = new Date(parseInt(backupTimestamp))
    const isFromToday = backupTime.toDateString() === today

    if (!isFromToday) {
      // Backup is from a different day, don't restore
      clearBackupData(userId)
      return false
    }

    // Restore session data
    const backupSessionCount = localStorage.getItem(`backup_${sessionKey}`)
    const backupWatchTimes = localStorage.getItem(`backup_${watchTimesKey}`)
    const backupDailyWatchTimes = localStorage.getItem(`backup_${dailyWatchTimesKey}`)

    let restored = false

    if (backupSessionCount) {
      localStorage.setItem(sessionKey, backupSessionCount)
      restored = true
    }
    if (backupWatchTimes) {
      localStorage.setItem(watchTimesKey, backupWatchTimes)
      restored = true
    }
    if (backupDailyWatchTimes) {
      localStorage.setItem(dailyWatchTimesKey, backupDailyWatchTimes)
      restored = true
    }

    if (restored) {
      console.log('Session data restored for user:', userId, {
        sessionCount: backupSessionCount,
        watchTimesCount: backupWatchTimes ? JSON.parse(backupWatchTimes).length : 0,
        dailyWatchTimesCount: backupDailyWatchTimes ? JSON.parse(backupDailyWatchTimes).length : 0
      })

      // Clear backup data after successful restore
      clearBackupData(userId)
      return true
    }

    return false
  } catch (error) {
    console.error('Error restoring session data:', error)
    return false
  }
}

/**
 * Clear backup session data
 */
export function clearBackupData(userId: string) {
  try {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(`backup_`) && key.includes(userId)) {
        localStorage.removeItem(key)
      }
    })
  } catch (error) {
    console.error('Error clearing backup data:', error)
  }
}

/**
 * Clear all user-specific data from localStorage (except backups)
 */
export function clearUserLocalStorage(userId: string, preserveSession: boolean = false) {
  try {
    // Preserve session data if requested (for auto-logout scenarios)
    if (preserveSession) {
      preserveUserSessionData(userId)
    }

    console.log('🧹 Starting comprehensive localStorage cleanup for user:', userId)

    // Get all localStorage keys
    const keys = Object.keys(localStorage)
    let clearedCount = 0

    // Remove user-specific data (but not backup data)
    keys.forEach(key => {
      if (!key.startsWith('backup_') && (
          key.includes(userId) ||
          key.startsWith('video_session_') ||
          key.startsWith('watch_times_') ||
          key.startsWith('daily_watch_times_') ||
          key.startsWith('video_refresh_') ||
          key.startsWith('video_change_notification_') ||
          key.startsWith('leave_') ||
          key.startsWith('notification_') ||
          key.startsWith('wallet_') ||
          key.startsWith('transaction_') ||
          key.startsWith('work_') ||
          key.startsWith('session_') ||
          key.includes('mytube_') ||
          key.includes('user_') ||
          key.includes('_uid_') ||
          key.includes('firebase'))) {
        localStorage.removeItem(key)
        clearedCount++
        console.log('🗑️ Cleared key:', key)
      }
    })

    // Also clear common app data that might contain user info
    const commonKeys = [
      'currentUser',
      'authToken',
      'userSession',
      'appState',
      'videoProgress',
      'sessionData',
      'workSession',
      'walletCache',
      'transactionCache',
      'userPreferences',
      'dashboardCache',
      'profileCache',
      'withdrawalCache',
      'planCache',
      'videoCache',
      'lastActiveUser',
      'currentSession',
      'activeUserId'
    ]

    commonKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key)
        clearedCount++
        console.log('🗑️ Cleared common key:', key)
      }
    })

    console.log(`✅ Local storage cleanup completed for user ${userId}: ${clearedCount} keys cleared`, { preserveSession })
  } catch (error) {
    console.error('Error clearing local storage:', error)
  }
}

/**
 * Clear ALL localStorage data (nuclear option for complete cleanup)
 */
export function clearAllLocalStorage(preserveBackups: boolean = false) {
  try {
    console.log('🧹 Starting COMPLETE localStorage cleanup...')

    if (preserveBackups) {
      // Get backup keys first
      const keys = Object.keys(localStorage)
      const backupData: { [key: string]: string } = {}

      keys.forEach(key => {
        if (key.startsWith('backup_')) {
          backupData[key] = localStorage.getItem(key) || ''
        }
      })

      // Clear everything
      localStorage.clear()

      // Restore backups
      Object.entries(backupData).forEach(([key, value]) => {
        localStorage.setItem(key, value)
      })

      console.log(`✅ Complete cleanup done, preserved ${Object.keys(backupData).length} backup keys`)
    } else {
      // Nuclear option - clear everything
      localStorage.clear()
      console.log('✅ Complete localStorage cleared (nuclear option)')
    }
  } catch (error) {
    console.error('Error in complete localStorage cleanup:', error)
  }
}

/**
 * Isolate user session data to prevent cross-user contamination
 */
export function isolateUserSession(userId: string) {
  try {
    console.log('🔒 Isolating session for user:', userId)

    // Clear any existing session data from other users
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      // Remove session data that doesn't belong to current user
      if ((key.startsWith('video_session_') ||
           key.startsWith('watch_times_') ||
           key.startsWith('daily_watch_times_') ||
           key.startsWith('video_refresh_') ||
           key.startsWith('video_change_notification_')) &&
          !key.includes(userId)) {
        localStorage.removeItem(key)
        console.log('🗑️ Removed other user data:', key)
      }
    })

    // Set current active user marker
    localStorage.setItem('activeUserId', userId)
    localStorage.setItem('sessionIsolatedAt', Date.now().toString())

    console.log('✅ Session isolated for user:', userId)
  } catch (error) {
    console.error('Error isolating user session:', error)
  }
}

/**
 * Handle user logout with confirmation and cleanup
 */
export async function handleUserLogout(userId?: string, redirectPath: string = '/login') {
  try {
    const result = await Swal.fire({
      title: 'Logout Confirmation',
      text: 'Are you sure you want to logout?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Logout',
      cancelButtonText: 'Cancel'
    })

    if (result.isConfirmed) {
      // Clear user-specific local storage data (don't preserve for manual logout)
      if (userId) {
        clearUserLocalStorage(userId, false)
      }

      // Sign out from Firebase
      await auth.signOut()

      // Show success message
      Swal.fire({
        icon: 'success',
        title: 'Logged Out Successfully',
        text: 'You have been logged out. Redirecting...',
        timer: 2000,
        showConfirmButton: false
      }).then(() => {
        // Redirect to specified path
        window.location.href = redirectPath
      })

      return true
    }

    return false
  } catch (error) {
    console.error('Logout error:', error)
    Swal.fire({
      icon: 'error',
      title: 'Logout Failed',
      text: 'There was an error logging out. Please try again.',
    })
    return false
  }
}

/**
 * Quick logout without confirmation (for emergency/auto-logout cases)
 */
export async function quickLogout(userId?: string, redirectPath: string = '/login', preserveSession: boolean = true) {
  try {
    // Clear user-specific local storage data (preserve session for auto-logout)
    if (userId) {
      clearUserLocalStorage(userId, preserveSession)
    }

    // Sign out from Firebase
    await auth.signOut()

    // Redirect immediately
    window.location.href = redirectPath
  } catch (error) {
    console.error('Quick logout error:', error)
    // Force redirect even if logout fails
    window.location.href = redirectPath
  }
}

/**
 * Clear session data on app start (useful for cleanup)
 */
export function clearExpiredSessions() {
  try {
    const keys = Object.keys(localStorage)
    const today = new Date().toDateString()

    keys.forEach(key => {
      // Clear old session data (not from today)
      if (key.startsWith('video_session_') || key.startsWith('watch_times_') || key.startsWith('backup_')) {
        const storedData = localStorage.getItem(key)
        if (storedData) {
          try {
            // Check if it's from today
            if (!key.includes(today)) {
              localStorage.removeItem(key)
              console.log('Cleared expired session:', key)
            }
          } catch (error) {
            // If we can't parse it, remove it
            localStorage.removeItem(key)
          }
        }
      }
    })

    // Also clear old backup timestamps
    keys.forEach(key => {
      if (key.startsWith('backup_timestamp_')) {
        const timestamp = localStorage.getItem(key)
        if (timestamp) {
          const backupDate = new Date(parseInt(timestamp))
          if (backupDate.toDateString() !== today) {
            localStorage.removeItem(key)
            console.log('Cleared expired backup timestamp:', key)
          }
        }
      }
    })
  } catch (error) {
    console.error('Error clearing expired sessions:', error)
  }
}

/**
 * Get user session info from localStorage with validation
 */
export function getUserSessionInfo(userId: string) {
  try {
    // Validate that this session belongs to the current user
    if (!validateUserSession(userId)) {
      console.warn('⚠️ Session validation failed for user:', userId)
      return {
        videoCount: 0,
        watchTimes: [],
        hasActiveSession: false
      }
    }

    const today = new Date().toDateString()
    const sessionKey = `video_session_${userId}_${today}`
    const watchTimesKey = `watch_times_${userId}_${today}`

    const sessionCount = localStorage.getItem(sessionKey)
    const watchTimes = localStorage.getItem(watchTimesKey)

    return {
      videoCount: sessionCount ? parseInt(sessionCount) : 0,
      watchTimes: watchTimes ? JSON.parse(watchTimes) : [],
      hasActiveSession: !!(sessionCount || watchTimes)
    }
  } catch (error) {
    console.error('Error getting session info:', error)
    return {
      videoCount: 0,
      watchTimes: [],
      hasActiveSession: false
    }
  }
}

/**
 * Validate that the current session belongs to the specified user
 */
export function validateUserSession(userId: string): boolean {
  try {
    const activeUserId = localStorage.getItem('activeUserId')

    // If no active user is set, this might be a legacy session
    if (!activeUserId) {
      console.warn('⚠️ No active user ID found in localStorage')
      return false
    }

    // Check if the active user matches the requested user
    if (activeUserId !== userId) {
      console.warn('⚠️ Session user mismatch:', { activeUserId, requestedUserId: userId })
      return false
    }

    return true
  } catch (error) {
    console.error('Error validating user session:', error)
    return false
  }
}

/**
 * Secure localStorage operations with user validation
 */
export function secureLocalStorageGet(key: string, userId: string): string | null {
  try {
    // Validate session first
    if (!validateUserSession(userId)) {
      console.warn('⚠️ Blocked localStorage access due to session validation failure')
      return null
    }

    // Ensure the key belongs to this user (if it's user-specific)
    if (key.includes('_') && !key.includes(userId) &&
        (key.startsWith('video_') || key.startsWith('watch_') || key.startsWith('daily_'))) {
      console.warn('⚠️ Blocked access to other user\'s data:', key)
      return null
    }

    return localStorage.getItem(key)
  } catch (error) {
    console.error('Error in secure localStorage get:', error)
    return null
  }
}

/**
 * Secure localStorage set with user validation
 */
export function secureLocalStorageSet(key: string, value: string, userId: string): boolean {
  try {
    // Validate session first
    if (!validateUserSession(userId)) {
      console.warn('⚠️ Blocked localStorage write due to session validation failure')
      return false
    }

    // Ensure the key belongs to this user (if it's user-specific)
    if (key.includes('_') && !key.includes(userId) &&
        (key.startsWith('video_') || key.startsWith('watch_') || key.startsWith('daily_'))) {
      console.warn('⚠️ Blocked write to other user\'s data:', key)
      return false
    }

    localStorage.setItem(key, value)
    return true
  } catch (error) {
    console.error('Error in secure localStorage set:', error)
    return false
  }
}
