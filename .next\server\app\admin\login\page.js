(()=>{var e={};e.id=2116,e.ids=[2116],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},37823:(e,r,s)=>{Promise.resolve().then(s.bind(s,64248))},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64248:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>p});var t=s(60687),i=s(43210),n=s(85814),a=s.n(n),o=s(30474),l=s(63385),d=s(33784),c=s(87979),u=s(77567);function p(){let{user:e,loading:r}=(0,c.hD)(),[s,n]=(0,i.useState)(""),[p,m]=(0,i.useState)(""),[x,h]=(0,i.useState)(!1),[f,b]=(0,i.useState)(!1),g=async e=>{if(e.preventDefault(),!s||!p)return void u.A.fire({icon:"error",title:"Error",text:"Please fill in all fields"});h(!0);try{let e=(await (0,l.x9)(d.j2,s,p)).user;if(!["<EMAIL>","<EMAIL>"].includes(e.email||""))throw await d.j2.signOut(),Error("Access denied. Admin privileges required.")}catch(r){console.error("Admin login error:",r);let e="An error occurred during login";if(r.message.includes("Access denied"))e="Access denied. Admin privileges required.";else switch(r.code){case"auth/user-not-found":e="No admin account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This admin account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=r.message||"Admin login failed"}u.A.fire({icon:"error",title:"Admin Login Failed",text:e}),m("")}finally{h(!1)}};return r?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,t.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,t.jsx)(o.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Panel"}),(0,t.jsx)("p",{className:"text-white/80",children:"Sign in to access admin dashboard"})]}),(0,t.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:[(0,t.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Admin Email"]}),(0,t.jsx)("input",{type:"email",id:"email",value:s,onChange:e=>n(e.target.value),className:"form-input",placeholder:"Enter admin email",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:[(0,t.jsx)("i",{className:"fas fa-lock mr-2"}),"Password"]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:f?"text":"password",id:"password",value:p,onChange:e=>m(e.target.value),className:"form-input pr-12",placeholder:"Enter admin password",required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>b(!f),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",children:(0,t.jsx)("i",{className:`fas ${f?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:x,className:"w-full btn-primary flex items-center justify-center",children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Signing in..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]})})]}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-red-500/20 rounded-lg border border-red-500/30",children:(0,t.jsxs)("div",{className:"flex items-center text-red-300",children:[(0,t.jsx)("i",{className:"fas fa-shield-alt mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"This is a secure admin area. Only authorized personnel can access this panel."})]})}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)(a(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},64821:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(65239),i=s(48088),n=s(88170),a=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d={children:["",{children:["admin",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,76158)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\login\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/login/page",pathname:"/admin/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76158:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\login\\page.tsx","default")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98503:(e,r,s)=>{Promise.resolve().then(s.bind(s,76158))}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6204,6958,7567,8441,7979],()=>s(64821));module.exports=t})();