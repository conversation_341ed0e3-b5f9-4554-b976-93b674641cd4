'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuthState } from '@/hooks/useAuth'
import { useBlockingNotifications } from '@/hooks/useBlockingNotifications'
import { getUserData, updateUserData } from '@/lib/dataService'
import { optimizedService } from '@/lib/optimizedDataService'
import { handleUserLogout } from '@/lib/authUtils'
import { updatePassword, updateEmail, EmailAuthProvider, reauthenticateWithCredential } from 'firebase/auth'
import { auth } from '@/lib/firebase'
import BlockingNotificationModal from '@/components/BlockingNotificationModal'
import Swal from 'sweetalert2'

interface UserData {
  name: string
  email: string
  mobile: string
  referralCode: string
  referredBy: string
  plan: string
  planExpiry: Date | null
  activeDays: number
  joinedDate: Date
}

interface EditData {
  name: string
  email: string
  mobile: string
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export default function ProfilePage() {
  const { user, loading } = useAuthState()
  const { hasBlockingNotifications, isChecking, markAllAsRead } = useBlockingNotifications(user?.uid || null)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      window.location.href = '/login'
    }
  }, [user, loading])
  const [userData, setUserData] = useState<UserData | null>(null)
  const [dataLoading, setDataLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [editData, setEditData] = useState<EditData>({
    name: '',
    email: '',
    mobile: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [isSaving, setIsSaving] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isChangingPassword, setIsChangingPassword] = useState(false)
  const [activeDays, setActiveDays] = useState(0)

  useEffect(() => {
    if (user) {
      loadUserData()
    }
  }, [user])

  const loadUserData = async () => {
    try {
      setDataLoading(true)
      const data = await getUserData(user!.uid)
      setUserData(data)
      setEditData({
        name: data?.name || '',
        email: data?.email || '',
        mobile: data?.mobile || '',
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })

      // Update active days to ensure live calculation for display
      if (data) {
        try {
          const { updateUserActiveDays, isUserPlanExpired } = await import('@/lib/dataService')
          await updateUserActiveDays(user!.uid)

          // Reload user data to get updated active days
          const updatedUserData = await getUserData(user!.uid)
          setUserData(updatedUserData)

          // Get live active days for display
          const { getLiveActiveDays } = await import('@/lib/dataService')
          const liveActiveDays = await getLiveActiveDays(user!.uid)
          setActiveDays(liveActiveDays)

          // Update edit data with fresh user data
          setEditData({
            name: updatedUserData?.name || '',
            email: updatedUserData?.email || '',
            mobile: updatedUserData?.mobile || '',
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
          })
        } catch (error) {
          console.error('Error updating active days:', error)
        }
      }
    } catch (error) {
      console.error('Error loading user data:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to load profile data. Please try again.',
      })
    } finally {
      setDataLoading(false)
    }
  }

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setIsChangingPassword(false)
    setEditData({
      name: userData?.name || '',
      email: userData?.email || '',
      mobile: userData?.mobile || '',
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
  }

  const handleSave = async () => {
    // Validate inputs
    if (!editData.name.trim()) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Name is required',
      })
      return
    }

    if (editData.mobile && !/^[6-9]\d{9}$/.test(editData.mobile)) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Please enter a valid 10-digit mobile number',
      })
      return
    }

    if (editData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editData.email)) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Please enter a valid email address',
      })
      return
    }

    // Password validation if changing password
    if (isChangingPassword) {
      if (!editData.currentPassword) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Current password is required to change password',
        })
        return
      }

      if (!editData.newPassword || editData.newPassword.length < 6) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'New password must be at least 6 characters long',
        })
        return
      }

      if (editData.newPassword !== editData.confirmPassword) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'New password and confirm password do not match',
        })
        return
      }
    }

    try {
      setIsSaving(true)

      // Handle password change first (requires reauthentication)
      if (isChangingPassword && editData.currentPassword && editData.newPassword) {
        try {
          // Reauthenticate user
          const credential = EmailAuthProvider.credential(user!.email!, editData.currentPassword)
          await reauthenticateWithCredential(user!, credential)

          // Update password
          await updatePassword(user!, editData.newPassword)

          Swal.fire({
            icon: 'success',
            title: 'Password Updated',
            text: 'Your password has been updated successfully',
            timer: 2000,
            showConfirmButton: false
          })
        } catch (error: any) {
          console.error('Error updating password:', error)
          let errorMessage = 'Failed to update password. Please try again.'

          if (error.code === 'auth/wrong-password') {
            errorMessage = 'Current password is incorrect'
          } else if (error.code === 'auth/too-many-requests') {
            errorMessage = 'Too many failed attempts. Please try again later.'
          }

          Swal.fire({
            icon: 'error',
            title: 'Password Update Failed',
            text: errorMessage,
          })
          return
        }
      }

      // Handle email change
      if (editData.email !== userData?.email && editData.email) {
        try {
          await updateEmail(user!, editData.email)
        } catch (error: any) {
          console.error('Error updating email:', error)
          let errorMessage = 'Failed to update email. Please try again.'

          if (error.code === 'auth/email-already-in-use') {
            errorMessage = 'This email is already in use by another account'
          } else if (error.code === 'auth/requires-recent-login') {
            errorMessage = 'Please log out and log back in before changing your email'
          }

          Swal.fire({
            icon: 'error',
            title: 'Email Update Failed',
            text: errorMessage,
          })
          return
        }
      }

      // Update other profile data
      const updateData: any = {
        name: editData.name.trim(),
        mobile: editData.mobile
      }

      if (editData.email !== userData?.email && editData.email) {
        updateData.email = editData.email
      }

      await updateUserData(user!.uid, updateData)

      setUserData(prev => prev ? {
        ...prev,
        ...updateData
      } : null)

      setIsEditing(false)
      setIsChangingPassword(false)

      Swal.fire({
        icon: 'success',
        title: 'Profile Updated',
        text: 'Your profile has been updated successfully',
        timer: 2000,
        showConfirmButton: false
      })
    } catch (error) {
      console.error('Error updating profile:', error)
      Swal.fire({
        icon: 'error',
        title: 'Update Failed',
        text: 'Failed to update profile. Please try again.',
      })
    } finally {
      setIsSaving(false)
    }
  }

  const copyReferralCode = () => {
    if (userData?.referralCode) {
      navigator.clipboard.writeText(userData.referralCode)
      Swal.fire({
        icon: 'success',
        title: 'Copied!',
        text: 'Referral code copied to clipboard',
        timer: 1500,
        showConfirmButton: false
      })
    }
  }

  const shareReferralLink = () => {
    if (userData?.referralCode) {
      const referralLink = `${window.location.origin}/register?ref=${userData.referralCode}`
      
      if (navigator.share) {
        navigator.share({
          title: 'Join MyTube and Start Earning',
          text: 'Join MyTube using my referral code and start earning money by watching videos!',
          url: referralLink
        })
      } else {
        navigator.clipboard.writeText(referralLink)
        Swal.fire({
          icon: 'success',
          title: 'Link Copied!',
          text: 'Referral link copied to clipboard',
          timer: 2000,
          showConfirmButton: false
        })
      }
    }
  }

  const handleLogout = () => {
    handleUserLogout(user?.uid, '/login')
  }

  if (loading || dataLoading || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">
            {loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading profile...'}
          </p>
        </div>
      </div>
    )
  }

  // Show blocking notifications if any exist
  if (hasBlockingNotifications && user) {
    return (
      <BlockingNotificationModal
        userId={user.uid}
        onAllRead={markAllAsRead}
      />
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between">
          <Link href="/dashboard" className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </Link>
          <h1 className="text-xl font-bold text-white">My Profile</h1>
          <button
            onClick={handleLogout}
            className="glass-button px-4 py-2 text-white"
          >
            <i className="fas fa-sign-out-alt mr-2"></i>
            Logout
          </button>
        </div>
      </header>

      {/* Profile Information */}
      <div className="glass-card p-6 mb-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">
            <i className="fas fa-user mr-2"></i>
            Profile Information
          </h2>
          {!isEditing && (
            <button
              onClick={handleEdit}
              className="glass-button px-4 py-2 text-white"
            >
              <i className="fas fa-edit mr-2"></i>
              Edit
            </button>
          )}
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-white font-medium mb-2">Full Name</label>
            {isEditing ? (
              <input
                type="text"
                value={editData.name}
                onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                className="form-input"
                placeholder="Enter your full name"
              />
            ) : (
              <p className="text-white/80 bg-white/10 p-3 rounded-lg">
                {userData?.name || 'Not provided'}
              </p>
            )}
          </div>

          <div>
            <label className="block text-white font-medium mb-2">Email Address</label>
            {isEditing ? (
              <input
                type="email"
                value={editData.email}
                onChange={(e) => setEditData(prev => ({ ...prev, email: e.target.value }))}
                className="form-input"
                placeholder="Enter your email address"
              />
            ) : (
              <p className="text-white/80 bg-white/10 p-3 rounded-lg">
                {userData?.email || 'Not provided'}
              </p>
            )}
          </div>

          <div>
            <label className="block text-white font-medium mb-2">Mobile Number</label>
            {isEditing ? (
              <input
                type="tel"
                value={editData.mobile}
                onChange={(e) => setEditData(prev => ({ ...prev, mobile: e.target.value }))}
                className="form-input"
                placeholder="Enter 10-digit mobile number"
                maxLength={10}
              />
            ) : (
              <p className="text-white/80 bg-white/10 p-3 rounded-lg">
                {userData?.mobile || 'Not provided'}
              </p>
            )}
          </div>

          <div>
            <label className="block text-white font-medium mb-2">Member Since</label>
            <p className="text-white/80 bg-white/10 p-3 rounded-lg">
              {userData?.joinedDate?.toLocaleDateString() || 'Unknown'}
            </p>
          </div>

          {/* Password Change Section */}
          {isEditing && (
            <div className="border-t border-white/20 pt-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Change Password</h3>
                <button
                  onClick={() => setIsChangingPassword(!isChangingPassword)}
                  className={`glass-button px-4 py-2 text-white ${isChangingPassword ? 'bg-red-500/20' : 'bg-blue-500/20'}`}
                >
                  <i className={`fas ${isChangingPassword ? 'fa-times' : 'fa-key'} mr-2`}></i>
                  {isChangingPassword ? 'Cancel' : 'Change Password'}
                </button>
              </div>

              {isChangingPassword && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-white font-medium mb-2">Current Password</label>
                    <div className="relative">
                      <input
                        type={showCurrentPassword ? "text" : "password"}
                        value={editData.currentPassword}
                        onChange={(e) => setEditData(prev => ({ ...prev, currentPassword: e.target.value }))}
                        className="form-input pr-12"
                        placeholder="Enter your current password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white"
                      >
                        <i className={`fas ${showCurrentPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">New Password</label>
                    <div className="relative">
                      <input
                        type={showNewPassword ? "text" : "password"}
                        value={editData.newPassword}
                        onChange={(e) => setEditData(prev => ({ ...prev, newPassword: e.target.value }))}
                        className="form-input pr-12"
                        placeholder="Enter new password (min 6 characters)"
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white"
                      >
                        <i className={`fas ${showNewPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">Confirm New Password</label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? "text" : "password"}
                        value={editData.confirmPassword}
                        onChange={(e) => setEditData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className="form-input pr-12"
                        placeholder="Confirm your new password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white"
                      >
                        <i className={`fas ${showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                      </button>
                    </div>
                  </div>

                  <div className="bg-yellow-500/20 p-3 rounded-lg">
                    <p className="text-yellow-300 text-sm">
                      <i className="fas fa-exclamation-triangle mr-2"></i>
                      Password must be at least 6 characters long. You will need to log in again after changing your password.
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {isEditing && (
          <div className="flex gap-4 mt-6">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="btn-primary flex-1"
            >
              {isSaving ? (
                <>
                  <div className="spinner mr-2 w-5 h-5"></div>
                  Saving...
                </>
              ) : (
                <>
                  <i className="fas fa-save mr-2"></i>
                  Save Changes
                </>
              )}
            </button>
            <button
              onClick={handleCancel}
              className="btn-secondary flex-1"
            >
              <i className="fas fa-times mr-2"></i>
              Cancel
            </button>
          </div>
        )}
      </div>

      {/* Plan Information */}
      <div className="glass-card p-6 mb-6">
        <h2 className="text-xl font-bold text-white mb-4">
          <i className="fas fa-crown mr-2"></i>
          Plan Information
        </h2>
        
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-white font-medium mb-2">Current Plan</label>
            <p className="text-white/80 bg-white/10 p-3 rounded-lg">
              {userData?.plan || 'Trial'}
            </p>
          </div>
          
          <div>
            <label className="block text-white font-medium mb-2">Active Days</label>
            <p className="text-white/80 bg-white/10 p-3 rounded-lg">
              {activeDays} days
            </p>
          </div>
          
          {userData?.planExpiry && (
            <div className="md:col-span-2">
              <label className="block text-white font-medium mb-2">Plan Expires</label>
              <p className="text-white/80 bg-white/10 p-3 rounded-lg">
                {userData.planExpiry.toLocaleDateString()}
              </p>
            </div>
          )}
        </div>

        <div className="mt-4">
          <Link href="/plans" className="btn-primary">
            <i className="fas fa-upgrade mr-2"></i>
            Upgrade Plan
          </Link>
        </div>
      </div>

      {/* Referral Information */}
      <div className="glass-card p-6">
        <h2 className="text-xl font-bold text-white mb-4">
          <i className="fas fa-users mr-2"></i>
          Referral Information
        </h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-white font-medium mb-2">Your Referral Code</label>
            <div className="flex gap-2">
              <p className="text-white/80 bg-white/10 p-3 rounded-lg flex-1 font-mono">
                {userData?.referralCode || 'Not generated'}
              </p>
              <button
                onClick={copyReferralCode}
                className="glass-button px-4 py-2 text-white"
                title="Copy referral code"
              >
                <i className="fas fa-copy"></i>
              </button>
            </div>
          </div>

          {userData?.referredBy && (
            <div>
              <label className="block text-white font-medium mb-2">Referred By</label>
              <p className="text-white/80 bg-white/10 p-3 rounded-lg font-mono">
                {userData.referredBy}
              </p>
            </div>
          )}

          <div className="flex gap-4">
            <button
              onClick={shareReferralLink}
              className="btn-primary flex-1"
            >
              <i className="fas fa-share mr-2"></i>
              Share Referral Link
            </button>
            <Link href="/refer" className="btn-secondary flex-1 text-center">
              <i className="fas fa-users mr-2"></i>
              View Referrals
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
