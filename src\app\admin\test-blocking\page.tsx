'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRequireAdmin } from '@/hooks/useAuth'
import { addNotification } from '@/lib/dataService'
import Swal from 'sweetalert2'

export default function TestBlockingNotificationPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const [isSending, setIsSending] = useState(false)

  const sendTestBlockingNotification = async () => {
    try {
      setIsSending(true)

      await addNotification({
        title: '🚨 Important System Update',
        message: 'This is a test notification. Users must acknowledge this message before they can continue using the platform. This ensures important announcements are seen by all users.',
        type: 'warning',
        targetUsers: 'all',
        userIds: [],
        createdBy: user?.email || 'admin'
      })

      Swal.fire({
        icon: 'success',
        title: 'Notification Sent!',
        text: 'Test notification sent to all users. Users will need to acknowledge this before accessing any features.',
        timer: 4000,
        showConfirmButton: false
      })
    } catch (error) {
      console.error('Error sending blocking notification:', error)
      Swal.fire({
        icon: 'error',
        title: 'Send Failed',
        text: 'Failed to send blocking notification. Please try again.',
      })
    } finally {
      setIsSending(false)
    }
  }

  const sendRegularNotification = async () => {
    try {
      setIsSending(true)

      await addNotification({
        title: 'Another Test Notification',
        message: 'This is another test notification. All notifications are now blocking and users must acknowledge them.',
        type: 'info',
        targetUsers: 'all',
        userIds: [],
        createdBy: user?.email || 'admin'
      })

      Swal.fire({
        icon: 'success',
        title: 'Notification Sent!',
        text: 'Test notification sent to all users.',
        timer: 3000,
        showConfirmButton: false
      })
    } catch (error) {
      console.error('Error sending notification:', error)
      Swal.fire({
        icon: 'error',
        title: 'Send Failed',
        text: 'Failed to send notification. Please try again.',
      })
    } finally {
      setIsSending(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Link
              href="/admin"
              className="text-gray-500 hover:text-gray-700"
            >
              <i className="fas fa-arrow-left text-xl"></i>
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Test Blocking Notifications</h1>
          </div>
        </div>
      </header>

      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h2 className="text-lg font-bold text-blue-900 mb-3">
              <i className="fas fa-info-circle mr-2"></i>
              How to Test Notifications
            </h2>
            <div className="text-blue-800 space-y-2">
              <p><strong>1.</strong> Send a notification using the buttons below</p>
              <p><strong>2.</strong> Open a new tab and go to the user dashboard or work page</p>
              <p><strong>3.</strong> You should see a full-page modal that blocks all activities until acknowledged</p>
              <p><strong>4.</strong> Click "Acknowledge" to dismiss the notification</p>
              <p><strong>5.</strong> All notifications are now blocking/mandatory for better user engagement</p>
            </div>
          </div>

          {/* Test Buttons */}
          <div className="grid md:grid-cols-2 gap-6">
            {/* Warning Notification */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  🚨 Warning Notification
                </h3>
                <p className="text-gray-600 mb-4">
                  Sends a warning notification that users must acknowledge before continuing
                </p>
                <button
                  onClick={sendTestBlockingNotification}
                  disabled={isSending}
                  className="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50"
                >
                  {isSending ? (
                    <>
                      <div className="spinner w-4 h-4 mr-2 inline-block"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-paper-plane mr-2"></i>
                      Send Warning Notification
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Info Notification */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="fas fa-bell text-blue-600 text-2xl"></i>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  📢 Info Notification
                </h3>
                <p className="text-gray-600 mb-4">
                  Sends an info notification that users must acknowledge before continuing
                </p>
                <button
                  onClick={sendRegularNotification}
                  disabled={isSending}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50"
                >
                  {isSending ? (
                    <>
                      <div className="spinner w-4 h-4 mr-2 inline-block"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-paper-plane mr-2"></i>
                      Send Info Notification
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Features List */}
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">
              <i className="fas fa-check-circle mr-2 text-green-500"></i>
              Notification Features (All Blocking)
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center">
                  <i className="fas fa-shield-alt text-green-500 mr-3"></i>
                  <span>Blocks all user activities until acknowledged</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-eye text-green-500 mr-3"></i>
                  <span>Forces users to read important announcements</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-users text-green-500 mr-3"></i>
                  <span>Can target all users or specific users</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center">
                  <i className="fas fa-mobile-alt text-green-500 mr-3"></i>
                  <span>Works on all pages (dashboard, work, wallet, etc.)</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-chart-line text-green-500 mr-3"></i>
                  <span>Progress indicator for multiple notifications</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-clock text-green-500 mr-3"></i>
                  <span>Persistent until user acknowledges</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
