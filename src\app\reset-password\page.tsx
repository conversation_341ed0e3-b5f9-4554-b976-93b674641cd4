'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { confirmPasswordReset, verifyPasswordResetCode } from 'firebase/auth'
import { auth } from '@/lib/firebase'
import { useAuthState } from '@/hooks/useAuth'
import Swal from 'sweetalert2'

export default function ResetPasswordPage() {
  const { user, loading } = useAuthState()
  const [oobCode, setOobCode] = useState('')
  const [email, setEmail] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isVerifying, setIsVerifying] = useState(true)
  const [isValidCode, setIsValidCode] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [resetComplete, setResetComplete] = useState(false)

  useEffect(() => {
    if (user && !loading) {
      window.location.href = '/dashboard'
    }
  }, [user, loading])

  useEffect(() => {
    // Get the reset code from URL parameters
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('oobCode')
    
    if (code) {
      setOobCode(code)
      verifyResetCode(code)
    } else {
      setIsVerifying(false)
      Swal.fire({
        icon: 'error',
        title: 'Invalid Reset Link',
        text: 'This password reset link is invalid or has expired. Please request a new one.',
        confirmButtonText: 'Go to Forgot Password'
      }).then(() => {
        window.location.href = '/forgot-password'
      })
    }
  }, [])

  const verifyResetCode = async (code: string) => {
    try {
      setIsVerifying(true)
      const userEmail = await verifyPasswordResetCode(auth, code)
      setEmail(userEmail)
      setIsValidCode(true)
    } catch (error: any) {
      console.error('Code verification error:', error)
      
      let message = 'This password reset link is invalid or has expired.'
      
      switch (error.code) {
        case 'auth/expired-action-code':
          message = 'This password reset link has expired. Please request a new one.'
          break
        case 'auth/invalid-action-code':
          message = 'This password reset link is invalid. Please request a new one.'
          break
        case 'auth/user-disabled':
          message = 'This account has been disabled. Please contact support.'
          break
        case 'auth/user-not-found':
          message = 'No account found for this reset link. The account may have been deleted.'
          break
      }

      Swal.fire({
        icon: 'error',
        title: 'Invalid Reset Link',
        text: message,
        confirmButtonText: 'Request New Reset Link'
      }).then(() => {
        window.location.href = '/forgot-password'
      })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newPassword.trim()) {
      Swal.fire({
        icon: 'error',
        title: 'Password Required',
        text: 'Please enter a new password',
      })
      return
    }

    if (newPassword.length < 6) {
      Swal.fire({
        icon: 'error',
        title: 'Password Too Short',
        text: 'Password must be at least 6 characters long',
      })
      return
    }

    if (newPassword !== confirmPassword) {
      Swal.fire({
        icon: 'error',
        title: 'Passwords Don\'t Match',
        text: 'Please make sure both passwords match',
      })
      return
    }

    setIsLoading(true)

    try {
      await confirmPasswordReset(auth, oobCode, newPassword)
      
      setResetComplete(true)
      
      Swal.fire({
        icon: 'success',
        title: 'Password Reset Successful!',
        text: 'Your password has been updated successfully. You can now login with your new password.',
        confirmButtonText: 'Go to Login',
        confirmButtonColor: '#3b82f6'
      }).then(() => {
        window.location.href = '/login'
      })

    } catch (error: any) {
      console.error('Password reset error:', error)
      
      let message = 'An error occurred while resetting your password'
      
      switch (error.code) {
        case 'auth/expired-action-code':
          message = 'This password reset link has expired. Please request a new one.'
          break
        case 'auth/invalid-action-code':
          message = 'This password reset link is invalid. Please request a new one.'
          break
        case 'auth/weak-password':
          message = 'Password is too weak. Please choose a stronger password.'
          break
        default:
          message = error.message || 'Failed to reset password'
      }

      Swal.fire({
        icon: 'error',
        title: 'Reset Failed',
        text: message,
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (loading || isVerifying) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">
            {isVerifying ? 'Verifying reset link...' : 'Loading...'}
          </p>
        </div>
      </div>
    )
  }

  if (!isValidCode) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="fas fa-times text-red-400 text-2xl"></i>
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">Invalid Reset Link</h2>
          <p className="text-white/80 mb-6">This password reset link is invalid or has expired.</p>
          <Link href="/forgot-password" className="btn-primary">
            Request New Reset Link
          </Link>
        </div>
      </div>
    )
  }

  return (
    <main className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <Image
              src="/logo.png"
              alt="MyTube"
              width={120}
              height={120}
              className="mx-auto mb-4"
            />
          </Link>
          <h1 className="text-3xl font-bold text-white mb-2">Set New Password</h1>
          <p className="text-white/80">
            Enter your new password for <span className="font-semibold text-blue-400">{email}</span>
          </p>
        </div>

        <form onSubmit={handleSubmit} className="glass-card p-8 space-y-6">
          {/* New Password Input */}
          <div>
            <label htmlFor="newPassword" className="block text-white font-medium mb-2">
              New Password
            </label>
            <div className="relative">
              <i className="fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"></i>
              <input
                type={showPassword ? 'text' : 'password'}
                id="newPassword"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40"
                placeholder="Enter new password"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white"
              >
                <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
              </button>
            </div>
          </div>

          {/* Confirm Password Input */}
          <div>
            <label htmlFor="confirmPassword" className="block text-white font-medium mb-2">
              Confirm New Password
            </label>
            <div className="relative">
              <i className="fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"></i>
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40"
                placeholder="Confirm new password"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white"
              >
                <i className={`fas ${showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
              </button>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full btn-primary flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <div className="spinner mr-2 w-5 h-5"></div>
                Updating Password...
              </>
            ) : (
              <>
                <i className="fas fa-check mr-2"></i>
                Update Password
              </>
            )}
          </button>
        </form>

        {/* Links */}
        <div className="mt-6 text-center">
          <Link
            href="/login"
            className="text-white/80 hover:text-white transition-colors flex items-center justify-center"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Login
          </Link>
        </div>
      </div>
    </main>
  )
}
