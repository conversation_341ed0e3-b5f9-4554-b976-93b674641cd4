'use client'

import { useState } from 'react'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { FIELD_NAMES, COLLECTIONS, generateUniqueReferralCode } from '@/lib/dataService'

export default function DebugRegistrationSimple() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: 'Test User',
    email: '',
    mobile: '9876543210',
    password: 'test123456',
    confirmPassword: 'test123456',
    referralCode: ''
  })

  const addToResult = (text: string) => {
    setResult(prev => prev + text + '\n')
    console.log(text)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const testRegistration = async () => {
    setResult('')
    setIsLoading(true)

    try {
      const testEmail = `test${Date.now()}@example.com`
      const testPassword = 'test123456'
      const testName = 'Test Registration User'
      const testMobile = '9876543210'

      addToResult('🚀 Starting registration test...')
      addToResult(`📧 Email: ${testEmail}`)
      addToResult(`👤 Name: ${testName}`)
      addToResult(`📱 Mobile: ${testMobile}`)
      addToResult(`🔧 Firebase Project: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}`)

      // Step 1: Create Firebase Auth user
      addToResult('\n=== STEP 1: Creating Firebase Auth User ===')
      try {
        const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
        const user = userCredential.user
        addToResult(`✅ Auth user created successfully!`)
        addToResult(`🆔 UID: ${user.uid}`)
        addToResult(`📧 Email: ${user.email}`)
        addToResult(`✅ Email Verified: ${user.emailVerified}`)
      } catch (authError: any) {
        addToResult(`❌ Auth creation failed: ${authError.message}`)
        addToResult(`❌ Auth error code: ${authError.code}`)
        throw authError
      }

      // Step 2: Wait for auth state
      addToResult('\n=== STEP 2: Waiting for Auth State ===')
      await new Promise(resolve => setTimeout(resolve, 2000))
      addToResult(`✅ Auth state propagated`)
      addToResult(`Current auth user: ${auth.currentUser?.uid}`)
      addToResult(`Auth state matches: ${auth.currentUser?.uid === auth.currentUser?.uid}`)

      // Step 3: Generate referral code
      addToResult('\n=== STEP 3: Generating Referral Code ===')
      let userReferralCode: string
      try {
        userReferralCode = await generateUniqueReferralCode()
        addToResult(`✅ Generated referral code: ${userReferralCode}`)
      } catch (refError: any) {
        addToResult(`❌ Referral code generation failed: ${refError.message}`)
        throw refError
      }

      // Step 4: Prepare user data
      addToResult('\n=== STEP 4: Preparing User Data ===')
      const userData = {
        [FIELD_NAMES.name]: testName,
        [FIELD_NAMES.email]: testEmail.toLowerCase(),
        [FIELD_NAMES.mobile]: testMobile,
        [FIELD_NAMES.referralCode]: userReferralCode,
        [FIELD_NAMES.referredBy]: '',
        [FIELD_NAMES.referralBonusCredited]: false,
        [FIELD_NAMES.plan]: 'Trial',
        [FIELD_NAMES.planExpiry]: null,
        [FIELD_NAMES.activeDays]: 1,
        [FIELD_NAMES.joinedDate]: Timestamp.now(),
        [FIELD_NAMES.wallet]: 0,
        [FIELD_NAMES.totalVideos]: 0,
        [FIELD_NAMES.todayVideos]: 0,
        [FIELD_NAMES.lastVideoDate]: null,
        [FIELD_NAMES.videoDuration]: 30,
        status: 'active'
      }

      addToResult(`✅ User data prepared`)
      addToResult(`📊 Data keys: ${Object.keys(userData).join(', ')}`)

      // Step 5: Create Firestore document
      addToResult('\n=== STEP 5: Creating Firestore Document ===')
      const user = auth.currentUser
      if (!user) {
        addToResult(`❌ No current user found`)
        throw new Error('No current user found')
      }

      const userDocRef = doc(db, COLLECTIONS.users, user.uid)
      addToResult(`📍 Document path: ${userDocRef.path}`)
      addToResult(`🔐 Current user UID: ${user.uid}`)
      addToResult(`📧 Current user email: ${user.email}`)

      try {
        await setDoc(userDocRef, userData)
        addToResult(`✅ Firestore document created successfully!`)
      } catch (firestoreError: any) {
        addToResult(`❌ Firestore creation failed: ${firestoreError.message}`)
        addToResult(`❌ Firestore error code: ${firestoreError.code}`)
        addToResult(`❌ Full error: ${JSON.stringify(firestoreError, null, 2)}`)
        throw firestoreError
      }

      // Step 6: Verify document
      addToResult('\n=== STEP 6: Verifying Document ===')
      try {
        const verifyDoc = await getDoc(userDocRef)
        if (verifyDoc.exists()) {
          const data = verifyDoc.data()
          addToResult(`✅ Document verification successful!`)
          addToResult(`📊 Document data keys: ${Object.keys(data).join(', ')}`)
          addToResult(`👤 Name: ${data[FIELD_NAMES.name]}`)
          addToResult(`📧 Email: ${data[FIELD_NAMES.email]}`)
          addToResult(`🎯 Referral Code: ${data[FIELD_NAMES.referralCode]}`)
        } else {
          addToResult(`❌ Document was not created properly`)
          throw new Error('Document verification failed')
        }
      } catch (verifyError: any) {
        addToResult(`❌ Document verification failed: ${verifyError.message}`)
        throw verifyError
      }

      addToResult('\n🎉 Registration test completed successfully!')

    } catch (error: any) {
      addToResult(`\n❌ Registration test failed!`)
      addToResult(`Error: ${error.message}`)
      addToResult(`Code: ${error.code}`)
      addToResult(`Stack: ${error.stack}`)
      console.error('Registration test error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const testFormRegistration = async (e: React.FormEvent) => {
    e.preventDefault()
    setResult('')
    setIsLoading(true)

    try {
      const testEmail = formData.email || `test${Date.now()}@example.com`

      addToResult('🚀 Starting FORM registration test...')
      addToResult(`📧 Email: ${testEmail}`)
      addToResult(`👤 Name: ${formData.name}`)
      addToResult(`📱 Mobile: ${formData.mobile}`)

      // Validation
      if (!formData.name || !testEmail || !formData.mobile || !formData.password) {
        throw new Error('Please fill in all required fields')
      }

      if (formData.password !== formData.confirmPassword) {
        throw new Error('Passwords do not match')
      }

      // Create user
      const userCredential = await createUserWithEmailAndPassword(auth, testEmail, formData.password)
      const user = userCredential.user
      addToResult(`✅ Auth user created: ${user.uid}`)

      // Generate referral code
      const userReferralCode = await generateUniqueReferralCode()
      addToResult(`✅ Referral code: ${userReferralCode}`)

      // Create user data
      const userData = {
        [FIELD_NAMES.name]: formData.name.trim(),
        [FIELD_NAMES.email]: testEmail.toLowerCase(),
        [FIELD_NAMES.mobile]: formData.mobile,
        [FIELD_NAMES.referralCode]: userReferralCode,
        [FIELD_NAMES.referredBy]: formData.referralCode || '',
        [FIELD_NAMES.referralBonusCredited]: false,
        [FIELD_NAMES.plan]: 'Trial',
        [FIELD_NAMES.planExpiry]: null,
        [FIELD_NAMES.activeDays]: 1,
        [FIELD_NAMES.joinedDate]: Timestamp.now(),
        [FIELD_NAMES.wallet]: 0,
        [FIELD_NAMES.totalVideos]: 0,
        [FIELD_NAMES.todayVideos]: 0,
        [FIELD_NAMES.lastVideoDate]: null,
        [FIELD_NAMES.videoDuration]: 30,
        status: 'active'
      }

      // Create document
      const userDocRef = doc(db, COLLECTIONS.users, user.uid)
      await setDoc(userDocRef, userData)
      addToResult(`✅ Document created successfully!`)

      addToResult('\n🎉 FORM registration test completed successfully!')

    } catch (error: any) {
      addToResult(`\n❌ FORM registration test failed!`)
      addToResult(`Error: ${error.message}`)
      addToResult(`Code: ${error.code}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Automated Test */}
          <div className="glass-card p-8">
            <h1 className="text-3xl font-bold text-white mb-6">Automated Test</h1>

            <button
              onClick={testRegistration}
              disabled={isLoading}
              className="btn-primary mb-6 w-full"
            >
              {isLoading ? 'Testing Registration...' : 'Test Registration Process'}
            </button>

            <div className="bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto">
              {result || 'Click the button to test registration process...'}
            </div>
          </div>

          {/* Form Test */}
          <div className="glass-card p-8">
            <h1 className="text-3xl font-bold text-white mb-6">Form Test</h1>

            <form onSubmit={testFormRegistration} className="space-y-4 mb-6">
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Full Name"
                className="form-input"
                required
              />
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Email (leave empty for auto-generated)"
                className="form-input"
              />
              <input
                type="tel"
                name="mobile"
                value={formData.mobile}
                onChange={handleInputChange}
                placeholder="Mobile Number"
                className="form-input"
                required
              />
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Password"
                className="form-input"
                required
              />
              <input
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                placeholder="Confirm Password"
                className="form-input"
                required
              />
              <input
                type="text"
                name="referralCode"
                value={formData.referralCode}
                onChange={handleInputChange}
                placeholder="Referral Code (Optional)"
                className="form-input"
              />
              <button
                type="submit"
                disabled={isLoading}
                className="btn-primary w-full"
              >
                {isLoading ? 'Testing...' : 'Test Form Registration'}
              </button>
            </form>

            <div className="bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto">
              {result || 'Fill the form and submit to test...'}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
