(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779,8733],{2899:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(5155),i=a(2115),s=a(6874),n=a.n(s),o=a(6681),l=a(6779),d=a(3592),c=a(3737),u=a(4752),g=a.n(u);function x(){let{user:e,loading:t,isAdmin:a}=(0,o.wC)(),[s,u]=(0,i.useState)([]),[x,m]=(0,i.useState)(!0),[p,h]=(0,i.useState)(""),[y,v]=(0,i.useState)(!1),[f,b]=(0,i.useState)(0),[D,w]=(0,i.useState)(null),[j,N]=(0,i.useState)(!1),[A,k]=(0,i.useState)({name:"",email:"",mobile:"",referralCode:"",referredBy:"",plan:"",activeDays:0,totalVideos:0,todayVideos:0,wallet:0,status:"active",videoDuration:300,quickVideoAdvantage:!1,quickVideoAdvantageDays:7,quickVideoAdvantageSeconds:30}),[E,S]=(0,i.useState)(!1),[C,V]=(0,i.useState)(1),[L,q]=(0,i.useState)(!0),[I,M]=(0,i.useState)(null);(0,i.useEffect)(()=>{a&&T()},[a]);let T=async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];try{m(!0);let t=await (0,l.lo)(50,e?null:I);if(e){u(t.users),V(1);try{let e=await (0,l.nQ)();b(e)}catch(e){console.error("Error getting total user count:",e)}}else u(e=>[...e,...t.users]);M(t.lastDoc),q(t.hasMore)}catch(e){console.error("Error loading users:",e),g().fire({icon:"error",title:"Error",text:"Failed to load users. Please try again."})}finally{m(!1)}},B=async()=>{if(!p.trim())return void T();try{v(!0);let e=await (0,l.x5)(p.trim());u(e),q(!1)}catch(e){console.error("Error searching users:",e),g().fire({icon:"error",title:"Search Failed",text:"Failed to search users. Please try again."})}finally{v(!1)}},F=e=>{w(e),k({name:e.name,email:e.email,mobile:e.mobile,referralCode:e.referralCode,referredBy:e.referredBy,plan:e.plan,activeDays:e.activeDays,totalVideos:e.totalVideos,todayVideos:e.todayVideos,wallet:e.wallet||0,status:e.status,videoDuration:e.videoDuration||300,quickVideoAdvantage:e.quickVideoAdvantage||!1,quickVideoAdvantageDays:e.quickVideoAdvantageDays||7,quickVideoAdvantageSeconds:e.quickVideoAdvantageSeconds||30}),N(!0)},O=async()=>{if(D)try{S(!0);let t=D.plan,a=A.plan,r=t!==a,i=A.activeDays!==D.activeDays,s={name:A.name,email:A.email,mobile:A.mobile,referralCode:A.referralCode,referredBy:A.referredBy,plan:A.plan,activeDays:A.activeDays,totalVideos:A.totalVideos,todayVideos:A.todayVideos,wallet:A.wallet,status:A.status};i&&(s.manuallySetActiveDays=!0),await (0,l.TK)(D.id,s),A.videoDuration!==(D.videoDuration||300)&&await (0,d.Gl)(D.id,A.videoDuration);let n=!!D.quickVideoAdvantage;if(A.quickVideoAdvantage&&!n?await (0,d.w1)(D.id,A.quickVideoAdvantageDays,(null==e?void 0:e.email)||"admin",A.quickVideoAdvantageSeconds):!A.quickVideoAdvantage&&n?await (0,d.wT)(D.id,(null==e?void 0:e.email)||"admin"):A.quickVideoAdvantage&&n&&(await (0,d.wT)(D.id,(null==e?void 0:e.email)||"admin"),await (0,d.w1)(D.id,A.quickVideoAdvantageDays,(null==e?void 0:e.email)||"admin",A.quickVideoAdvantageSeconds)),r)try{await (0,d.II)(D.id,a),i?console.log("Plan changed but active days manually set to ".concat(A.activeDays," for user ").concat(D.id)):(s.activeDays=1,s.manuallySetActiveDays=!1,console.log("Reset active days to 1 for user ".concat(D.id," due to plan change: ").concat(t," -> ").concat(a))),console.log("Updated plan expiry for user ".concat(D.id,": ").concat(t," -> ").concat(a))}catch(e){console.error("Error updating plan expiry:",e)}if(r&&"Trial"===t&&"Trial"!==a)try{console.log("Processing referral bonus for user ".concat(D.id,": ").concat(t," -> ").concat(a)),await (0,d.IK)(D.id,t,a),g().fire({icon:"success",title:"User Updated & Referral Bonus Processed",html:'\n              <div class="text-left">\n                <p><strong>User plan updated:</strong> '.concat(t," → ").concat(a,"</p>\n                <p><strong>Referral bonus:</strong> Processed for referrer (if applicable)</p>\n              </div>\n            "),timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error processing referral bonus:",e),g().fire({icon:"warning",title:"User Updated (Referral Bonus Issue)",html:'\n              <div class="text-left">\n                <p><strong>User plan updated successfully:</strong> '.concat(t," → ").concat(a,'</p>\n                <p><strong>Referral bonus:</strong> Could not be processed automatically</p>\n                <p class="text-sm text-gray-600 mt-2">Please check referral bonus manually if needed.</p>\n              </div>\n            '),timer:5e3,showConfirmButton:!1})}else{let e="User information has been updated successfully";A.quickVideoAdvantage&&!n?e+=". Quick video advantage granted for ".concat(A.quickVideoAdvantageDays," days."):!A.quickVideoAdvantage&&n?e+=". Quick video advantage removed.":A.quickVideoAdvantage&&n&&(e+=". Quick video advantage updated for ".concat(A.quickVideoAdvantageDays," days.")),g().fire({icon:"success",title:"User Updated",text:e,timer:3e3,showConfirmButton:!1})}u(e=>e.map(e=>e.id===D.id?{...e,...s,videoDuration:A.videoDuration,quickVideoAdvantage:A.quickVideoAdvantage,quickVideoAdvantageDays:A.quickVideoAdvantage?A.quickVideoAdvantageDays:0,quickVideoAdvantageSeconds:A.quickVideoAdvantage?A.quickVideoAdvantageSeconds:30,quickVideoAdvantageExpiry:A.quickVideoAdvantage?new Date(Date.now()+24*A.quickVideoAdvantageDays*36e5):null}:e)),N(!1),w(null),await T()}catch(e){console.error("Error updating user:",e),g().fire({icon:"error",title:"Update Failed",text:"Failed to update user. Please try again."})}finally{S(!1)}},P=async e=>{if((await g().fire({icon:"warning",title:"Delete User",text:"Are you sure you want to delete ".concat(e.name,"? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,l.hG)(e.id),u(t=>t.filter(t=>t.id!==e.id)),g().fire({icon:"success",title:"User Deleted",text:"User has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting user:",e),g().fire({icon:"error",title:"Delete Failed",text:"Failed to delete user. Please try again."})}},_=e=>null==e||isNaN(e)?"₹0.00":"₹".concat(e.toFixed(2)),U=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},R=e=>{switch(e){case"active":return"bg-green-500";case"inactive":return"bg-red-500";case"suspended":return"bg-yellow-500";default:return"bg-gray-500"}},Q=async()=>{try{g().fire({title:"Exporting Users...",text:"Please wait while we prepare your export file.",allowOutsideClick:!1,didOpen:()=>{g().showLoading()}});let e=await (0,l.CF)();if(0===e.length)return void g().fire({icon:"warning",title:"No Data",text:"No users to export."});let t=(0,c.Fz)(e);(0,c.Bf)(t,"users"),g().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(e.length," users to CSV file."),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error exporting users:",e),g().fire({icon:"error",title:"Export Failed",text:"Failed to export users. Please try again."})}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)(n(),{href:"/admin",className:"text-gray-600 hover:text-gray-800",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),f>0&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:p?"Showing ".concat(s.length," of ").concat(f," users"):"Total: ".concat(f," users")})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(n(),{href:"/admin/upload-users",className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]}),(0,r.jsxs)("button",{onClick:Q,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,r.jsxs)("button",{onClick:()=>T(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("input",{type:"text",value:p,onChange:e=>h(e.target.value),placeholder:"Search by name, email, mobile, or referral code...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onKeyDown:e=>"Enter"===e.key&&B()}),(0,r.jsx)("button",{onClick:B,disabled:y,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Searching..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-search mr-2"}),"Search"]})}),p&&(0,r.jsx)("button",{onClick:()=>{h(""),T()},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:(0,r.jsx)("i",{className:"fas fa-times"})})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Videos"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quick Advantage"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:x&&0===s.length?(0,r.jsx)("tr",{children:(0,r.jsxs)("td",{colSpan:9,className:"px-6 py-4 text-center",children:[(0,r.jsx)("div",{className:"spinner mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading users..."})]})}):0===s.length?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:9,className:"px-6 py-4 text-center text-gray-500",children:"No users found"})}):s.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined: ",e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():new Date(e.joinedDate).toLocaleDateString()]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.mobile}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Code: ",e.referralCode]}),e.referredBy&&(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Ref: ",e.referredBy]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(U(e.plan)),children:e.plan}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Days: ",e.activeDays]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.totalVideos]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Today: ",e.todayVideos]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300,"s"):"".concat(Math.round((e.videoDuration||300)/60),"m")}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300," second").concat((e.videoDuration||300)>1?"s":""):"".concat(Math.round((e.videoDuration||300)/60)," minute").concat(Math.round((e.videoDuration||300)/60)>1?"s":"")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.quickVideoAdvantage&&(e.quickVideoAdvantageRemainingDays&&e.quickVideoAdvantageRemainingDays>0||e.quickVideoAdvantageExpiry&&new Date<e.quickVideoAdvantageExpiry)?(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-green-500",children:"Active"}),(0,r.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:void 0!==e.quickVideoAdvantageRemainingDays?"".concat(e.quickVideoAdvantageRemainingDays," days left"):e.quickVideoAdvantageExpiry?"Until: ".concat(e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():new Date(e.quickVideoAdvantageExpiry).toLocaleDateString()):"Active"})]}):(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-gray-500",children:"None"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsx)("i",{className:"fas fa-wallet mr-1 text-green-500"}),_(e.wallet||0)]}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Total Balance"})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(R(e.status)),children:e.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>F(e),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:(0,r.jsx)("i",{className:"fas fa-edit"})}),(0,r.jsx)("button",{onClick:()=>P(e),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,r.jsx)("i",{className:"fas fa-trash"})})]})})]},e.id))})]})}),L&&!x&&s.length>0&&(0,r.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 text-center",children:(0,r.jsxs)("button",{onClick:()=>{L&&!x&&T(!1)},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:[(0,r.jsx)("i",{className:"fas fa-chevron-down mr-2"}),"Load More Users"]})})]})}),j&&D&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto modal-scrollable",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-md my-8 max-h-[calc(100vh-2rem)] flex flex-col shadow-xl",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 flex-shrink-0",children:(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"Edit User"})}),(0,r.jsxs)("div",{className:"p-6 space-y-4 flex-1 overflow-y-auto modal-scrollable",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,r.jsx)("input",{type:"text",value:A.name,onChange:e=>k(t=>({...t,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,r.jsx)("input",{type:"email",value:A.email,onChange:e=>k(t=>({...t,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mobile"}),(0,r.jsx)("input",{type:"text",value:A.mobile,onChange:e=>k(t=>({...t,mobile:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Code"}),(0,r.jsx)("input",{type:"text",value:A.referralCode,onChange:e=>k(t=>({...t,referralCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referred By"}),(0,r.jsx)("input",{type:"text",value:A.referredBy,onChange:e=>k(t=>({...t,referredBy:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan"}),(0,r.jsxs)("select",{value:A.plan,onChange:e=>k(t=>({...t,plan:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"Trial",children:"Trial"}),(0,r.jsx)("option",{value:"Starter",children:"Starter"}),(0,r.jsx)("option",{value:"Basic",children:"Basic"}),(0,r.jsx)("option",{value:"Premium",children:"Premium"}),(0,r.jsx)("option",{value:"Gold",children:"Gold"}),(0,r.jsx)("option",{value:"Platinum",children:"Platinum"}),(0,r.jsx)("option",{value:"Diamond",children:"Diamond"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Active Days"}),(0,r.jsx)("input",{type:"number",value:A.activeDays,onChange:e=>k(t=>({...t,activeDays:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Videos"}),(0,r.jsx)("input",{type:"number",value:A.totalVideos,onChange:e=>k(t=>({...t,totalVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Today Videos"}),(0,r.jsx)("input",{type:"number",value:A.todayVideos,onChange:e=>k(t=>({...t,todayVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wallet Balance (₹)"}),(0,r.jsx)("input",{type:"number",step:"0.01",value:A.wallet,onChange:e=>k(t=>({...t,wallet:parseFloat(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Video Duration"}),(0,r.jsxs)("select",{value:A.videoDuration,onChange:e=>k(t=>({...t,videoDuration:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsxs)("optgroup",{label:"\uD83D\uDE80 Quick Duration",children:[(0,r.jsx)("option",{value:1,children:"1 second"}),(0,r.jsx)("option",{value:10,children:"10 seconds"}),(0,r.jsx)("option",{value:30,children:"30 seconds"})]}),(0,r.jsxs)("optgroup",{label:"⏱️ Standard Duration",children:[(0,r.jsx)("option",{value:60,children:"1 minute"}),(0,r.jsx)("option",{value:120,children:"2 minutes"}),(0,r.jsx)("option",{value:180,children:"3 minutes"}),(0,r.jsx)("option",{value:240,children:"4 minutes"}),(0,r.jsx)("option",{value:300,children:"5 minutes"}),(0,r.jsx)("option",{value:360,children:"6 minutes"}),(0,r.jsx)("option",{value:420,children:"7 minutes"})]})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:A.videoDuration<60?"".concat(A.videoDuration," second").concat(A.videoDuration>1?"s":""):"".concat(Math.round(A.videoDuration/60)," minute").concat(Math.round(A.videoDuration/60)>1?"s":"")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("select",{value:A.status,onChange:e=>k(t=>({...t,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"active",children:"Active"}),(0,r.jsx)("option",{value:"inactive",children:"Inactive"}),(0,r.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,r.jsxs)("h4",{className:"text-md font-semibold text-gray-900 mb-3",children:[(0,r.jsx)("i",{className:"fas fa-bolt mr-2 text-yellow-500"}),"Quick Video Advantage"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"quickVideoAdvantage",checked:A.quickVideoAdvantage,onChange:e=>k(t=>({...t,quickVideoAdvantage:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"quickVideoAdvantage",className:"ml-2 block text-sm text-gray-700",children:"Grant Quick Video Advantage"})]}),A.quickVideoAdvantage&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 ml-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Days"}),(0,r.jsx)("input",{type:"number",min:"1",max:"365",value:A.quickVideoAdvantageDays,onChange:e=>k(t=>({...t,quickVideoAdvantageDays:parseInt(e.target.value)||7})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Video Duration"}),(0,r.jsxs)("select",{value:A.quickVideoAdvantageSeconds,onChange:e=>k(t=>({...t,quickVideoAdvantageSeconds:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:1,children:"1 second"}),(0,r.jsx)("option",{value:10,children:"10 seconds"}),(0,r.jsx)("option",{value:30,children:"30 seconds"})]})]})]}),D&&(0,r.jsx)("div",{className:"ml-6 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Current Status:"})," ",D.quickVideoAdvantage&&(D.quickVideoAdvantageRemainingDays&&D.quickVideoAdvantageRemainingDays>0||D.quickVideoAdvantageExpiry&&new Date<D.quickVideoAdvantageExpiry)?(0,r.jsx)("span",{className:"text-green-600",children:void 0!==D.quickVideoAdvantageRemainingDays?"Active - ".concat(D.quickVideoAdvantageRemainingDays," days remaining"):D.quickVideoAdvantageExpiry?"Active until ".concat(D.quickVideoAdvantageExpiry instanceof Date?D.quickVideoAdvantageExpiry.toLocaleDateString():new Date(D.quickVideoAdvantageExpiry).toLocaleDateString()):"Active"}):(0,r.jsx)("span",{className:"text-gray-500",children:"Not active"})]})})]})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 flex-shrink-0",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{onClick:O,disabled:E,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:E?"Saving...":"Save Changes"}),(0,r.jsx)("button",{onClick:()=>N(!1),className:"flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700",children:"Cancel"})]})})]})})]})}},3737:(e,t,a)=>{"use strict";function r(e,t,a){if(!e||0===e.length)return void alert("No data to export");let r=a||Object.keys(e[0]),i=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],s=new Blob(["\uFEFF"+[r.join(","),...e.map(e=>r.map(t=>{let a=e[t];if(null==a)return"";let r=i.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):r&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let e=URL.createObjectURL(s);n.setAttribute("href",e),n.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)}}function i(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function s(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function n(e){return e.map(e=>{var t,a,r,i;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(r=e.bankDetails)?void 0:r.accountNumber)||""),"IFSC Code":(null==(i=e.bankDetails)?void 0:i.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>r,Fz:()=>i,Pe:()=>o,dB:()=>n,sL:()=>s})},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>c,I0:()=>g,Pn:()=>o,TK:()=>m,getWithdrawals:()=>x,hG:()=>p,lo:()=>l,nQ:()=>u,updateWithdrawalStatus:()=>h,x5:()=>d});var r=a(5317),i=a(6104),s=a(3592);let n=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=n.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=r.Dc.fromDate(t),o=await (0,r.getDocs)((0,r.collection)(i.db,s.COLLECTIONS.users)),l=o.size,d=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.users),(0,r._M)(s.FIELD_NAMES.joinedDate,">=",a)),c=(await (0,r.getDocs)(d)).size,u=0,g=0,x=0,m=0;o.forEach(e=>{var a;let r=e.data();u+=r[s.FIELD_NAMES.totalVideos]||0,g+=r[s.FIELD_NAMES.wallet]||0;let i=null==(a=r[s.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();i&&i.toDateString()===t.toDateString()&&(x+=r[s.FIELD_NAMES.todayVideos]||0)});try{let e=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.transactions),(0,r._M)(s.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(e)).forEach(e=>{var a;let r=e.data(),i=null==(a=r[s.FIELD_NAMES.date])?void 0:a.toDate();i&&i>=t&&(m+=r[s.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let p=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),h=(await (0,r.getDocs)(p)).size,y=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.withdrawals),(0,r._M)("date",">=",a)),v=(await (0,r.getDocs)(y)).size,f={totalUsers:l,totalVideos:u,totalEarnings:g,pendingWithdrawals:h,todayUsers:c,todayVideos:x,todayEarnings:m,todayWithdrawals:v};return n.set(e,{data:f,timestamp:Date.now()}),f}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let n=await (0,r.getDocs)(a);return{users:n.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[s.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[s.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[s.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[s.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[s.FIELD_NAMES.name]||"").toLowerCase(),r=String(e[s.FIELD_NAMES.email]||"").toLowerCase(),i=String(e[s.FIELD_NAMES.mobile]||"").toLowerCase(),n=String(e[s.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||r.includes(t)||i.includes(t)||n.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.users),(0,r.My)(s.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[s.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[s.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.users));return(await (0,r.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.transactions),(0,r.My)(s.FIELD_NAMES.date,"desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.transactions),(0,r.My)(s.FIELD_NAMES.date,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let n=await (0,r.getDocs)(a);return{transactions:n.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[s.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(i.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(t),(0,r.AB)(e)));let n=await (0,r.getDocs)(a);return{withdrawals:n.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function m(e,t){try{await (0,r.mZ)((0,r.H9)(i.db,s.COLLECTIONS.users,e),t),n.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function p(e){try{await (0,r.kd)((0,r.H9)(i.db,s.COLLECTIONS.users,e)),n.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function h(e,t,o){try{let l=await (0,r.x7)((0,r.H9)(i.db,s.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=l.data(),g={status:t,updatedAt:r.Dc.now()};if(o&&(g.adminNotes=o),await (0,r.mZ)((0,r.H9)(i.db,s.COLLECTIONS.withdrawals,e),g),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(d,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}n.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},7428:(e,t,a)=>{Promise.resolve().then(a.bind(a,2899))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,6681,8441,1684,7358],()=>t(7428)),_N_E=e.O()}]);