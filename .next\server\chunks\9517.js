"use strict";exports.id=9517,exports.ids=[1898,9517],exports.modules={1898:(e,a,t)=>{t.d(a,{checkVersionAndClearCache:()=>l});var o=t(75535),r=t(33784);let c="1.1.0",n="mytube_app_version";async function l(){try{console.log("\uD83D\uDD0D Silently checking app version for cache management...");let e=localStorage.getItem(n),a=c;try{let e=await (0,o.x7)((0,o.H9)(r.db,"system/version"));e.exists()&&(a=e.data()?.version||c)}catch(e){console.warn("Could not fetch server version, using current version:",e)}if(!e||e!==a){console.log(`🔄 Version changed: ${e||"none"} → ${a} - Clearing cache silently...`);try{return await s(),localStorage.setItem(n,a),localStorage.setItem("mytube_last_cache_clear",new Date().toISOString()),console.log("✅ Cache cleared silently due to version update"),setTimeout(()=>{window.location.reload()},1e3),!0}catch(e){return console.error("Silent cache clear failed, continuing normally:",e),localStorage.setItem(n,a),!1}}return console.log("✅ Version unchanged, no cache clearing needed"),!1}catch(e){return console.error("Error checking version:",e),!1}}async function s(){try{console.log("\uD83D\uDD07 Silently clearing application cache...");let e={};if(["firebase:authUser","firebase:host"].forEach(a=>{Object.keys(localStorage).filter(e=>e.includes(a)).forEach(a=>{e[a]=localStorage.getItem(a)})}),localStorage.clear(),Object.entries(e).forEach(([e,a])=>{a&&localStorage.setItem(e,a)}),sessionStorage.clear(),"caches"in window)try{let e=await Promise.race([caches.keys(),new Promise((e,a)=>setTimeout(()=>a(Error("Cache keys timeout")),2e3))]);await Promise.race([Promise.all(e.map(e=>caches.delete(e))),new Promise((e,a)=>setTimeout(()=>a(Error("Cache deletion timeout")),2e3))])}catch(e){console.warn("Silent cache clear skipped browser caches:",e)}console.log("✅ Silent cache clear completed")}catch(e){console.warn("Silent cache clear encountered error:",e)}}},33784:(e,a,t)=>{t.d(a,{Cn:()=>h,db:()=>d,j2:()=>i});var o=t(67989),r=t(63385),c=t(75535),n=t(70146),l=t(24791);let s=(0,o.Dk)().length?(0,o.Sx)():(0,o.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),i=(0,r.xI)(s),d=(0,c.aU)(s);(0,n.c7)(s);let h=(0,l.Uz)(s)}};