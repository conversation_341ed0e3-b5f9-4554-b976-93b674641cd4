(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{1469:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return r},getImageProps:function(){return c}});let a=t(8229),l=t(8883),i=t(3063),n=a._(t(1193));function c(e){let{props:s}=(0,l.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(s))void 0===t&&delete s[e];return{props:s}}let r=i.Image},3792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var a=t(5155),l=t(2115),i=t(6874),n=t.n(i),c=t(8926),r=t(6766),o=t(4752),m=t.n(o);function d(){let[e,s]=(0,l.useState)(!1);(0,l.useEffect)(()=>{(async()=>{try{let{checkVersionAndClearCache:e}=await Promise.all([t.e(2992),t.e(7416),t.e(5181),t.e(8278)]).then(t.bind(t,8278));await e()}catch(e){console.error("Silent version check failed:",e)}})()},[]);let i="2.1.0",o=async()=>{try{if(s(!0),!(await m().fire({title:"Clear Cache & Data?",html:'\n          <div class="text-left">\n            <p class="mb-3"><strong>This will clear:</strong></p>\n            <ul class="text-sm space-y-1 mb-4">\n              <li>• Browser cache & stored data</li>\n              <li>• Local storage & session data</li>\n              <li>• Cookies & preferences</li>\n              <li>• Cached videos & images</li>\n              <li>• All temporary files</li>\n            </ul>\n            <p class="text-sm text-gray-600">\n              <strong>Why clear cache?</strong> Get the latest version of MyTube with all new features and bug fixes.\n            </p>\n          </div>\n        ',icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Clear Everything!",cancelButtonText:"Cancel",allowOutsideClick:!1})).isConfirmed)return void s(!1);m().fire({title:"Clearing Cache & Data...",html:'\n          <div class="text-center">\n            <div class="spinner mx-auto mb-4"></div>\n            <p>Please wait while we clear all cached data...</p>\n            <p class="text-sm text-gray-600 mt-2">This may take a few seconds</p>\n          </div>\n        ',allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1});try{localStorage.clear(),console.log("✅ localStorage cleared")}catch(e){console.warn("⚠️ Could not clear localStorage:",e)}try{sessionStorage.clear(),console.log("✅ sessionStorage cleared")}catch(e){console.warn("⚠️ Could not clear sessionStorage:",e)}try{if("indexedDB"in window){for(let e of["firebaseLocalStorageDb","firebase-heartbeat-database","firebase-installations-database"])try{await Promise.race([new Promise((s,t)=>{let a=indexedDB.deleteDatabase(e);a.onsuccess=()=>s(!0),a.onerror=()=>s(!0),a.onblocked=()=>s(!0)}),new Promise((e,s)=>setTimeout(()=>s(Error("Timeout")),3e3))])}catch(s){console.warn("⚠️ Could not clear IndexedDB ".concat(e,":"),s)}console.log("✅ IndexedDB cleared")}}catch(e){console.warn("⚠️ Could not clear IndexedDB:",e)}try{if("serviceWorker"in navigator&&"caches"in window){let e=await Promise.race([caches.keys(),new Promise((e,s)=>setTimeout(()=>s(Error("Cache keys timeout")),5e3))]);await Promise.race([Promise.all(e.map(e=>caches.delete(e))),new Promise((e,s)=>setTimeout(()=>s(Error("Cache deletion timeout")),5e3))]),console.log("✅ Service Worker cache cleared")}}catch(e){console.warn("⚠️ Could not clear Service Worker cache:",e)}try{document.cookie.split(";").forEach(e=>{let s=e.indexOf("="),t=s>-1?e.substring(0,s):e;document.cookie="".concat(t,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"),document.cookie="".concat(t,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=").concat(window.location.hostname),document.cookie="".concat(t,"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.").concat(window.location.hostname)}),console.log("✅ Cookies cleared")}catch(e){console.warn("⚠️ Could not clear cookies:",e)}await new Promise(e=>setTimeout(e,1e3)),m().fire({icon:"success",title:"Cache & Data Cleared!",html:'\n          <div class="text-center">\n            <p class="mb-3">✅ All cached data has been cleared successfully!</p>\n            <p class="text-sm text-gray-600 mb-3">\n              The page will now reload to load the latest version of MyTube.\n            </p>\n            <p class="text-sm text-green-600 font-semibold">\n              \uD83C\uDF89 You now have the freshest version with all updates!\n            </p>\n          </div>\n        ',timer:3e3,showConfirmButton:!0,confirmButtonText:"Reload Now",allowOutsideClick:!1}).then(()=>{window.location.reload()})}catch(e){console.error("Error clearing cache and data:",e),m().fire({icon:"error",title:"Clear Failed",html:'\n          <div class="text-left">\n            <p class="mb-2">Some data could not be cleared:</p>\n            <p class="text-sm text-gray-600">'.concat(e instanceof Error?e.message:"Unknown error",'</p>\n            <p class="text-sm text-blue-600 mt-3">\n              Try refreshing the page manually (Ctrl+F5 or Cmd+Shift+R)\n            </p>\n          </div>\n        '),confirmButtonText:"OK"})}finally{s(!1)}};return(0,a.jsxs)("main",{className:"min-h-screen",children:[(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 z-50 p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(r.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:32,height:32,className:"mr-2"}),(0,a.jsx)("span",{className:"text-white text-xl font-bold",children:"MyTube"})]}),(0,a.jsxs)("div",{className:"flex space-x-2 sm:space-x-4",children:[(0,a.jsx)("button",{onClick:o,disabled:e,className:"nav-link text-sm sm:text-base",title:"Clear cache & data to get latest version",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-3 h-3 mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Clearing..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Clear Cache"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Cache"})]})}),(0,a.jsxs)(n(),{href:"#pricing",className:"nav-link text-sm sm:text-base",children:[(0,a.jsx)("i",{className:"fas fa-crown mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Plans"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Plans"})]}),(0,a.jsxs)(n(),{href:"/login",className:"nav-link text-sm sm:text-base",children:[(0,a.jsx)("i",{className:"fas fa-sign-in-alt mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Login"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Login"})]})]})]})}),(0,a.jsx)("section",{className:"min-h-screen flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[(0,a.jsx)(r.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:60,height:60,className:"mr-4"}),(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"MyTube"})]}),(0,a.jsx)("h1",{className:"text-5xl md:text-7xl font-bold mb-6 gradient-text",children:"Watch Videos & Earn Money"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto",children:"Watch videos and earn up to ₹30,000 per month. Start your journey to financial freedom today by completing simple video watching tasks!"})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mb-12",children:[(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsx)("i",{className:"fas fa-play-circle text-4xl text-youtube-red mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Trending Videos"}),(0,a.jsx)("p",{className:"text-white/80",children:"Watch popular content daily"})]}),(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave text-4xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Instant Earnings"}),(0,a.jsx)("p",{className:"text-white/80",children:"Get paid for every video watched"})]}),(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsx)("i",{className:"fas fa-bolt text-4xl text-yellow-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Fast & Simple"}),(0,a.jsx)("p",{className:"text-white/80",children:"Easy video watching process"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n(),{href:"/login",className:"btn-primary inline-flex items-center text-lg px-8 py-4",children:[(0,a.jsx)("i",{className:"fas fa-rocket mr-3"}),"Start Earning Now"]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mt-6",children:[(0,a.jsxs)(n(),{href:"/how-it-works",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How It Works"]}),(0,a.jsxs)(n(),{href:"/faq",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-question-circle mr-2"}),"FAQ"]})]})]})]})}),(0,a.jsx)("section",{id:"pricing",className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Choose Your Earning Plan"}),(0,a.jsx)("p",{className:"text-xl text-white/80 max-w-3xl mx-auto",children:"Start with our free trial or upgrade to premium plans for higher earnings. Watch videos and earn money with flexible pricing options."})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"glass-card p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Trial"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"Free"}),(0,a.jsx)("span",{className:"text-white/60 ml-2",children:"/ 2 days"})]}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"Earn ₹10 per 50 videos"})]}),(0,a.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"2 days access"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"₹10 per 50 videos"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Basic support"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Video duration: 30 seconds"]})]}),(0,a.jsx)(n(),{href:"/register",className:"w-full btn-secondary block text-center",children:"Start Free Trial"})]}),(0,a.jsxs)("div",{className:"glass-card p-8 relative ring-2 ring-yellow-400",children:[(0,a.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,a.jsx)("span",{className:"bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold",children:"Most Popular"})}),(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Gold"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"₹3,999"}),(0,a.jsx)("span",{className:"text-white/60 ml-2",children:"/ 30 days"})]}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"Earn ₹200 per 50 videos"})]}),(0,a.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"30 days access"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"₹200 per 50 videos"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Video duration: 3 minutes"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Referral bonus: ₹400"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Priority support"]})]}),(0,a.jsx)(n(),{href:"/plans",className:"w-full bg-yellow-400 text-black py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-all duration-300 block text-center",children:"Choose Gold"})]}),(0,a.jsxs)("div",{className:"glass-card p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Diamond"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"₹9,999"}),(0,a.jsx)("span",{className:"text-white/60 ml-2",children:"/ 30 days"})]}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"Earn ₹400 per 50 videos"})]}),(0,a.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"30 days access"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"₹400 per 50 videos"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Video duration: 1 minute"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Referral bonus: ₹1200"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"VIP support"]})]}),(0,a.jsx)(n(),{href:"/plans",className:"w-full btn-primary block text-center",children:"Choose Diamond"})]})]}),(0,a.jsx)("div",{className:"text-center mt-12",children:(0,a.jsxs)(n(),{href:"/plans",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-crown mr-2"}),"View All Plans"]})})]})}),(0,a.jsx)("section",{className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Need Help?"}),(0,a.jsx)("p",{className:"text-xl text-white/80 mb-12 max-w-2xl mx-auto",children:"Our support team is here to help you get started and answer any questions about earning with MyTube."}),(0,a.jsx)("div",{className:"glass-card p-8 mb-12 max-w-2xl mx-auto clear-cache-section",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt text-5xl text-blue-400 mb-4 clear-cache-icon"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Get Latest Version"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Having issues or want the latest features? Clear your cache and data to get the freshest version of MyTube with all updates and bug fixes."}),(0,a.jsx)("button",{onClick:o,disabled:e,className:"clear-cache-btn inline-flex items-center text-lg px-8 py-4 text-white font-semibold rounded-lg ".concat(e?"btn-processing":""),children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-3 w-5 h-5"}),"Clearing Cache..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-3"}),"Clear Cache & Data"]})}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-4",children:"✨ Recommended if you're experiencing issues or want the latest features"}),(0,a.jsxs)("div",{className:"mt-4 inline-flex items-center bg-white/10 rounded-full px-4 py-2 text-xs text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-code-branch mr-2"}),"Version ",i," • Updated ","2025-01-19"]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-4 text-xs text-white/60",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-database mr-2"}),"Clears Storage"]}),(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-cookie-bite mr-2"}),"Removes Cookies"]}),(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-memory mr-2"}),"Clears Cache"]}),(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Fresh Download"]})]})]})}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[(0,a.jsxs)("a",{href:"https://wa.me/917676636990",target:"_blank",rel:"noopener noreferrer",className:"glass-card p-8 hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fab fa-whatsapp text-5xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"WhatsApp Support"}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Get instant help via WhatsApp"}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"+91 7676636990"}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"9 AM - 6 PM (Working days)"})]}),(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"glass-card p-8 hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-envelope text-5xl text-blue-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Email Support"}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Send us detailed queries"}),(0,a.jsx)("p",{className:"text-blue-400 font-semibold",children:"<EMAIL>"}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"9 AM - 6 PM (Working days)"})]}),(0,a.jsx)(c.A,{variant:"homepage"})]})]})}),(0,a.jsx)("footer",{className:"py-12 px-4 border-t border-white/20",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto text-center",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,a.jsxs)("div",{className:"mb-4 md:mb-0",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"MyTube"}),(0,a.jsx)("p",{className:"text-white/60",children:"Earn money by watching videos"})]}),(0,a.jsxs)("div",{className:"text-white/60 text-sm",children:[(0,a.jsx)("p",{children:"\xa9 2024 MyTube. All rights reserved."}),(0,a.jsxs)("div",{className:"mt-2 flex items-center justify-center md:justify-end",children:[(0,a.jsx)("i",{className:"fas fa-code-branch mr-2 text-xs"}),(0,a.jsxs)("span",{className:"text-xs",children:["v",i]}),(0,a.jsx)("span",{className:"mx-2",children:"•"}),(0,a.jsx)("button",{onClick:o,disabled:e,className:"text-xs text-blue-400 hover:text-blue-300 transition-colors duration-200 underline",children:e?"Clearing...":"Clear Cache"})]})]})]})})})]})}},6766:(e,s,t)=>{"use strict";t.d(s,{default:()=>l.a});var a=t(1469),l=t.n(a)},8926:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var a=t(5155),l=t(2115),i=t(4752),n=t.n(i);function c(e){let{variant:s="homepage",className:t=""}=e,{isInstallable:i,isInstalled:c,installApp:r,getInstallInstructions:o}=function(){let[e,s]=(0,l.useState)(null),[t,a]=(0,l.useState)(!1),[i,n]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e=e=>{e.preventDefault(),s(e),a(!0)},t=()=>{n(!0),a(!1),s(null)};return window.matchMedia("(display-mode: standalone)").matches&&n(!0),window.addEventListener("beforeinstallprompt",e),window.addEventListener("appinstalled",t),()=>{window.removeEventListener("beforeinstallprompt",e),window.removeEventListener("appinstalled",t)}},[]),{isInstallable:t,isInstalled:i,installApp:async()=>{if(!e)return!1;try{await e.prompt();let{outcome:t}=await e.userChoice;if("accepted"===t)return n(!0),a(!1),s(null),!0;return!1}catch(e){return console.error("Error installing app:",e),!1}},getInstallInstructions:()=>{let e=navigator.userAgent.toLowerCase();return e.includes("chrome")&&!e.includes("edg")?{browser:"Chrome",steps:["Click the install button above","Or click the install icon in the address bar",'Click "Install" in the popup']}:e.includes("firefox")?{browser:"Firefox",steps:["Click the menu button (☰)",'Select "Install this site as an app"','Click "Install" in the dialog']}:e.includes("safari")?{browser:"Safari",steps:["Tap the Share button",'Scroll down and tap "Add to Home Screen"','Tap "Add" to install']}:e.includes("edg")?{browser:"Edge",steps:["Click the install button above","Or click the app icon in the address bar",'Click "Install" in the popup']}:{browser:"Your Browser",steps:["Look for an install option in your browser menu","Or check the address bar for an install icon","Follow your browser's installation prompts"]}}}}(),[m,d]=(0,l.useState)(!1),x=async()=>{await r()?n().fire({icon:"success",title:"App Installed!",text:"MyTube has been installed on your device. You can now access it from your home screen.",timer:3e3,showConfirmButton:!1}):i||d(!0)},h=()=>{let e=o();n().fire({title:"Install MyTube on ".concat(e.browser),html:'\n        <div class="text-left">\n          <p class="mb-4 text-gray-600">Follow these steps to install MyTube as an app:</p>\n          <ol class="list-decimal list-inside space-y-2">\n            '.concat(e.steps.map(e=>'<li class="text-gray-700">'.concat(e,"</li>")).join(""),'\n          </ol>\n          <div class="mt-6 p-4 bg-blue-50 rounded-lg">\n            <p class="text-sm text-blue-800">\n              <i class="fas fa-info-circle mr-2"></i>\n              Installing the app gives you faster access, offline capabilities, and a native app experience!\n            </p>\n          </div>\n        </div>\n      '),confirmButtonText:"Got it!",confirmButtonColor:"#3b82f6"})};return c?(0,a.jsx)("div",{className:"".concat(t),children:"homepage"===s?(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-check-circle text-4xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"App Installed!"}),(0,a.jsx)("p",{className:"text-white/80",children:"MyTube is installed on your device"})]}):(0,a.jsxs)("div",{className:"flex items-center text-green-400",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"App Installed"})]})}):"homepage"===s?(0,a.jsxs)("div",{className:"glass-card p-8 hover:scale-105 transition-transform ".concat(t),children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-5xl text-purple-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Get the best experience with our mobile app"}),(0,a.jsx)("div",{className:"space-y-3",children:i?(0,a.jsxs)("button",{onClick:x,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Install Now"]}):(0,a.jsxs)("button",{onClick:h,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Install"]})}),(0,a.jsx)("div",{className:"mt-4 text-white/60 text-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-bolt mr-1"}),"Faster"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-wifi mr-1"}),"Offline"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-home mr-1"}),"Home Screen"]})]})})]}):(0,a.jsx)("div",{className:"glass-card p-4 ".concat(t),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-purple-400 text-xl mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Get faster access & offline features"})]})]}),i?(0,a.jsxs)("button",{onClick:x,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-1"}),"Install"]}):(0,a.jsxs)("button",{onClick:h,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),"How to"]})]})})}},9289:(e,s,t)=>{Promise.resolve().then(t.bind(t,3792))}},e=>{var s=s=>e(e.s=s);e.O(0,[8320,6874,3063,8441,1684,7358],()=>s(9289)),_N_E=e.O()}]);