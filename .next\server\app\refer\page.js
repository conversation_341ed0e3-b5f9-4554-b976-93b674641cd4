(()=>{var e={};e.id=4363,e.ids=[4363],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},47939:(e,s,r)=>{Promise.resolve().then(r.bind(r,48067))},48067:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(60687),i=r(43210),a=r(85814),n=r.n(a),l=r(87979),o=r(3582),d=r(77567);function c(){let{user:e,loading:s}=(0,l.Nu)(),[r,a]=(0,i.useState)(null),[c,x]=(0,i.useState)([]),[u,p]=(0,i.useState)(!0),[m,h]=(0,i.useState)(0),f=async()=>{try{p(!0);let s=await (0,o.getUserData)(e.uid);if(a(s),s?.referralCode){let e=await (0,o.pl)(s.referralCode);x(e);let r=e.reduce((e,s)=>e+j(s.plan),0);h(r)}}catch(e){console.error("Error loading referral data:",e),d.A.fire({icon:"error",title:"Error",text:"Failed to load referral data. Please try again."})}finally{p(!1)}},b=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},j=e=>({Trial:0,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200})[e]||0;return s||u?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsxs)("div",{className:"min-h-screen p-4",children:[(0,t.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,t.jsx)("h1",{className:"text-xl font-bold text-white",children:"Refer & Earn"}),(0,t.jsxs)("button",{onClick:f,className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-code mr-2"}),"Your Referral Code"]}),(0,t.jsx)("div",{className:"bg-white/10 p-4 rounded-lg mb-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-2xl font-mono font-bold text-white",children:r?.referralCode||"Loading..."}),(0,t.jsxs)("button",{onClick:()=>{r?.referralCode&&(navigator.clipboard.writeText(r.referralCode),d.A.fire({icon:"success",title:"Copied!",text:"Referral code copied to clipboard",timer:1500,showConfirmButton:!1}))},className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-copy mr-2"}),"Copy"]})]})}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("button",{onClick:()=>{if(r?.referralCode){let e=`${window.location.origin}/register?ref=${r.referralCode}`;navigator.clipboard.writeText(e),d.A.fire({icon:"success",title:"Link Copied!",text:"Referral link copied to clipboard",timer:2e3,showConfirmButton:!1})}},className:"btn-primary",children:[(0,t.jsx)("i",{className:"fas fa-link mr-2"}),"Copy Referral Link"]}),(0,t.jsxs)("button",{onClick:()=>{if(r?.referralCode){let e=`${window.location.origin}/register?ref=${r.referralCode}`,s=`🎉 Join MyTube and start earning money by watching videos!

💰 Earn up to ₹30,000 per month
🎬 Watch videos and get paid
⚡ Quick and easy registration

Use my referral code: ${r.referralCode}

Join now: ${e}`;navigator.share?navigator.share({title:"Join MyTube and Start Earning",text:s,url:e}):(navigator.clipboard.writeText(s),d.A.fire({icon:"success",title:"Message Copied!",text:"Referral message copied to clipboard. Share it with your friends!",timer:2e3,showConfirmButton:!1}))}},className:"btn-secondary",children:[(0,t.jsx)("i",{className:"fas fa-share mr-2"}),"Share with Friends"]})]})]}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-list mr-2"}),"Your Referrals (",c.length,")"]}),0===c.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("i",{className:"fas fa-users text-white/30 text-4xl mb-4"}),(0,t.jsx)("p",{className:"text-white/60 mb-4",children:"No referrals yet"}),(0,t.jsx)("p",{className:"text-white/40 text-sm",children:"Start sharing your referral code to earn bonuses!"})]}):(0,t.jsx)("div",{className:"space-y-3",children:c.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/10 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3",children:(0,t.jsx)("span",{className:"text-white font-bold",children:e.name.charAt(0).toUpperCase()})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-white/60 text-sm",children:e.email}),(0,t.jsxs)("p",{className:"text-white/60 text-sm",children:["Joined: ",e.joinedDate.toLocaleDateString()]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("span",{className:`px-3 py-1 rounded-full text-white text-sm ${b(e.plan)}`,children:e.plan}),(0,t.jsxs)("p",{className:"text-green-400 font-bold mt-1",children:["+₹",j(e.plan)]})]})]},e.id))})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mb-6",children:[(0,t.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,t.jsx)("i",{className:"fas fa-users text-4xl text-blue-400 mb-4"}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-white",children:c.length}),(0,t.jsx)("p",{className:"text-white/80",children:"Total Referrals"})]}),(0,t.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,t.jsx)("i",{className:"fas fa-rupee-sign text-4xl text-green-400 mb-4"}),(0,t.jsxs)("h3",{className:"text-2xl font-bold text-white",children:["₹",m]}),(0,t.jsx)("p",{className:"text-white/80",children:"Total Earnings"})]}),(0,t.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,t.jsx)("i",{className:"fas fa-gift text-4xl text-purple-400 mb-4"}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-white",children:c.filter(e=>"Trial"!==e.plan).length}),(0,t.jsx)("p",{className:"text-white/80",children:"Paid Referrals"})]})]}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How Referral Works"]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,t.jsx)("span",{className:"text-white font-bold",children:"1"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-white font-semibold",children:"Share Your Code"}),(0,t.jsx)("p",{className:"text-white/80 text-sm",children:"Share your referral code or link with friends"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,t.jsx)("span",{className:"text-white font-bold",children:"2"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-white font-semibold",children:"Friend Joins"}),(0,t.jsx)("p",{className:"text-white/80 text-sm",children:"Your friend registers using your referral code"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,t.jsx)("span",{className:"text-white font-bold",children:"3"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-white font-semibold",children:"Friend Upgrades"}),(0,t.jsx)("p",{className:"text-white/80 text-sm",children:"When they purchase a plan, you earn bonus"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,t.jsx)("span",{className:"text-white font-bold",children:"4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-white font-semibold",children:"You Earn + 50 Videos"}),(0,t.jsx)("p",{className:"text-white/80 text-sm",children:"₹50-₹1200 bonus + 50 lifetime videos based on plan"}),(0,t.jsxs)("div",{className:"mt-2 text-xs text-white/60",children:[(0,t.jsx)("div",{children:"₹499 Plan: ₹50 • ₹2999 Plan: ₹300"}),(0,t.jsx)("div",{children:"₹3999 Plan: ₹400 • ₹5999 Plan: ₹700 • ₹9999 Plan: ₹1200"})]})]})]})]})]})]})]})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59127:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),i=r(48088),a=r(88170),n=r.n(a),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["refer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85825)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\refer\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\refer\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/refer/page",pathname:"/refer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85825:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\refer\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\refer\\page.tsx","default")},88267:(e,s,r)=>{Promise.resolve().then(r.bind(r,85825))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[6204,2756,7567,8441,3582,7979],()=>r(59127));module.exports=t})();