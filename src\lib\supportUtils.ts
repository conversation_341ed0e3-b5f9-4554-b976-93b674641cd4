/**
 * Utility functions for customer support availability
 */

export interface SupportAvailability {
  isAvailable: boolean
  message: string
  nextAvailableTime?: string
}

/**
 * Check if customer support is currently available
 * Support hours: 9 AM to 6 PM, Monday to Friday (working days)
 */
export function checkSupportAvailability(): SupportAvailability {
  const now = new Date()
  const currentHour = now.getHours()
  const currentDay = now.getDay() // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  
  // Check if it's a working day (Monday to Friday)
  const isWorkingDay = currentDay >= 1 && currentDay <= 5
  
  // Check if it's within support hours (9 AM to 6 PM)
  const isWithinHours = currentHour >= 9 && currentHour < 18
  
  if (!isWorkingDay) {
    // Weekend
    const daysUntilMonday = currentDay === 0 ? 1 : (8 - currentDay)
    const nextMonday = new Date(now)
    nextMonday.setDate(now.getDate() + daysUntilMonday)
    nextMonday.setHours(9, 0, 0, 0)
    
    return {
      isAvailable: false,
      message: 'Support is available Monday to Friday, 9 AM - 6 PM',
      nextAvailableTime: `Next available: ${nextMonday.toLocaleDateString()} at 9:00 AM`
    }
  }
  
  if (!isWithinHours) {
    if (currentHour < 9) {
      // Before 9 AM
      const todayAt9AM = new Date(now)
      todayAt9AM.setHours(9, 0, 0, 0)
      
      return {
        isAvailable: false,
        message: 'Support hours: 9 AM - 6 PM (Working days)',
        nextAvailableTime: `Available today at 9:00 AM`
      }
    } else {
      // After 6 PM
      const tomorrow = new Date(now)
      tomorrow.setDate(now.getDate() + 1)
      
      // Check if tomorrow is a working day
      const tomorrowDay = tomorrow.getDay()
      if (tomorrowDay >= 1 && tomorrowDay <= 5) {
        tomorrow.setHours(9, 0, 0, 0)
        return {
          isAvailable: false,
          message: 'Support hours: 9 AM - 6 PM (Working days)',
          nextAvailableTime: `Next available: ${tomorrow.toLocaleDateString()} at 9:00 AM`
        }
      } else {
        // Tomorrow is weekend, find next Monday
        const daysUntilMonday = tomorrowDay === 0 ? 1 : (8 - tomorrowDay)
        const nextMonday = new Date(tomorrow)
        nextMonday.setDate(tomorrow.getDate() + daysUntilMonday)
        nextMonday.setHours(9, 0, 0, 0)
        
        return {
          isAvailable: false,
          message: 'Support is available Monday to Friday, 9 AM - 6 PM',
          nextAvailableTime: `Next available: ${nextMonday.toLocaleDateString()} at 9:00 AM`
        }
      }
    }
  }
  
  // Support is available
  return {
    isAvailable: true,
    message: 'Support is currently available! We typically respond within minutes.'
  }
}

/**
 * Get support status message for display
 */
export function getSupportStatusMessage(): string {
  const availability = checkSupportAvailability()
  
  if (availability.isAvailable) {
    return '🟢 Support Online - We\'re here to help!'
  } else {
    return `🔴 Support Offline - ${availability.message}`
  }
}

/**
 * Get detailed support availability info
 */
export function getSupportAvailabilityDetails(): {
  status: 'online' | 'offline'
  message: string
  nextAvailable?: string
  hoursInfo: string
} {
  const availability = checkSupportAvailability()
  
  return {
    status: availability.isAvailable ? 'online' : 'offline',
    message: availability.message,
    nextAvailable: availability.nextAvailableTime,
    hoursInfo: 'Monday to Friday, 9:00 AM - 6:00 PM'
  }
}

/**
 * Check if it's currently a working day
 */
export function isWorkingDay(date: Date = new Date()): boolean {
  const day = date.getDay()
  return day >= 1 && day <= 5 // Monday to Friday
}

/**
 * Check if current time is within support hours (9 AM - 6 PM)
 */
export function isWithinSupportHours(date: Date = new Date()): boolean {
  const hour = date.getHours()
  return hour >= 9 && hour < 18
}

/**
 * Get next available support time
 */
export function getNextSupportTime(): Date {
  const now = new Date()
  const currentDay = now.getDay()
  const currentHour = now.getHours()
  
  // If it's a working day and before 9 AM, return today at 9 AM
  if (isWorkingDay(now) && currentHour < 9) {
    const today9AM = new Date(now)
    today9AM.setHours(9, 0, 0, 0)
    return today9AM
  }
  
  // Otherwise, find next working day at 9 AM
  const nextDay = new Date(now)
  nextDay.setDate(now.getDate() + 1)
  nextDay.setHours(9, 0, 0, 0)
  
  // Keep adding days until we find a working day
  while (!isWorkingDay(nextDay)) {
    nextDay.setDate(nextDay.getDate() + 1)
  }
  
  return nextDay
}
