'use client'

import { useState } from 'react'

export default function ClearCachePage() {
  const [result, setResult] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const addToResult = (text: string) => {
    setResult(prev => prev + text + '\n')
    console.log(text)
  }

  const clearCache = async () => {
    setResult('')
    setIsLoading(true)

    try {
      addToResult('🧹 Starting cache clearing process...')

      // Clear all caches
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        addToResult(`📦 Found ${cacheNames.length} caches: ${cacheNames.join(', ')}`)

        for (const cacheName of cacheNames) {
          await caches.delete(cacheName)
          addToResult(`✅ Deleted cache: ${cacheName}`)
        }
      } else {
        addToResult('❌ Cache API not supported')
      }

      // Unregister service worker
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations()
        addToResult(`🔧 Found ${registrations.length} service worker registrations`)

        for (const registration of registrations) {
          await registration.unregister()
          addToResult(`✅ Unregistered service worker: ${registration.scope}`)
        }
      } else {
        addToResult('❌ Service Worker API not supported')
      }

      // Clear localStorage
      localStorage.clear()
      addToResult('✅ Cleared localStorage')

      // Clear sessionStorage
      sessionStorage.clear()
      addToResult('✅ Cleared sessionStorage')

      // Clear IndexedDB (if any)
      if ('indexedDB' in window) {
        addToResult('✅ IndexedDB available (manual clearing may be needed)')
      }

      addToResult('\n🎉 Cache clearing completed!')
      addToResult('💡 Refresh the page to see changes')

    } catch (error: any) {
      addToResult(`❌ Cache clearing failed: ${error.message}`)
      console.error('Cache clearing error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const hardRefresh = () => {
    addToResult('🔄 Performing hard refresh...')
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="glass-card p-8">
          <h1 className="text-3xl font-bold text-white mb-6">Clear Cache & Service Worker</h1>
          
          <div className="space-y-4 mb-6">
            <button
              onClick={clearCache}
              disabled={isLoading}
              className="btn-primary w-full"
            >
              {isLoading ? 'Clearing Cache...' : 'Clear All Cache & Service Workers'}
            </button>

            <button
              onClick={hardRefresh}
              className="btn-secondary w-full"
            >
              Hard Refresh Page
            </button>
          </div>

          <div className="bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto">
            {result || 'Click the button to clear cache and service workers...'}
          </div>

          <div className="mt-6 p-4 bg-yellow-500/20 rounded-lg">
            <h3 className="text-white font-bold mb-2">Instructions:</h3>
            <ol className="text-white/80 text-sm space-y-1">
              <li>1. Click "Clear All Cache & Service Workers"</li>
              <li>2. Wait for completion</li>
              <li>3. Click "Hard Refresh Page" or press Ctrl+Shift+R</li>
              <li>4. Try registration again</li>
            </ol>
          </div>

          <div className="mt-6 space-y-2">
            <a href="/register" className="btn-primary inline-block">
              Go to Registration
            </a>
            <a href="/debug-registration-simple" className="btn-secondary inline-block ml-4">
              Go to Debug Registration
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
