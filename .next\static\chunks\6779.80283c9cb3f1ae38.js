"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779],{6779:(t,e,a)=>{a.d(e,{CF:()=>c,I0:()=>u,Pn:()=>s,TK:()=>h,getWithdrawals:()=>w,hG:()=>D,lo:()=>i,nQ:()=>E,updateWithdrawalStatus:()=>L,x5:()=>l});var o=a(5317),r=a(6104),n=a(3592);let d=new Map;async function s(){let t="dashboard-stats",e=function(t){let e=d.get(t);return e&&Date.now()-e.timestamp<3e5?e.data:null}(t);if(e)return e;try{let e=new Date;e.setHours(0,0,0,0);let a=o.Dc.fromDate(e),s=await (0,o.getDocs)((0,o.collection)(r.db,n.COLLECTIONS.users)),i=s.size,l=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o._M)(n.FIELD_NAMES.joinedDate,">=",a)),c=(await (0,o.getDocs)(l)).size,E=0,u=0,w=0,h=0;s.forEach(t=>{var a;let o=t.data();E+=o[n.FIELD_NAMES.totalVideos]||0,u+=o[n.FIELD_NAMES.wallet]||0;let r=null==(a=o[n.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();r&&r.toDateString()===e.toDateString()&&(w+=o[n.FIELD_NAMES.todayVideos]||0)});try{let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.transactions),(0,o._M)(n.FIELD_NAMES.type,"==","video_earning"),(0,o.AB)(1e3));(await (0,o.getDocs)(t)).forEach(t=>{var a;let o=t.data(),r=null==(a=o[n.FIELD_NAMES.date])?void 0:a.toDate();r&&r>=e&&(h+=o[n.FIELD_NAMES.amount]||0)})}catch(t){console.warn("Could not fetch today's transactions:",t)}let D=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o._M)("status","==","pending")),L=(await (0,o.getDocs)(D)).size,g=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o._M)("date",">=",a)),S=(await (0,o.getDocs)(g)).size,C={totalUsers:i,totalVideos:E,totalEarnings:u,pendingWithdrawals:L,todayUsers:c,todayVideos:w,todayEarnings:h,todayWithdrawals:S};return d.set(t,{data:C,timestamp:Date.now()}),C}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function i(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,o.AB)(t));e&&(a=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.FIELD_NAMES.joinedDate,"desc"),(0,o.HM)(e),(0,o.AB)(t)));let d=await (0,o.getDocs)(a);return{users:d.docs.map(t=>{var e,a;return{id:t.id,...t.data(),joinedDate:null==(e=t.data()[n.FIELD_NAMES.joinedDate])?void 0:e.toDate(),planExpiry:null==(a=t.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function l(t){try{if(!t||0===t.trim().length)return[];let e=t.toLowerCase().trim(),a=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(a)).docs.map(t=>{var e,a;return{id:t.id,...t.data(),joinedDate:null==(e=t.data()[n.FIELD_NAMES.joinedDate])?void 0:e.toDate(),planExpiry:null==(a=t.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(t=>{let a=String(t[n.FIELD_NAMES.name]||"").toLowerCase(),o=String(t[n.FIELD_NAMES.email]||"").toLowerCase(),r=String(t[n.FIELD_NAMES.mobile]||"").toLowerCase(),d=String(t[n.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(e)||o.includes(e)||r.includes(e)||d.includes(e)})}catch(t){throw console.error("Error searching users:",t),t}}async function c(){try{let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.FIELD_NAMES.joinedDate,"desc"));return(await (0,o.getDocs)(t)).docs.map(t=>{var e,a;return{id:t.id,...t.data(),joinedDate:null==(e=t.data()[n.FIELD_NAMES.joinedDate])?void 0:e.toDate(),planExpiry:null==(a=t.data()[n.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(t){throw console.error("Error getting all users:",t),t}}async function E(){try{let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users));return(await (0,o.getDocs)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function u(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.transactions),(0,o.My)(n.FIELD_NAMES.date,"desc"),(0,o.AB)(t));e&&(a=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.transactions),(0,o.My)(n.FIELD_NAMES.date,"desc"),(0,o.HM)(e),(0,o.AB)(t)));let d=await (0,o.getDocs)(a);return{transactions:d.docs.map(t=>{var e;return{id:t.id,...t.data(),date:null==(e=t.data()[n.FIELD_NAMES.date])?void 0:e.toDate()}}),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting transactions:",t),t}}async function w(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o.My)("date","desc"),(0,o.AB)(t));e&&(a=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o.My)("date","desc"),(0,o.HM)(e),(0,o.AB)(t)));let d=await (0,o.getDocs)(a);return{withdrawals:d.docs.map(t=>{var e;return{id:t.id,...t.data(),date:null==(e=t.data().date)?void 0:e.toDate()}}),lastDoc:d.docs[d.docs.length-1]||null,hasMore:d.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function h(t,e){try{await (0,o.mZ)((0,o.H9)(r.db,n.COLLECTIONS.users,t),e),d.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function D(t){try{await (0,o.kd)((0,o.H9)(r.db,n.COLLECTIONS.users,t)),d.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function L(t,e,s){try{let i=await (0,o.x7)((0,o.H9)(r.db,n.COLLECTIONS.withdrawals,t));if(!i.exists())throw Error("Withdrawal not found");let{userId:l,amount:c,status:E}=i.data(),u={status:e,updatedAt:o.Dc.now()};if(s&&(u.adminNotes=s),await (0,o.mZ)((0,o.H9)(r.db,n.COLLECTIONS.withdrawals,t),u),"approved"===e&&"approved"!==E){let{addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await t(l,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===e&&"rejected"!==E){let{updateWalletBalance:t,addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await t(l,c),await e(l,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}d.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}}]);