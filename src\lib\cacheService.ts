// Advanced Caching Service for Firestore Read Optimization
// Implements multiple caching layers to minimize pay-as-you-go costs

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  version: string
}

interface CacheConfig {
  defaultTTL: number
  maxSize: number
  enableLocalStorage: boolean
  enableMemoryCache: boolean
}

class CacheService {
  private memoryCache = new Map<string, CacheEntry<any>>()
  private config: CacheConfig
  private readonly VERSION = '1.0.0'

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5 minutes default
      maxSize: 1000,
      enableLocalStorage: true,
      enableMemoryCache: true,
      ...config
    }
  }

  // Generate cache key with user context
  private generateKey(namespace: string, identifier: string, userId?: string): string {
    const userPrefix = userId ? `user:${userId}:` : 'global:'
    return `${userPrefix}${namespace}:${identifier}`
  }

  // Check if cache entry is valid
  private isValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < entry.ttl && entry.version === this.VERSION
  }

  // Get from memory cache
  private getFromMemory<T>(key: string): T | null {
    if (!this.config.enableMemoryCache) return null
    
    const entry = this.memoryCache.get(key)
    if (entry && this.isValid(entry)) {
      return entry.data
    }
    
    if (entry) {
      this.memoryCache.delete(key)
    }
    return null
  }

  // Set to memory cache with size limit
  private setToMemory<T>(key: string, data: T, ttl: number): void {
    if (!this.config.enableMemoryCache) return

    // Implement LRU eviction if cache is full
    if (this.memoryCache.size >= this.config.maxSize) {
      const firstKey = this.memoryCache.keys().next().value
      if (firstKey) {
        this.memoryCache.delete(firstKey)
      }
    }

    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      version: this.VERSION
    })
  }

  // Get from localStorage
  private getFromLocalStorage<T>(key: string): T | null {
    if (!this.config.enableLocalStorage || typeof window === 'undefined') return null
    
    try {
      const stored = localStorage.getItem(`cache:${key}`)
      if (!stored) return null
      
      const entry: CacheEntry<T> = JSON.parse(stored)
      if (this.isValid(entry)) {
        return entry.data
      }
      
      localStorage.removeItem(`cache:${key}`)
      return null
    } catch (error) {
      console.warn('Error reading from localStorage cache:', error)
      return null
    }
  }

  // Set to localStorage
  private setToLocalStorage<T>(key: string, data: T, ttl: number): void {
    if (!this.config.enableLocalStorage || typeof window === 'undefined') return
    
    try {
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl,
        version: this.VERSION
      }
      localStorage.setItem(`cache:${key}`, JSON.stringify(entry))
    } catch (error) {
      console.warn('Error writing to localStorage cache:', error)
    }
  }

  // Public API: Get cached data
  get<T>(namespace: string, identifier: string, userId?: string): T | null {
    const key = this.generateKey(namespace, identifier, userId)
    
    // Try memory cache first (fastest)
    const memoryResult = this.getFromMemory<T>(key)
    if (memoryResult !== null) {
      return memoryResult
    }
    
    // Try localStorage (persistent across sessions)
    const localStorageResult = this.getFromLocalStorage<T>(key)
    if (localStorageResult !== null) {
      // Promote to memory cache
      this.setToMemory(key, localStorageResult, this.config.defaultTTL)
      return localStorageResult
    }
    
    return null
  }

  // Public API: Set cached data
  set<T>(namespace: string, identifier: string, data: T, userId?: string, customTTL?: number): void {
    const key = this.generateKey(namespace, identifier, userId)
    const ttl = customTTL || this.config.defaultTTL
    
    this.setToMemory(key, data, ttl)
    this.setToLocalStorage(key, data, ttl)
  }

  // Public API: Invalidate specific cache
  invalidate(namespace: string, identifier: string, userId?: string): void {
    const key = this.generateKey(namespace, identifier, userId)
    
    this.memoryCache.delete(key)
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`cache:${key}`)
    }
  }

  // Public API: Invalidate all cache for a user
  invalidateUser(userId: string): void {
    const userPrefix = `user:${userId}:`
    
    // Clear memory cache
    const keysToDelete: string[] = []
    this.memoryCache.forEach((_, key) => {
      if (key.startsWith(userPrefix)) {
        keysToDelete.push(key)
      }
    })
    keysToDelete.forEach(key => this.memoryCache.delete(key))
    
    // Clear localStorage
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(`cache:${userPrefix}`)) {
          localStorage.removeItem(key)
        }
      })
    }
  }

  // Public API: Clear all cache
  clear(): void {
    this.memoryCache.clear()
    
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('cache:')) {
          localStorage.removeItem(key)
        }
      })
    }
  }

  // Public API: Get cache statistics
  getStats() {
    return {
      memorySize: this.memoryCache.size,
      maxSize: this.config.maxSize,
      version: this.VERSION,
      config: this.config
    }
  }
}

// Cache TTL configurations for different data types
export const CACHE_TTL = {
  USER_DATA: 10 * 60 * 1000,        // 10 minutes - user profile data
  WALLET_DATA: 5 * 60 * 1000,       // 5 minutes - wallet balance
  VIDEO_COUNT: 2 * 60 * 1000,       // 2 minutes - video counts
  PLAN_STATUS: 15 * 60 * 1000,      // 15 minutes - plan expiry status
  NOTIFICATIONS: 30 * 60 * 1000,    // 30 minutes - notifications
  ADMIN_STATS: 5 * 60 * 1000,       // 5 minutes - admin dashboard stats
  TRANSACTIONS: 10 * 60 * 1000,     // 10 minutes - transaction history
  WITHDRAWALS: 5 * 60 * 1000,       // 5 minutes - withdrawal data
  LEAVE_STATUS: 60 * 60 * 1000,     // 1 hour - leave status
  QUICK_VIDEO: 5 * 60 * 1000,       // 5 minutes - quick video settings
  ACTIVE_DAYS: 30 * 60 * 1000       // 30 minutes - active days (updated daily)
}

// Cache namespaces for organization
export const CACHE_NAMESPACE = {
  USER: 'user',
  WALLET: 'wallet', 
  VIDEO: 'video',
  PLAN: 'plan',
  NOTIFICATION: 'notification',
  ADMIN: 'admin',
  TRANSACTION: 'transaction',
  WITHDRAWAL: 'withdrawal',
  LEAVE: 'leave',
  SETTINGS: 'settings'
}

// Global cache instance
export const cacheService = new CacheService({
  defaultTTL: 5 * 60 * 1000,  // 5 minutes
  maxSize: 1000,
  enableLocalStorage: true,
  enableMemoryCache: true
})

// Helper function for cached Firestore reads
export async function cachedFirestoreRead<T>(
  namespace: string,
  identifier: string,
  firestoreFunction: () => Promise<T>,
  userId?: string,
  customTTL?: number
): Promise<T> {
  // Try cache first
  const cached = cacheService.get<T>(namespace, identifier, userId)
  if (cached !== null) {
    console.log(`🎯 Cache hit: ${namespace}:${identifier}`)
    return cached
  }
  
  // Cache miss - fetch from Firestore
  console.log(`📡 Cache miss: ${namespace}:${identifier} - fetching from Firestore`)
  const data = await firestoreFunction()
  
  // Cache the result
  cacheService.set(namespace, identifier, data, userId, customTTL)
  
  return data
}

// Batch invalidation helper
export function invalidateRelatedCache(userId: string, ...namespaces: string[]): void {
  namespaces.forEach(namespace => {
    // Invalidate specific user cache
    cacheService.invalidate(namespace, 'data', userId)
    
    // Also invalidate any global cache that might be affected
    if (namespace === CACHE_NAMESPACE.USER) {
      cacheService.invalidate(CACHE_NAMESPACE.ADMIN, 'stats')
    }
  })
}
