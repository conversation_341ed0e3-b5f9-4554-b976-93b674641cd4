(()=>{var e={};e.id=3698,e.ids=[1391,3698],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},9504:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12454:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var a=t(60687),r=t(43210),i=t(85814),l=t.n(i),d=t(30474),n=t(87979);t(91391);var o=t(51278);function c(){let{user:e,loading:s,isAdmin:t}=(0,n.wC)(),[i,c]=(0,r.useState)(null),[x,h]=(0,r.useState)(!0),[m,g]=(0,r.useState)(!1);return s||x?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,a.jsxs)("aside",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ${m?"translate-x-0":"-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-center h-16 bg-gray-900",children:[(0,a.jsx)(d.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:32,height:32,className:"mr-2"}),(0,a.jsx)("span",{className:"text-white text-xl font-bold",children:"MyTube Admin"})]}),(0,a.jsx)("nav",{className:"mt-8",children:(0,a.jsxs)("div",{className:"px-4 space-y-2",children:[(0,a.jsxs)(l(),{href:"/admin",className:"flex items-center px-4 py-2 text-white bg-gray-700 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-tachometer-alt mr-3"}),"Dashboard"]}),(0,a.jsxs)(l(),{href:"/admin/users",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-users mr-3"}),"Users"]}),(0,a.jsxs)(l(),{href:"/admin/simple-upload",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-file-csv mr-3"}),"Simple Upload"]}),(0,a.jsxs)(l(),{href:"/admin/transactions",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-exchange-alt mr-3"}),"Transactions"]}),(0,a.jsxs)(l(),{href:"/admin/withdrawals",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave mr-3"}),"Withdrawals"]}),(0,a.jsxs)(l(),{href:"/admin/notifications",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-bell mr-3"}),"Notifications"]}),(0,a.jsxs)(l(),{href:"/admin/leaves",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-calendar-times mr-3"}),"Leave Management"]}),(0,a.jsxs)(l(),{href:"/admin/settings",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-cog mr-3"}),"Settings"]}),(0,a.jsxs)(l(),{href:"/admin/fix-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-tools mr-3"}),"Fix Active Days"]}),(0,a.jsxs)(l(),{href:"/admin/daily-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Daily Active Days"]})]})}),(0,a.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,a.jsxs)("button",{onClick:()=>{(0,o._f)(e?.uid,"/admin/login")},className:"w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt mr-3"}),"Logout"]})})]}),(0,a.jsxs)("div",{className:"lg:ml-64",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,a.jsx)("button",{onClick:()=>g(!m),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-bars text-xl"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"text-gray-700",children:"Welcome, Admin"}),(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-user-shield text-gray-600"})})]})]})}),(0,a.jsxs)("main",{className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-users text-blue-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i?.totalUsers?.toLocaleString()||"0"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-video text-green-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Videos"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i?.totalVideos?.toLocaleString()||"0"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-rupee-sign text-yellow-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Earnings"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₹",i?.totalEarnings?.toLocaleString()||"0"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-clock text-red-600 text-xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:i?.pendingWithdrawals||"0"})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow mb-8",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Today's Activity"})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:i?.todayUsers||"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"New Users"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-green-600",children:i?.todayVideos?.toLocaleString()||"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Videos Watched"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-3xl font-bold text-yellow-600",children:["₹",i?.todayEarnings?.toLocaleString()||"0"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Earnings Paid"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-3xl font-bold text-red-600",children:i?.todayWithdrawals||"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Withdrawals"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(l(),{href:"/admin/users",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-users text-blue-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manage Users"}),(0,a.jsx)("p",{className:"text-gray-600",children:"View and manage user accounts"})]})]})}),(0,a.jsx)(l(),{href:"/admin/withdrawals",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-money-bill-wave text-green-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Process Withdrawals"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Review and approve withdrawals"})]})]})}),(0,a.jsx)(l(),{href:"/admin/notifications",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-bell text-yellow-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Send Notifications"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Notify users about updates"})]})]})}),(0,a.jsx)(l(),{href:"/admin/settings",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-cog text-purple-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Settings"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Configure platform settings"})]})]})}),(0,a.jsx)(l(),{href:"/admin/fix-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-tools text-red-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fix Active Days"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Fix daily counts and active days"})]})]})}),(0,a.jsx)(l(),{href:"/admin/simple-upload",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-file-csv text-green-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Simple Upload"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Update videos, wallet & active days via CSV"})]})]})}),(0,a.jsx)(l(),{href:"/admin/daily-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-indigo-100 rounded-lg",children:(0,a.jsx)("i",{className:"fas fa-calendar-plus text-indigo-600 text-2xl"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Daily Active Days"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage daily active days increment"})]})]})})]})]})]}),m&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>g(!1)})]})}},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22528:(e,s,t)=>{Promise.resolve().then(t.bind(t,12454))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91391:(e,s,t)=>{"use strict";t.d(s,{CF:()=>c,I0:()=>h,Pn:()=>d,TK:()=>u,getAllPendingWithdrawals:()=>m,getAllWithdrawals:()=>g,hG:()=>p,lo:()=>n,nQ:()=>x,updateWithdrawalStatus:()=>N,x5:()=>o});var a=t(75535),r=t(33784),i=t(3582);let l=new Map;async function d(){let e="dashboard-stats",s=function(e){let s=l.get(e);return s&&Date.now()-s.timestamp<3e5?s.data:null}(e);if(s)return s;try{let s=new Date;s.setHours(0,0,0,0);let t=a.Dc.fromDate(s),d=await (0,a.getDocs)((0,a.collection)(r.db,i.COLLECTIONS.users)),n=d.size,o=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a._M)(i.FIELD_NAMES.joinedDate,">=",t)),c=(await (0,a.getDocs)(o)).size,x=0,h=0,m=0,g=0;d.forEach(e=>{let t=e.data();x+=t[i.FIELD_NAMES.totalVideos]||0,h+=t[i.FIELD_NAMES.wallet]||0;let a=t[i.FIELD_NAMES.lastVideoDate]?.toDate();a&&a.toDateString()===s.toDateString()&&(m+=t[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,a.AB)(1e3));(await (0,a.getDocs)(e)).forEach(e=>{let t=e.data(),a=t[i.FIELD_NAMES.date]?.toDate();a&&a>=s&&(g+=t[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let u=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending")),p=(await (0,a.getDocs)(u)).size,N=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("date",">=",t)),f=(await (0,a.getDocs)(N)).size,y={totalUsers:n,totalVideos:x,totalEarnings:h,pendingWithdrawals:p,todayUsers:c,todayVideos:m,todayEarnings:g,todayWithdrawals:f};return l.set(e,{data:y,timestamp:Date.now()}),y}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function n(e=50,s=null){try{let t=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.AB)(e));s&&(t=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,a.HM)(s),(0,a.AB)(e)));let l=await (0,a.getDocs)(t);return{users:l.docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function o(e){try{if(!e||0===e.trim().length)return[];let s=e.toLowerCase().trim(),t=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()})).filter(e=>{let t=String(e[i.FIELD_NAMES.name]||"").toLowerCase(),a=String(e[i.FIELD_NAMES.email]||"").toLowerCase(),r=String(e[i.FIELD_NAMES.mobile]||"").toLowerCase(),l=String(e[i.FIELD_NAMES.referralCode]||"").toLowerCase();return t.includes(s)||a.includes(s)||r.includes(s)||l.includes(s)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users),(0,a.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.FIELD_NAMES.joinedDate]?.toDate(),planExpiry:e.data()[i.FIELD_NAMES.planExpiry]?.toDate()}))}catch(e){throw console.error("Error getting all users:",e),e}}async function x(){try{let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.users));return(await (0,a.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function h(e=50,s=null){try{let t=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.AB)(e));s&&(t=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.transactions),(0,a.My)(i.FIELD_NAMES.date,"desc"),(0,a.HM)(s),(0,a.AB)(e)));let l=await (0,a.getDocs)(t);return{transactions:l.docs.map(e=>({id:e.id,...e.data(),date:e.data()[i.FIELD_NAMES.date]?.toDate()})),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function m(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending"),(0,a.My)("date","desc")),s=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${s.length} pending withdrawals`),s}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,a.P)((0,a.collection)(r.db,i.COLLECTIONS.withdrawals),(0,a.My)("date","desc")),s=(await (0,a.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}));return console.log(`✅ Loaded ${s.length} total withdrawals`),s}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function u(e,s){try{await (0,a.mZ)((0,a.H9)(r.db,i.COLLECTIONS.users,e),s),l.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function p(e){try{await (0,a.kd)((0,a.H9)(r.db,i.COLLECTIONS.users,e)),l.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function N(e,s,d){try{let n=await (0,a.x7)((0,a.H9)(r.db,i.COLLECTIONS.withdrawals,e));if(!n.exists())throw Error("Withdrawal not found");let{userId:o,amount:c,status:x}=n.data(),h={status:s,updatedAt:a.Dc.now()};if(d&&(h.adminNotes=d),await (0,a.mZ)((0,a.H9)(r.db,i.COLLECTIONS.withdrawals,e),h),"approved"===s&&"approved"!==x){let{addTransaction:e}=await Promise.resolve().then(t.bind(t,3582));await e(o,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===s&&"rejected"!==x){let{updateWalletBalance:e,addTransaction:s}=await Promise.resolve().then(t.bind(t,3582));await e(o,c),await s(o,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}l.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91445:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>o});var a=t(65239),r=t(48088),i=t(88170),l=t.n(i),d=t(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let o={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[6204,6958,7567,8441,3582,7979],()=>t(91445));module.exports=a})();