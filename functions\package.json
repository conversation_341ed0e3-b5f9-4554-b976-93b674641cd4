{"name": "mytube-functions", "description": "Firebase Functions for MyTube Platform - Optimized for Pay-as-you-go", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": ">=18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.1.0", "firebase-functions": "^5.0.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0"}, "private": true}