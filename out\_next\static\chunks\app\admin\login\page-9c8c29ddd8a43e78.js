(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2116],{1469:(e,s,a)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var a in s)Object.defineProperty(e,a,{enumerable:!0,get:s[a]})}(s,{default:function(){return d},getImageProps:function(){return l}});let t=a(8229),i=a(8883),r=a(3063),n=t._(a(1193));function l(e){let{props:s}=(0,i.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(s))void 0===a&&delete s[e];return{props:s}}let d=r.Image},5430:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var t=a(5155),i=a(2115),r=a(6874),n=a.n(r),l=a(6766),d=a(3004),c=a(6104),o=a(6681),m=a(4752),u=a.n(m);function h(){let{user:e,loading:s}=(0,o.hD)(),[a,r]=(0,i.useState)(""),[m,h]=(0,i.useState)(""),[f,x]=(0,i.useState)(!1),[p,b]=(0,i.useState)(!1);(0,i.useEffect)(()=>{e&&!s&&(["<EMAIL>","<EMAIL>"].includes(e.email||"")?window.location.href="/admin":(c.j2.signOut(),u().fire({icon:"error",title:"Access Denied",text:"You do not have admin privileges"})))},[e,s]);let g=async e=>{if(e.preventDefault(),!a||!m)return void u().fire({icon:"error",title:"Error",text:"Please fill in all fields"});x(!0);try{let e=(await (0,d.x9)(c.j2,a,m)).user;if(!["<EMAIL>","<EMAIL>"].includes(e.email||""))throw await c.j2.signOut(),Error("Access denied. Admin privileges required.")}catch(s){console.error("Admin login error:",s);let e="An error occurred during login";if(s.message.includes("Access denied"))e="Access denied. Admin privileges required.";else switch(s.code){case"auth/user-not-found":e="No admin account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This admin account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=s.message||"Admin login failed"}u().fire({icon:"error",title:"Admin Login Failed",text:e}),h("")}finally{x(!1)}};return s?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,t.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,t.jsx)(l.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Panel"}),(0,t.jsx)("p",{className:"text-white/80",children:"Sign in to access admin dashboard"})]}),(0,t.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:[(0,t.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Admin Email"]}),(0,t.jsx)("input",{type:"email",id:"email",value:a,onChange:e=>r(e.target.value),className:"form-input",placeholder:"Enter admin email",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:[(0,t.jsx)("i",{className:"fas fa-lock mr-2"}),"Password"]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:p?"text":"password",id:"password",value:m,onChange:e=>h(e.target.value),className:"form-input pr-12",placeholder:"Enter admin password",required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>b(!p),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",children:(0,t.jsx)("i",{className:"fas ".concat(p?"fa-eye-slash":"fa-eye")})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:f,className:"w-full btn-primary flex items-center justify-center",children:f?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Signing in..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]})})]}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-red-500/20 rounded-lg border border-red-500/30",children:(0,t.jsxs)("div",{className:"flex items-center text-red-300",children:[(0,t.jsx)("i",{className:"fas fa-shield-alt mr-2"}),(0,t.jsx)("span",{className:"text-sm",children:"This is a secure admin area. Only authorized personnel can access this panel."})]})}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)(n(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},6766:(e,s,a)=>{"use strict";a.d(s,{default:()=>i.a});var t=a(1469),i=a.n(t)},9833:(e,s,a)=>{Promise.resolve().then(a.bind(a,5430))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3063,6681,8441,1684,7358],()=>s(9833)),_N_E=e.O()}]);