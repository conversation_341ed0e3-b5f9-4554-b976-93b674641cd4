'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAdmin } from '@/hooks/useAuth'
import { downloadCSV } from '@/lib/csvExport'
import Swal from 'sweetalert2'

interface AdminLeave {
  id: string
  date: Date
  reason: string
  type: 'holiday' | 'maintenance' | 'emergency'
  createdBy: string
  createdAt: Date
}

export default function AdminLeavesPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const [leaves, setLeaves] = useState<AdminLeave[]>([])
  const [dataLoading, setDataLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  
  const [formData, setFormData] = useState({
    date: '',
    reason: '',
    type: 'holiday' as 'holiday' | 'maintenance' | 'emergency'
  })

  useEffect(() => {
    if (isAdmin) {
      loadLeaves()
    }
  }, [isAdmin])

  const loadLeaves = async () => {
    try {
      setDataLoading(true)

      // Load actual admin leaves from Firestore
      const { getAdminLeaves } = await import('@/lib/leaveService')
      const adminLeaves = await getAdminLeaves()
      setLeaves(adminLeaves)
    } catch (error) {
      console.error('Error loading leaves:', error)
      setLeaves([]) // Set empty array on error
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to load leaves. Please try again.',
      })
    } finally {
      setDataLoading(false)
    }
  }

  const handleCreateLeave = async () => {
    try {
      if (!formData.date || !formData.reason.trim()) {
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: 'Please fill in all required fields.',
        })
        return
      }

      const selectedDate = new Date(formData.date)
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      if (selectedDate < today) {
        Swal.fire({
          icon: 'error',
          title: 'Invalid Date',
          text: 'Cannot create leave for past dates.',
        })
        return
      }

      // Check if leave already exists for this date
      const existingLeave = leaves.find(leave => 
        leave.date.toDateString() === selectedDate.toDateString()
      )

      if (existingLeave) {
        Swal.fire({
          icon: 'error',
          title: 'Duplicate Leave',
          text: 'Leave already exists for this date.',
        })
        return
      }

      setIsSaving(true)

      // Save to Firestore
      const { createAdminLeave } = await import('@/lib/leaveService')
      const leaveId = await createAdminLeave({
        date: selectedDate,
        reason: formData.reason.trim(),
        type: formData.type,
        createdBy: user?.email || 'admin'
      })

      // Reload leaves from Firestore to get updated data
      await loadLeaves()

      Swal.fire({
        icon: 'success',
        title: 'Leave Created!',
        text: `Admin leave created for ${selectedDate.toLocaleDateString()}.`,
        timer: 3000,
        showConfirmButton: false
      })

      // Reset form and close modal
      setFormData({
        date: '',
        reason: '',
        type: 'holiday'
      })
      setShowCreateModal(false)
    } catch (error) {
      console.error('Error creating leave:', error)
      Swal.fire({
        icon: 'error',
        title: 'Creation Failed',
        text: 'Failed to create leave. Please try again.',
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleDeleteLeave = async (leaveId: string) => {
    try {
      const result = await Swal.fire({
        title: 'Delete Leave',
        text: 'Are you sure you want to delete this leave?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, Delete',
        cancelButtonText: 'Cancel'
      })

      if (result.isConfirmed) {
        // Delete from Firestore
        const { deleteAdminLeave } = await import('@/lib/leaveService')
        await deleteAdminLeave(leaveId)

        // Reload leaves from Firestore to get updated data
        await loadLeaves()

        Swal.fire({
          icon: 'success',
          title: 'Leave Deleted',
          text: 'Leave has been deleted successfully.',
          timer: 2000,
          showConfirmButton: false
        })
      }
    } catch (error) {
      console.error('Error deleting leave:', error)
      Swal.fire({
        icon: 'error',
        title: 'Delete Failed',
        text: 'Failed to delete leave. Please try again.',
      })
    }
  }

  const handleExportLeaves = () => {
    if (leaves.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data',
        text: 'No leaves to export.',
      })
      return
    }

    const exportData = leaves.map(leave => ({
      'Date': leave.date instanceof Date ? leave.date.toLocaleDateString() : new Date(leave.date).toLocaleDateString(),
      'Reason': leave.reason,
      'Type': leave.type.charAt(0).toUpperCase() + leave.type.slice(1),
      'Created By': leave.createdBy,
      'Created At': leave.createdAt instanceof Date ? leave.createdAt.toLocaleDateString() : new Date(leave.createdAt).toLocaleDateString()
    }))

    downloadCSV(exportData, 'admin-leaves')
    
    Swal.fire({
      icon: 'success',
      title: 'Export Complete',
      text: `Exported ${leaves.length} leaves to CSV file.`,
      timer: 2000,
      showConfirmButton: false
    })
  }

  const getLeaveTypeColor = (type: string) => {
    switch (type) {
      case 'holiday':
        return 'bg-green-100 text-green-800'
      case 'maintenance':
        return 'bg-blue-100 text-blue-800'
      case 'emergency':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getLeaveTypeIcon = (type: string) => {
    switch (type) {
      case 'holiday':
        return 'fas fa-calendar-day text-green-500'
      case 'maintenance':
        return 'fas fa-tools text-blue-500'
      case 'emergency':
        return 'fas fa-exclamation-triangle text-red-500'
      default:
        return 'fas fa-calendar text-gray-500'
    }
  }

  if (loading || dataLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading leaves...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Link
              href="/admin"
              className="text-gray-500 hover:text-gray-700"
            >
              <i className="fas fa-arrow-left text-xl"></i>
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Leave Management</h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">Total: {leaves.length}</span>
            <button
              onClick={handleExportLeaves}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-download mr-2"></i>
              Export CSV
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-plus mr-2"></i>
              Add Leave
            </button>
            <button
              onClick={loadLeaves}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-sync-alt mr-2"></i>
              Refresh
            </button>
          </div>
        </div>
      </header>

      {/* Leaves List */}
      <div className="p-6">
        <div className="bg-white rounded-lg shadow overflow-hidden">
          {leaves.length === 0 ? (
            <div className="text-center py-12">
              <i className="fas fa-calendar-times text-gray-300 text-6xl mb-4"></i>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No leaves scheduled</h3>
              <p className="text-gray-500 mb-4">Create your first admin leave to block work and withdrawals</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg"
              >
                <i className="fas fa-plus mr-2"></i>
                Add First Leave
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reason
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created By
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {leaves.map((leave) => (
                    <tr key={leave.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {leave.date.toLocaleDateString()}
                        </div>
                        <div className="text-xs text-gray-500">
                          {leave.date.toLocaleDateString('en-US', { weekday: 'long' })}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xs">
                          {leave.reason}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getLeaveTypeColor(leave.type)}`}>
                          <i className={`${getLeaveTypeIcon(leave.type)} mr-1`}></i>
                          {leave.type.charAt(0).toUpperCase() + leave.type.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{leave.createdBy}</div>
                        <div className="text-xs text-gray-500">
                          {leave.createdAt.toLocaleDateString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleDeleteLeave(leave.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <i className="fas fa-trash mr-1"></i>
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create Leave Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Add Admin Leave</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <i className="fas fa-times text-xl"></i>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="holiday">Holiday</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="emergency">Emergency</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Reason</label>
                <textarea
                  value={formData.reason}
                  onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter reason for leave..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-4 mt-6">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateLeave}
                disabled={isSaving}
                className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
              >
                {isSaving ? (
                  <>
                    <div className="spinner w-4 h-4 mr-2 inline-block"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <i className="fas fa-plus mr-2"></i>
                    Create Leave
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
