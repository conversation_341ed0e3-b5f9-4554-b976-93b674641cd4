(()=>{var e={};e.id=6950,e.ids=[6950],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20845:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),o=r(48088),l=r(88170),a=r.n(l),n=r(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let c={children:["",{children:["test-videos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21320)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-videos\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-videos\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/test-videos/page",pathname:"/test-videos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21320:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-videos\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-videos\\page.tsx","default")},23302:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(60687),o=r(43210),l=r(59007);function a(){let[e,t]=(0,o.useState)([]),[r,a]=(0,o.useState)({totalVideos:0,currentBatch:0,totalBatches:0,videosInCurrentBatch:0}),[n,i]=(0,o.useState)(!1),[c,d]=(0,o.useState)(null),h=async()=>{i(!0),d(null);try{console.log("Loading videos from file...");let e=await (0,l.tx)();console.log("Videos loaded:",e.length),t(e.slice(0,10));let r=(0,l.No)();a(r)}catch(e){console.error("Error loading videos:",e),d(e.message)}finally{i(!1)}},u=async()=>{i(!0),d(null);try{console.log("Initializing video system...");let e=await (0,l.ZB)();console.log("System initialized, current batch:",e.length),t(e.slice(0,10));let r=(0,l.No)();a(r)}catch(e){console.error("Error initializing system:",e),d(e.message)}finally{i(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Video System Test"}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Test Controls"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)("button",{onClick:h,disabled:n,className:"btn-primary",children:n?"Loading...":"Load Videos from File"}),(0,s.jsx)("button",{onClick:u,disabled:n,className:"btn-secondary",children:n?"Loading...":"Initialize Video System"}),(0,s.jsx)("button",{onClick:()=>{(0,l.iD)(),t([]),a({totalVideos:0,currentBatch:0,totalBatches:0,videosInCurrentBatch:0}),console.log("Storage cleared")},className:"btn-danger",children:"Clear Storage"})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Video Statistics"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-green-400",children:r.totalVideos}),(0,s.jsx)("p",{className:"text-white/60",children:"Total Videos"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-2xl font-bold text-blue-400",children:[r.currentBatch+1,"/",r.totalBatches]}),(0,s.jsx)("p",{className:"text-white/60",children:"Current Batch"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-purple-400",children:r.videosInCurrentBatch}),(0,s.jsx)("p",{className:"text-white/60",children:"Videos in Batch"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:e.length}),(0,s.jsx)("p",{className:"text-white/60",children:"Displayed"})]})]})]}),c&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6 border-red-500 border",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-red-400 mb-2",children:"Error"}),(0,s.jsx)("p",{className:"text-white",children:c})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Videos Preview (First 10)"}),0===e.length?(0,s.jsx)("p",{className:"text-white/60 text-center py-8",children:"No videos loaded. Click a button above to test."}):(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map((e,t)=>(0,s.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"aspect-video mb-3",children:(0,s.jsx)("iframe",{src:e.embedUrl,title:e.title,className:"w-full h-full rounded",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),(0,s.jsx)("h3",{className:"text-white font-medium text-sm mb-2",children:e.title}),(0,s.jsxs)("div",{className:"text-xs text-white/60 space-y-1",children:[(0,s.jsxs)("p",{children:["ID: ",e.id]}),(0,s.jsxs)("p",{children:["Duration: ",e.duration,"s"]}),(0,s.jsxs)("p",{children:["Batch: ",e.batchIndex]})]})]},e.id))})]})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41108:(e,t,r)=>{Promise.resolve().then(r.bind(r,23302))},59007:(e,t,r)=>{"use strict";r.d(t,{CA:()=>c,No:()=>d,ZB:()=>m,iD:()=>h,tx:()=>u});let s={CURRENT_BATCH:"mytube_current_batch",BATCH_PREFIX:"mytube_batch_",VIDEO_INDEX:"mytube_video_index",TOTAL_VIDEOS:"mytube_total_videos",LAST_PROCESSED:"mytube_last_processed"};function o(e){for(let t of[/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,/youtube\.com\/v\/([^&\n?#]+)/]){let r=e.match(t);if(r)return r[1]}return null}function l(e){let t=o(e);return t?`https://www.youtube.com/embed/${t}`:e}function a(e,t){return`Video ${t+1}`}function n(e){let t=function(e){try{let t=localStorage.getItem(`${s.BATCH_PREFIX}${e}`);if(!t)return null;let r=JSON.parse(t);if(Date.now()-r.lastUpdated>864e5)return localStorage.removeItem(`${s.BATCH_PREFIX}${e}`),null;return r}catch(t){return console.error(`Error loading batch ${e}:`,t),null}}(e);return t?t.videos:[]}function i(){return n(parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0"))}function c(){let e=(parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0")+1)%Math.ceil(parseInt(localStorage.getItem(s.TOTAL_VIDEOS)||"0")/100);return localStorage.setItem(s.CURRENT_BATCH,e.toString()),n(e)}function d(){let e=parseInt(localStorage.getItem(s.TOTAL_VIDEOS)||"0"),t=parseInt(localStorage.getItem(s.CURRENT_BATCH)||"0"),r=Math.ceil(e/100),o=n(t);return{totalVideos:e,currentBatch:t,totalBatches:r,videosInCurrentBatch:o.length}}function h(){Object.keys(localStorage).forEach(e=>{(e.startsWith(s.BATCH_PREFIX)||Object.values(s).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all video storage")}async function u(){try{let e=await fetch("/Mytube.json");if(!e.ok)throw Error(`Failed to load videos: ${e.statusText}`);let t=await e.json();console.log("Raw video data loaded:",Object.keys(t).length,"entries");let r=[];return Array.isArray(t)?t.forEach((e,t)=>{Object.entries(e).forEach(([e,t])=>{let s=o(t);s&&r.push({id:`video_${r.length}_${s}`,title:a(t,r.length),url:t,embedUrl:l(t),duration:300,category:"General",batchIndex:Math.floor(r.length/100)})})}):Object.entries(t).forEach(([e,t],s)=>{let n=o(t);n&&r.push({id:`video_${r.length}_${n}`,title:a(t,r.length),url:t,embedUrl:l(t),duration:300,category:"General",batchIndex:Math.floor(r.length/100)})}),r}catch(e){throw console.error("Error loading videos from file:",e),e}}async function m(){try{if(!function(){let e=localStorage.getItem(s.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached video data..."),i();{console.log("Loading fresh video data...");let e=await u();return!function(e){let t=Math.ceil(e.length/100);for(let o=0;o<t;o++){let t=100*o,l=Math.min(t+100,e.length),a=e.slice(t,l);var r=o;try{let e={batchNumber:r,videos:a,totalVideos:a.length,lastUpdated:Date.now()};localStorage.setItem(`${s.BATCH_PREFIX}${r}`,JSON.stringify(e))}catch(e){console.error(`Error saving batch ${r}:`,e)}}localStorage.setItem(s.TOTAL_VIDEOS,e.length.toString()),localStorage.setItem(s.CURRENT_BATCH,"0"),localStorage.setItem(s.LAST_PROCESSED,Date.now().toString()),console.log(`Saved ${e.length} videos in ${t} batches`)}(e),i()}}catch(t){console.error("Error initializing video system:",t);let e=i();if(e.length>0)return console.log("Using cached videos as fallback"),e;throw t}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},88372:(e,t,r)=>{Promise.resolve().then(r.bind(r,21320))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,8441],()=>r(20845));module.exports=s})();