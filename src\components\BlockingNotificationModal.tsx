'use client'

import { useState, useEffect } from 'react'
import { getUnreadNotifications, markNotificationAsRead, Notification } from '@/lib/dataService'

interface BlockingNotificationModalProps {
  userId: string
  onAllRead: () => void
}

export default function BlockingNotificationModal({ userId, onAllRead }: BlockingNotificationModalProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (userId) {
      loadBlockingNotifications()
    }
  }, [userId])

  const loadBlockingNotifications = async () => {
    try {
      setLoading(true)
      const unreadNotifications = await getUnreadNotifications(userId)
      setNotifications(unreadNotifications)

      if (unreadNotifications.length === 0) {
        onAllRead()
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
      onAllRead() // Allow user to continue if there's an error
    } finally {
      setLoading(false)
    }
  }

  const handleAcknowledge = async () => {
    const currentNotification = notifications[currentIndex]
    
    if (currentNotification?.id) {
      // Mark current notification as read
      await markNotificationAsRead(currentNotification.id, userId)
      
      // Move to next notification or close modal
      if (currentIndex < notifications.length - 1) {
        setCurrentIndex(currentIndex + 1)
      } else {
        // All notifications have been read
        onAllRead()
      }
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return 'fas fa-check-circle text-green-500'
      case 'warning':
        return 'fas fa-exclamation-triangle text-yellow-500'
      case 'error':
        return 'fas fa-times-circle text-red-500'
      default:
        return 'fas fa-info-circle text-blue-500'
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
    
    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`
    return `${Math.floor(diffInSeconds / 86400)} days ago`
  }

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="spinner w-8 h-8 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading notifications...</p>
          </div>
        </div>
      </div>
    )
  }

  if (notifications.length === 0) {
    return null
  }

  const currentNotification = notifications[currentIndex]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <i className={getNotificationIcon(currentNotification.type)}></i>
              <div>
                <h3 className="text-lg font-bold">Important Notice</h3>
                <p className="text-blue-100 text-sm">
                  {currentIndex + 1} of {notifications.length} notifications
                </p>
              </div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-full px-3 py-1">
              <span className="text-sm font-medium">Required</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <h4 className="text-xl font-bold text-gray-900 mb-3">
            {currentNotification.title}
          </h4>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <p className="text-gray-800 leading-relaxed">
              {currentNotification.message}
            </p>
          </div>
          
          <div className="flex items-center justify-between text-sm text-gray-500 mb-6">
            <span>From: {currentNotification.createdBy}</span>
            <span>{formatTimeAgo(currentNotification.createdAt)}</span>
          </div>

          {/* Progress indicator */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{currentIndex + 1}/{notifications.length}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentIndex + 1) / notifications.length) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Action Button */}
          <button
            onClick={handleAcknowledge}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <i className="fas fa-check"></i>
            <span>
              {currentIndex < notifications.length - 1 ? 'Acknowledge & Continue' : 'Acknowledge & Proceed'}
            </span>
          </button>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t">
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
            <i className="fas fa-info-circle"></i>
            <span>You must acknowledge all notifications to continue</span>
          </div>
        </div>
      </div>
    </div>
  )
}
