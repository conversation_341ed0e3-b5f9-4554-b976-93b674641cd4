[debug] [2025-06-25T14:35:07.717Z] ----------------------------------------------------------------------
[debug] [2025-06-25T14:35:07.722Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only hosting --project mytube-india
[debug] [2025-06-25T14:35:07.723Z] CLI Version:   14.8.0
[debug] [2025-06-25T14:35:07.723Z] Platform:      win32
[debug] [2025-06-25T14:35:07.723Z] Node Version:  v22.14.0
[debug] [2025-06-25T14:35:07.726Z] Time:          Wed Jun 25 2025 20:05:07 GMT+0530 (India Standard Time)
[debug] [2025-06-25T14:35:07.727Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-25T14:35:08.245Z] Field "/functions/runtime" in "firebase.json" is possibly invalid: must be equal to one of the allowed values
[debug] [2025-06-25T14:35:08.246Z] Field "/functions" in "firebase.json" is possibly invalid: must be array
[debug] [2025-06-25T14:35:08.246Z] Field "/functions" in "firebase.json" is possibly invalid: must match a schema in anyOf
[debug] [2025-06-25T14:35:08.254Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-25T14:35:08.255Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-25T14:35:08.255Z] [iam] checking project mytube-india for permissions ["firebase.projects.get","firebasehosting.sites.update"]
[debug] [2025-06-25T14:35:08.257Z] Checked if tokens are valid: false, expires at: 1750862640062
[debug] [2025-06-25T14:35:08.258Z] Checked if tokens are valid: false, expires at: 1750862640062
[debug] [2025-06-25T14:35:08.258Z] > refreshing access token with scopes: []
[debug] [2025-06-25T14:35:08.261Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-25T14:35:08.261Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-25T14:35:08.622Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-25T14:35:08.623Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-25T14:35:08.641Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/mytube-india:testIamPermissions [none]
[debug] [2025-06-25T14:35:08.641Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/mytube-india:testIamPermissions x-goog-quota-user=projects/mytube-india
[debug] [2025-06-25T14:35:08.641Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/mytube-india:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-06-25T14:35:09.928Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/mytube-india:testIamPermissions 200
[debug] [2025-06-25T14:35:09.929Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/mytube-india:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-06-25T14:35:09.930Z] Checked if tokens are valid: true, expires at: 1750865707623
[debug] [2025-06-25T14:35:09.930Z] Checked if tokens are valid: true, expires at: 1750865707623
[debug] [2025-06-25T14:35:09.931Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/mytube-india [none]
[debug] [2025-06-25T14:35:10.743Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/mytube-india 200
[debug] [2025-06-25T14:35:10.743Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/mytube-india {"projectId":"mytube-india","projectNumber":"************","displayName":"Mytube-india","name":"projects/mytube-india","resources":{"hostingSite":"mytube-india"},"state":"ACTIVE","etag":"1_3f3243ec-dcca-4360-8124-ec6a1aa7c74a"}
[info] 
[info] === Deploying to 'mytube-india'...
[info] 
[info] i  deploying hosting 
[debug] [2025-06-25T14:35:10.769Z] Checked if tokens are valid: true, expires at: 1750865707623
[debug] [2025-06-25T14:35:10.769Z] Checked if tokens are valid: true, expires at: 1750865707623
[debug] [2025-06-25T14:35:10.770Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/mytube-india/versions [none]
[debug] [2025-06-25T14:35:10.770Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/mytube-india/versions {"status":"CREATED","labels":{"deployment-tool":"cli-firebase"}}
