'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { useRequireAuth } from '@/hooks/useAuth'
import { useBlockingNotifications } from '@/hooks/useBlockingNotifications'
import { useLeaveMonitor } from '@/hooks/useLeaveMonitor'
import { getVideoCountData, updateVideoCount, addTransaction, updateWalletBalance, getUserVideoSettings, getUserData, isUserPlanExpired, updateUserActiveDays } from '@/lib/dataService'
import { optimizedService } from '@/lib/optimizedDataService'
import { restoreUserSessionData, isolateUserSession, secureLocalStorageGet, secureLocalStorageSet, validateUserSession } from '@/lib/authUtils'
import {
  VideoData,
  initializeVideoSystem,
  moveToNextBatch,
  getVideoStats
} from '@/lib/videoManager'
import { isWorkBlocked } from '@/lib/leaveService'
import BlockingNotificationModal from '@/components/BlockingNotificationModal'
import Swal from 'sweetalert2'

export default function WorkPage() {
  const { user, loading } = useRequireAuth()
  const { hasBlockingNotifications, isChecking, markAllAsRead } = useBlockingNotifications(user?.uid || null)
  const { isBlocked: isLeaveBlocked, leaveStatus, checkLeaveStatus } = useLeaveMonitor({
    userId: user?.uid || null,
    checkInterval: 30000, // Check every 30 seconds
    enabled: !!user
  })
  const [videoData, setVideoData] = useState<VideoData | null>(null)
  const [todayVideos, setTodayVideos] = useState(0)
  const [totalVideosWatched, setTotalVideosWatched] = useState(0)
  const [remainingVideos, setRemainingVideos] = useState(50)
  const [isWatching, setIsWatching] = useState(false)
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [canSubmit, setCanSubmit] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [canNext, setCanNext] = useState(false)
  const [localVideoCount, setLocalVideoCount] = useState(0)
  const [localWatchTimes, setLocalWatchTimes] = useState<Date[]>([])
  const [dailyWatchTimes, setDailyWatchTimes] = useState<Date[]>([])
  const [sessionStarted, setSessionStarted] = useState(false)
  const [videos, setVideos] = useState<VideoData[]>([])
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0)
  const [isLoadingVideos, setIsLoadingVideos] = useState(true)
  const [videoStats, setVideoStats] = useState({
    totalVideos: 0,
    currentBatch: 0,
    totalBatches: 0,
    videosInCurrentBatch: 0
  })
  const [videoSettings, setVideoSettings] = useState({
    videoDuration: 300,
    earningPerBatch: 10,
    plan: 'Trial',
    hasQuickAdvantage: false,
    quickAdvantageExpiry: null
  })
  const [userData, setUserData] = useState<any>(null)
  const [daysLeft, setDaysLeft] = useState(0)
  const [activeDays, setActiveDays] = useState(0)
  const [isPageVisible, setIsPageVisible] = useState(true)
  const [isPaused, setIsPaused] = useState(false)
  const [pausedTime, setPausedTime] = useState(0)

  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const videoRef = useRef<HTMLIFrameElement>(null)

  useEffect(() => {
    // Trigger daily process check on work page load
    const triggerDailyProcess = async () => {
      try {
        const { checkAndRunDailyProcess } = await import('@/lib/dataService')
        await checkAndRunDailyProcess()
      } catch (error) {
        console.error('Daily process trigger failed:', error)
      }
    }
    triggerDailyProcess()

    if (user) {
      checkWorkAccess()
    }
  }, [user])

  // Reload video data when component mounts to ensure fresh daily counts
  useEffect(() => {
    if (user && sessionStarted) {
      loadVideoData()
    }
  }, [user, sessionStarted])

  // Monitor leave status changes and block work if needed
  useEffect(() => {
    console.log('🔍 Work page leave status check:', {
      isLeaveBlocked,
      leaveStatus,
      user: user?.uid
    })

    if (isLeaveBlocked && leaveStatus.reason) {
      console.log('🚫 Work blocked due to leave:', leaveStatus.reason)

      // Stop any ongoing work
      if (isWatching) {
        stopWatching()
      }

      // Show leave notification and redirect
      Swal.fire({
        icon: 'warning',
        title: 'Work Suspended',
        text: leaveStatus.reason,
        confirmButtonText: 'Go to Dashboard',
        allowOutsideClick: false,
        allowEscapeKey: false
      }).then(() => {
        window.location.href = '/dashboard'
      })
    }
  }, [isLeaveBlocked, leaveStatus, isWatching])

  const checkWorkAccess = async () => {
    try {
      console.log('🔍 Checking work access for user:', user!.uid)

      // Check plan expiry first
      const planStatus = await isUserPlanExpired(user!.uid)
      console.log('📅 Plan status result:', planStatus)

      if (planStatus.expired) {
        console.log('🚫 Work access blocked - Plan expired:', planStatus.reason)
        Swal.fire({
          icon: 'error',
          title: 'Plan Expired',
          html: `
            <div class="text-center">
              <p class="mb-3">${planStatus.reason}</p>
              <p class="text-sm text-gray-600">
                Active Days: ${planStatus.activeDays || 0} | Days Left: ${planStatus.daysLeft || 0}
              </p>
            </div>
          `,
          confirmButtonText: 'Upgrade Plan',
          showCancelButton: true,
          cancelButtonText: 'Go to Dashboard'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = '/plans'
          } else {
            window.location.href = '/dashboard'
          }
        })
        return
      }

      // Check if user has already completed their daily session (50 videos)
      const videoData = await getVideoCountData(user!.uid)
      console.log('📊 Video data check:', videoData)

      if (videoData.todayVideos >= 50) {
        console.log('🚫 Work access blocked - Daily session completed')
        Swal.fire({
          icon: 'info',
          title: 'Daily Session Completed',
          html: `
            <div class="text-center">
              <p class="mb-3">You have already completed your daily session of 50 videos!</p>
              <p class="text-sm text-gray-600">
                Videos completed today: ${videoData.todayVideos}/50
              </p>
              <p class="text-sm text-green-600 mt-2">
                Come back tomorrow for your next session.
              </p>
            </div>
          `,
          confirmButtonText: 'Go to Dashboard',
          allowOutsideClick: false,
          allowEscapeKey: false
        }).then(() => {
          window.location.href = '/dashboard'
        })
        return
      }

      // Debug admin leave status
      const { debugAdminLeaveStatus } = await import('@/lib/leaveService')
      await debugAdminLeaveStatus()

      const workStatus = await isWorkBlocked(user!.uid)
      console.log('📊 Work status result:', workStatus)

      if (workStatus.blocked) {
        console.log('🚫 Work access blocked:', workStatus.reason)
        Swal.fire({
          icon: 'warning',
          title: 'Work Not Available',
          text: workStatus.reason || 'Work is currently blocked.',
          confirmButtonText: 'Go to Dashboard'
        }).then(() => {
          window.location.href = '/dashboard'
        })
        return
      }

      console.log('✅ Work access allowed, proceeding with normal loading')
      // If work is not blocked, proceed with normal loading
      loadVideoData()
      loadVideoSettings()
      loadUserData()
      initializeVideos()
      initializeSession()
    } catch (error) {
      console.error('❌ Error checking work access (allowing work to proceed):', error)
      // On error, allow work to proceed
      loadVideoData()
      loadVideoSettings()
      loadUserData()
      initializeVideos()
      initializeSession()
    }
  }

  useEffect(() => {
    // Check if user can submit (completed 50 videos locally)
    setCanSubmit(localVideoCount >= 50)
  }, [localVideoCount])

  useEffect(() => {
    // Prevent user from leaving the page while watching
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isWatching) {
        e.preventDefault()
        return ''
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [isWatching])

  // Page visibility detection - pause timer when user leaves page/tab
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden
      setIsPageVisible(isVisible)

      if (isWatching) {
        if (!isVisible) {
          // Page became hidden - pause timer
          setIsPaused(true)
          setPausedTime(timeRemaining)
          if (timerRef.current) {
            clearInterval(timerRef.current)
            timerRef.current = null
          }

          // Show warning message
          console.log('⚠️ Timer paused - User left the page/tab')
        } else if (isPaused) {
          // Page became visible again - resume timer
          setIsPaused(false)
          setTimeRemaining(pausedTime)

          // Restart timer from paused time
          timerRef.current = setInterval(() => {
            setTimeRemaining((prev) => {
              if (prev <= 1) {
                setCanNext(true)
                if (timerRef.current) {
                  clearInterval(timerRef.current)
                }
                return 0
              }
              return prev - 1
            })
          }, 1000)

          console.log('✅ Timer resumed - User returned to page/tab')
        }
      }
    }

    const handleFocus = () => {
      if (isWatching && isPaused) {
        handleVisibilityChange()
      }
    }

    const handleBlur = () => {
      if (isWatching && !isPaused) {
        handleVisibilityChange()
      }
    }

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)
    window.addEventListener('blur', handleBlur)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('blur', handleBlur)
    }
  }, [isWatching, isPaused, timeRemaining, pausedTime])

  const loadVideoData = async () => {
    try {
      console.log('📊 Loading video data for user:', user!.uid)
      const data = await getVideoCountData(user!.uid)
      console.log('📊 Video data loaded:', data)
      setTodayVideos(data.todayVideos)
      setTotalVideosWatched(data.totalVideos)
      // Don't update remainingVideos here - it should be based on localVideoCount
      // setRemainingVideos will be updated when session data is loaded
    } catch (error) {
      console.error('Error loading video data:', error)
    }
  }

  const loadVideoSettings = async () => {
    try {
      const settings = await getUserVideoSettings(user!.uid)
      setVideoSettings({
        videoDuration: settings.videoDuration,
        earningPerBatch: settings.earningPerBatch,
        plan: settings.plan,
        hasQuickAdvantage: settings.hasQuickAdvantage || false,
        quickAdvantageExpiry: settings.quickAdvantageExpiry || null
      })
    } catch (error) {
      console.error('Error loading video settings:', error)
    }
  }

  const loadUserData = async () => {
    try {
      const data = await getUserData(user!.uid)
      setUserData(data)

      // Update active days to ensure accurate tracking
      if (data) {
        try {
          await updateUserActiveDays(user!.uid)

          // Reload user data to get updated active days
          const updatedUserData = await getUserData(user!.uid)
          setUserData(updatedUserData)
        } catch (error) {
          console.error('Error updating active days:', error)
        }

        // Get accurate plan status using the new expiry system
        const planStatus = await isUserPlanExpired(user!.uid)
        setDaysLeft(planStatus.daysLeft || 0)

        // Get live active days for display
        const { getLiveActiveDays } = await import('@/lib/dataService')
        const liveActiveDays = await getLiveActiveDays(user!.uid)
        setActiveDays(liveActiveDays)

        console.log('📊 Plan status loaded:', {
          plan: data.plan,
          expired: planStatus.expired,
          daysLeft: planStatus.daysLeft,
          activeDays: planStatus.activeDays,
          reason: planStatus.reason
        })
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    }
  }

  const initializeSession = () => {
    // Isolate user session to prevent cross-user data contamination
    isolateUserSession(user!.uid)

    // Try to restore session data from backup (in case of auto-logout)
    const restored = restoreUserSessionData(user!.uid)
    if (restored) {
      console.log('🔄 Session data restored after auto-logout')
      // Show restoration message
      setTimeout(() => {
        Swal.fire({
          icon: 'info',
          title: 'Session Restored',
          text: 'Your progress has been restored after reconnection.',
          timer: 3000,
          showConfirmButton: false
        })
      }, 1000)
    }

    // Initialize local session data with security validation
    const today = new Date().toDateString()
    const sessionKey = `video_session_${user!.uid}_${today}`
    const watchTimesKey = `watch_times_${user!.uid}_${today}`
    const dailyWatchTimesKey = `daily_watch_times_${user!.uid}_${today}`

    // Use secure localStorage operations
    const savedCount = secureLocalStorageGet(sessionKey, user!.uid)
    const savedTimes = secureLocalStorageGet(watchTimesKey, user!.uid)
    const savedDailyTimes = secureLocalStorageGet(dailyWatchTimesKey, user!.uid)

    if (savedCount) {
      const count = parseInt(savedCount)
      setLocalVideoCount(count)
      setRemainingVideos(Math.max(0, 50 - count))

      if (restored && count > 0) {
        console.log(`📊 Restored session: ${count}/50 videos completed`)
      }
    } else {
      // No saved session, start fresh
      setRemainingVideos(50)
    }

    if (savedTimes) {
      try {
        const times = JSON.parse(savedTimes).map((time: string) => new Date(time))
        setLocalWatchTimes(times)
      } catch (error) {
        console.error('Error parsing saved watch times:', error)
        setLocalWatchTimes([])
      }
    }

    // Load daily watch times (persistent for the whole day)
    if (savedDailyTimes) {
      try {
        const dailyTimes = JSON.parse(savedDailyTimes).map((time: string) => new Date(time))
        setDailyWatchTimes(dailyTimes)
      } catch (error) {
        console.error('Error parsing saved daily watch times:', error)
        setDailyWatchTimes([])
      }
    }

    setSessionStarted(true)
  }

  const initializeVideos = async () => {
    try {
      setIsLoadingVideos(true)

      // Initialize video system with batching
      const currentBatchVideos = await initializeVideoSystem()

      setVideos(currentBatchVideos)

      // Get or generate a random video index for this session
      const randomVideoIndex = getRandomVideoIndex(currentBatchVideos.length)

      if (currentBatchVideos.length > 0) {
        setVideoData(currentBatchVideos[randomVideoIndex])
        setCurrentVideoIndex(randomVideoIndex)
      }

      // Update video statistics
      const stats = getVideoStats()
      setVideoStats(stats)

      console.log(`Loaded batch ${stats.currentBatch + 1}/${stats.totalBatches} with ${currentBatchVideos.length} videos`)
      console.log(`Starting with video ${randomVideoIndex + 1}/${currentBatchVideos.length}: ${currentBatchVideos[randomVideoIndex]?.title}`)

      // Show video change notification on first load using secure localStorage
      const notificationKey = `video_change_notification_${user!.uid}`
      const hasSeenNotification = secureLocalStorageGet(notificationKey, user!.uid)
      if (!hasSeenNotification) {
        setTimeout(() => {
          Swal.fire({
            icon: 'info',
            title: '🎬 Video Variety Feature',
            html: `
              <div class="text-left">
                <p class="mb-2">🔄 <strong>Refresh to change videos!</strong></p>
                <p class="mb-2">• Each refresh loads a different video</p>
                <p class="mb-2">• Click "Change Video" button anytime</p>
                <p>• Enjoy variety while earning!</p>
              </div>
            `,
            confirmButtonText: 'Got it!',
            timer: 8000,
            timerProgressBar: true
          })
          secureLocalStorageSet(notificationKey, 'shown', user!.uid)
        }, 2000)
      }

    } catch (error) {
      console.error('Error initializing videos:', error)

      // Fallback to default videos with randomization
      const defaultVideos: VideoData[] = [
        {
          id: '1',
          title: 'Sample Video 1',
          url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          embedUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
          duration: 300
        },
        {
          id: '2',
          title: 'Sample Video 2',
          url: 'https://www.youtube.com/watch?v=9bZkp7q19f0',
          embedUrl: 'https://www.youtube.com/embed/9bZkp7q19f0',
          duration: 300
        },
        {
          id: '3',
          title: 'Sample Video 3',
          url: 'https://www.youtube.com/watch?v=L_jWHffIx5E',
          embedUrl: 'https://www.youtube.com/embed/L_jWHffIx5E',
          duration: 300
        },
        {
          id: '4',
          title: 'Sample Video 4',
          url: 'https://www.youtube.com/watch?v=fJ9rUzIMcZQ',
          embedUrl: 'https://www.youtube.com/embed/fJ9rUzIMcZQ',
          duration: 300
        },
        {
          id: '5',
          title: 'Sample Video 5',
          url: 'https://www.youtube.com/watch?v=ZZ5LpwO-An4',
          embedUrl: 'https://www.youtube.com/embed/ZZ5LpwO-An4',
          duration: 300
        }
      ]

      const randomIndex = getRandomVideoIndex(defaultVideos.length)
      setVideos(defaultVideos)
      setVideoData(defaultVideos[randomIndex])
      setCurrentVideoIndex(randomIndex)

      Swal.fire({
        icon: 'warning',
        title: 'Video Loading Issue',
        text: 'Using sample videos. Please check your internet connection.',
        timer: 3000,
        showConfirmButton: false
      })
    } finally {
      setIsLoadingVideos(false)
    }
  }

  const getRandomVideoIndex = (totalVideos: number): number => {
    if (totalVideos <= 1) return 0

    // Generate a session-based random index that changes on page refresh
    const sessionId = Date.now() + Math.random()
    const today = new Date().toDateString()
    const refreshKey = `video_refresh_${user!.uid}_${today}`

    // Get or create a refresh counter for today using secure localStorage
    let refreshCount = parseInt(secureLocalStorageGet(refreshKey, user!.uid) || '0')
    refreshCount += 1
    secureLocalStorageSet(refreshKey, refreshCount.toString(), user!.uid)

    // Use refresh count and session ID to generate pseudo-random index
    const seed = refreshCount * sessionId
    const randomIndex = Math.floor(seed % totalVideos)

    console.log(`Refresh #${refreshCount} - Selected video index: ${randomIndex}`)

    return randomIndex
  }

  const startWatching = async () => {
    // Prevent multiple clicks
    if (isWatching) return

    // Check if plan is expired based on active days
    try {
      const { isUserPlanExpired } = await import('@/lib/dataService')
      const planStatus = await isUserPlanExpired(user!.uid)

      if (planStatus.expired) {
        Swal.fire({
          icon: 'error',
          title: 'Plan Expired',
          html: `
            <div class="text-center">
              <p class="mb-2">Your plan has expired and you cannot watch videos.</p>
              <p class="text-sm text-gray-600 mb-2">${planStatus.reason}</p>
              <p class="text-sm text-blue-600">Please upgrade your plan to continue earning.</p>
            </div>
          `,
          confirmButtonText: 'Go to Plans',
          showCancelButton: true,
          cancelButtonText: 'Go to Dashboard'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = '/plans'
          } else {
            window.location.href = '/dashboard'
          }
        })
        return
      }
    } catch (error) {
      console.error('Error checking plan expiry:', error)
    }

    // Check if work is blocked due to leave
    if (isLeaveBlocked) {
      Swal.fire({
        icon: 'warning',
        title: 'Work Not Available',
        text: leaveStatus.reason || 'Work is currently blocked due to leave.',
        confirmButtonText: 'Go to Dashboard'
      }).then(() => {
        window.location.href = '/dashboard'
      })
      return
    }

    // Check both local remaining videos and database today videos
    if (remainingVideos <= 0 || todayVideos >= 50) {
      Swal.fire({
        icon: 'warning',
        title: 'Daily Session Completed',
        html: `
          <div class="text-center">
            <p class="mb-2">You have completed your daily session of 50 videos!</p>
            <p class="text-sm text-gray-600">Videos completed today: ${todayVideos}/50</p>
            <p class="text-sm text-green-600 mt-2">Come back tomorrow for your next session.</p>
          </div>
        `,
        confirmButtonText: 'Go to Dashboard'
      }).then(() => {
        window.location.href = '/dashboard'
      })
      return
    }

    setIsWatching(true)
    setTimeRemaining(videoSettings.videoDuration) // Use dynamic duration
    setCanNext(false)
    setIsPaused(false) // Reset pause state
    setPausedTime(0) // Reset paused time

    // Auto-play video by updating iframe src with autoplay parameter
    if (videoRef.current && videoData) {
      const autoplayUrl = `${videoData.embedUrl}?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1`
      videoRef.current.src = autoplayUrl
    }

    // Only start timer if page is visible
    if (isPageVisible) {
      timerRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            setCanNext(true)
            if (timerRef.current) {
              clearInterval(timerRef.current)
            }
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } else {
      // If page is not visible, immediately pause
      setIsPaused(true)
      setPausedTime(videoSettings.videoDuration)
    }
  }

  const stopWatching = () => {
    setIsWatching(false)
    setTimeRemaining(0)
    setCanNext(false)
    setIsPaused(false) // Reset pause state
    setPausedTime(0) // Reset paused time

    // Reset video to original URL (stops autoplay)
    if (videoRef.current && videoData) {
      videoRef.current.src = videoData.embedUrl
    }

    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
  }

  const nextVideo = () => {
    // Prevent multiple clicks
    if (!canNext || isSubmitting) return

    // Check if work is blocked due to leave
    if (isLeaveBlocked) {
      stopWatching()
      Swal.fire({
        icon: 'warning',
        title: 'Work Suspended',
        text: leaveStatus.reason || 'Work has been suspended due to leave.',
        confirmButtonText: 'Go to Dashboard'
      }).then(() => {
        window.location.href = '/dashboard'
      })
      return
    }

    // Save local count and time
    const newLocalCount = localVideoCount + 1
    const newWatchTime = new Date()
    const updatedWatchTimes = [...localWatchTimes, newWatchTime]
    const updatedDailyWatchTimes = [...dailyWatchTimes, newWatchTime]

    setLocalVideoCount(newLocalCount)
    setLocalWatchTimes(updatedWatchTimes)
    setDailyWatchTimes(updatedDailyWatchTimes)

    // Update remaining videos count
    setRemainingVideos(Math.max(0, 50 - newLocalCount))

    // Save to localStorage using secure operations
    const today = new Date().toDateString()
    const sessionKey = `video_session_${user!.uid}_${today}`
    const watchTimesKey = `watch_times_${user!.uid}_${today}`
    const dailyWatchTimesKey = `daily_watch_times_${user!.uid}_${today}`

    // Use secure localStorage operations with user validation
    secureLocalStorageSet(sessionKey, newLocalCount.toString(), user!.uid)
    secureLocalStorageSet(watchTimesKey, JSON.stringify(updatedWatchTimes.map(time => time.toISOString())), user!.uid)
    secureLocalStorageSet(dailyWatchTimesKey, JSON.stringify(updatedDailyWatchTimes.map(time => time.toISOString())), user!.uid)

    // Move to next video with some randomization
    let nextIndex = currentVideoIndex + 1

    if (nextIndex >= videos.length) {
      // Load next batch
      try {
        const nextBatchVideos = moveToNextBatch()
        setVideos(nextBatchVideos)

        // Start with a random video in the new batch
        const randomIndex = Math.floor(Math.random() * nextBatchVideos.length)
        setCurrentVideoIndex(randomIndex)
        setVideoData(nextBatchVideos[randomIndex])

        // Update stats
        const stats = getVideoStats()
        setVideoStats(stats)

        Swal.fire({
          icon: 'info',
          title: 'New Video Batch Loaded',
          text: `Video ${newLocalCount}/50 completed. Batch ${stats.currentBatch + 1}/${stats.totalBatches} loaded.`,
          timer: 2000,
          showConfirmButton: false
        })
      } catch (error) {
        console.error('Error loading next batch:', error)
        // Reset to a random video in current batch
        const randomIndex = Math.floor(Math.random() * videos.length)
        setCurrentVideoIndex(randomIndex)
        setVideoData(videos[randomIndex])
      }
    } else {
      // Occasionally skip to a random video instead of sequential
      if (Math.random() < 0.3 && videos.length > 3) { // 30% chance to randomize
        const availableIndices = videos.map((_, index) => index).filter(index => index !== currentVideoIndex)
        nextIndex = availableIndices[Math.floor(Math.random() * availableIndices.length)]
        console.log(`Randomized next video: ${nextIndex} (was going to ${currentVideoIndex + 1})`)
      }

      setCurrentVideoIndex(nextIndex)
      setVideoData(videos[nextIndex])
    }

    // Reset watching state
    stopWatching()

    // Show progress message
    if (newLocalCount < 50) {
      Swal.fire({
        icon: 'success',
        title: 'Video Completed!',
        text: `Progress: ${newLocalCount}/50 videos watched. ${50 - newLocalCount} more to go!`,
        timer: 2000,
        showConfirmButton: false
      })
    } else {
      Swal.fire({
        icon: 'success',
        title: '🎉 All Videos Completed!',
        text: 'You have watched all 50 videos! Click "Submit & Earn" to get your rewards.',
        timer: 3000,
        showConfirmButton: false
      })
    }
  }

  const submitVideo = async () => {
    // Prevent multiple submissions with strict validation
    if (isSubmitting) {
      console.log('⚠️ Submission already in progress, ignoring click')
      return
    }

    if (!canSubmit || localVideoCount < 50) {
      console.log('⚠️ Cannot submit: canSubmit =', canSubmit, 'localVideoCount =', localVideoCount)
      Swal.fire({
        icon: 'warning',
        title: 'Cannot Submit',
        text: `You need to complete exactly 50 videos to submit. Current: ${localVideoCount}/50`,
        confirmButtonText: 'Continue Watching'
      })
      return
    }

    // Check if already submitted today (double-check against database)
    try {
      const currentVideoData = await getVideoCountData(user!.uid)
      if (currentVideoData.todayVideos >= 50) {
        console.log('⚠️ User already submitted 50 videos today')
        Swal.fire({
          icon: 'info',
          title: 'Already Submitted',
          text: 'You have already submitted your daily videos. Come back tomorrow!',
          confirmButtonText: 'Go to Dashboard'
        }).then(() => {
          window.location.href = '/dashboard'
        })
        return
      }
    } catch (error) {
      console.error('Error checking current video data:', error)
    }

    // Check if plan is expired based on active days
    try {
      const { isUserPlanExpired } = await import('@/lib/dataService')
      const planStatus = await isUserPlanExpired(user!.uid)

      if (planStatus.expired) {
        Swal.fire({
          icon: 'error',
          title: 'Plan Expired',
          html: `
            <div class="text-center">
              <p class="mb-2">Your plan has expired and you cannot submit videos.</p>
              <p class="text-sm text-gray-600 mb-2">${planStatus.reason}</p>
              <p class="text-sm text-blue-600">Please upgrade your plan to continue earning.</p>
            </div>
          `,
          confirmButtonText: 'Go to Plans',
          showCancelButton: true,
          cancelButtonText: 'Go to Dashboard'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = '/plans'
          } else {
            window.location.href = '/dashboard'
          }
        })
        return
      }
    } catch (error) {
      console.error('Error checking plan expiry:', error)
    }

    // Check if work is blocked due to leave
    if (isLeaveBlocked) {
      Swal.fire({
        icon: 'warning',
        title: 'Submission Not Available',
        text: leaveStatus.reason || 'Video submission is not available due to leave.',
        confirmButtonText: 'Go to Dashboard'
      }).then(() => {
        window.location.href = '/dashboard'
      })
      return
    }

    // Show confirmation dialog before submission
    const confirmResult = await Swal.fire({
      title: 'Submit Videos & Earn?',
      html: `
        <div class="text-left">
          <p><strong>Videos Completed:</strong> ${localVideoCount}/50</p>
          <p><strong>Earning Amount:</strong> ₹${videoSettings.earningPerBatch}</p>
          <p class="text-sm text-gray-600 mt-2">This action cannot be undone. You can only submit once per day.</p>
        </div>
      `,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Submit & Earn!',
      cancelButtonText: 'Cancel',
      allowOutsideClick: false,
      allowEscapeKey: false
    })

    if (!confirmResult.isConfirmed) {
      console.log('📝 User cancelled submission')
      return
    }

    try {
      setIsSubmitting(true)
      console.log('🚀 Starting video submission for 50 videos...')

      // Final validation before database updates
      if (localVideoCount !== 50) {
        throw new Error(`Invalid video count: ${localVideoCount}. Expected exactly 50 videos.`)
      }

      // Calculate earning for the batch of 50 videos
      const batchEarningAmount = videoSettings.earningPerBatch
      console.log(`💰 Batch earning amount: ₹${batchEarningAmount}`)

      // Check one more time if user already submitted today (race condition protection)
      const finalVideoCheck = await getVideoCountData(user!.uid)
      if (finalVideoCheck.todayVideos >= 50) {
        throw new Error('Videos already submitted today. Cannot submit again.')
      }

      // Use optimized video batch submission (atomic operation)
      let batchResult: any = null
      try {
        console.log('🚀 Submitting batch via optimized function...')
        batchResult = await optimizedService.submitVideoBatch(user!.uid, 50)
        console.log('✅ Batch submitted via optimized function:', batchResult)
      } catch (optimizedError) {
        console.warn('⚠️ Optimized submission failed, using fallback:', optimizedError)

        // Fallback to original method
        console.log('📊 Submitting batch of 50 videos to database...')
        const { submitBatchVideos } = await import('@/lib/dataService')
        batchResult = await submitBatchVideos(user!.uid, 50)
        console.log('✅ Batch submission result:', batchResult)

        console.log('💰 Adding earnings to wallet...')
        await updateWalletBalance(user!.uid, batchEarningAmount)

        console.log('📝 Adding transaction record...')
        await addTransaction(user!.uid, {
          type: 'video_earning',
          amount: batchEarningAmount,
          description: `Batch completion reward - 50 videos watched`
        })
      }

      // Update local state with actual database values from batch submission
      if (batchResult) {
        setTodayVideos(batchResult.todayVideos || 50)
        setTotalVideosWatched(batchResult.totalVideos || 0)
      }
      setRemainingVideos(0) // Set to 0 since daily session is complete

      // Clear local session data (but keep daily watch times)
      const today = new Date().toDateString()
      const sessionKey = `video_session_${user!.uid}_${today}`
      const watchTimesKey = `watch_times_${user!.uid}_${today}`

      localStorage.removeItem(sessionKey)
      localStorage.removeItem(watchTimesKey)

      setLocalVideoCount(0)
      setLocalWatchTimes([])
      setCanSubmit(false)
      // Don't reset remainingVideos to 50 - keep it at 0 until next day

      // Note: dailyWatchTimes are preserved for the full day

      // Reset watching state
      stopWatching()

      Swal.fire({
        icon: 'success',
        title: '🎉 Daily Session Completed!',
        html: `
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${batchEarningAmount} Earned!</p>
            <p class="mb-2">50 videos completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
          </div>
        `,
        confirmButtonText: 'Go to Dashboard',
        timer: 6000,
        showConfirmButton: true
      }).then(() => {
        // Redirect to dashboard after completion
        window.location.href = '/dashboard'
      })

    } catch (error) {
      console.error('Error submitting videos:', error)

      // Show detailed error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'

      Swal.fire({
        icon: 'error',
        title: 'Submission Failed',
        html: `
          <div class="text-left">
            <p><strong>Error:</strong> ${errorMessage}</p>
            <p class="text-sm text-gray-600 mt-2">Please try again or contact support if the problem persists.</p>
            <p class="text-xs text-gray-500 mt-2">If you believe this is an error, please screenshot this message.</p>
          </div>
        `,
        confirmButtonText: 'Try Again',
        allowOutsideClick: false
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes} min ago`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`

    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  if (loading || isLoadingVideos || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">
            {loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading videos...'}
          </p>
        </div>
      </div>
    )
  }

  // Show blocking notifications if any exist
  if (hasBlockingNotifications && user) {
    return (
      <BlockingNotificationModal
        userId={user.uid}
        onAllRead={markAllAsRead}
      />
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <Link href="/dashboard" className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </Link>
          <h1 className="text-xl font-bold text-white">Watch Videos & Earn</h1>
          <div className="text-white text-right">
            <p className="text-sm">Plan: {videoSettings.plan}</p>
            <p className="text-sm">₹{videoSettings.earningPerBatch}/batch (50 videos)</p>
          </div>
        </div>

        {/* Video Change Info Banner */}
        <div className="bg-blue-500/20 border border-blue-400/30 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-center text-center">
            <i className="fas fa-sync-alt text-blue-400 mr-2"></i>
            <span className="text-white/90 text-sm">
              Refresh page or click "Change Video" for different content
            </span>
          </div>
        </div>

        {/* Video Statistics Header */}
        <div className="grid grid-cols-5 gap-2 text-center">
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-yellow-400">{daysLeft}</p>
            <p className="text-white/80 text-xs">days left</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-blue-400">{todayVideos}</p>
            <p className="text-white/80 text-xs">Today's Videos</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-green-400">{totalVideosWatched}</p>
            <p className="text-white/80 text-xs">Total Videos</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-purple-400">{remainingVideos}</p>
            <p className="text-white/80 text-xs">Videos Left</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-orange-400">{activeDays}/{videoSettings.plan === 'Trial' ? '2' : '30'}</p>
            <p className="text-white/80 text-xs">Active Days</p>
          </div>
        </div>
      </header>


      {/* Video Player Section */}
      <div className="glass-card p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-white">
            <i className="fas fa-play-circle mr-2"></i>
            Watch Video & Earn
          </h2>
          <div className="flex items-center space-x-2">
            {videoSettings.hasQuickAdvantage && (
              <div className="bg-green-500/20 border border-green-400/30 rounded-lg px-3 py-1">
                <div className="flex items-center text-green-300 text-sm">
                  <i className="fas fa-bolt mr-1"></i>
                  <span className="font-medium">Quick Advantage Active</span>
                </div>
                {videoSettings.quickAdvantageExpiry && (
                  <div className="text-xs text-green-400 mt-1">
                    Until: {new Date(videoSettings.quickAdvantageExpiry).toLocaleDateString()}
                  </div>
                )}
              </div>
            )}
            <button
              onClick={() => window.location.reload()}
              className="glass-button px-3 py-1 text-white text-sm"
              title="Refresh to change video"
            >
              <i className="fas fa-sync-alt mr-1"></i>
              Change Video
            </button>

          </div>
        </div>

        {videoData && (
          <div className={`aspect-video mb-4 video-container ${isWatching ? 'watching' : ''}`}>
            <iframe
              ref={videoRef}
              src={isWatching ? `${videoData.embedUrl}?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1&disablekb=1` : videoData.embedUrl}
              title={videoData.title}
              className="w-full h-full rounded-lg border-0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />

            {/* Glass Overlay to Block Interactions During Watching */}
            {isWatching && (
              <div className="video-protection-overlay rounded-lg">
                {/* Transparent overlay - no text to obstruct video viewing */}
              </div>
            )}

            {/* Video Loading State */}
            {!isWatching && (
              <div className="absolute inset-0 bg-black/30 backdrop-blur-sm rounded-lg flex items-center justify-center">
                <div className="text-center text-white">
                  <i className="fas fa-play-circle text-6xl opacity-60 text-youtube-red"></i>
                </div>
              </div>
            )}
          </div>
        )}



        {/* Timer and Controls */}
        <div className="text-center">
          {!isWatching ? (
            <div className="space-y-4">
              <button
                onClick={startWatching}
                disabled={localVideoCount >= 50 || isWatching || isSubmitting}
                className={`text-lg px-8 py-4 ${
                  localVideoCount >= 50 || isWatching || isSubmitting
                    ? 'btn-disabled cursor-not-allowed opacity-50'
                    : videoSettings.hasQuickAdvantage
                      ? 'btn-success bg-green-500 hover:bg-green-600'
                      : 'btn-primary'
                }`}
              >
                {isWatching ? (
                  <>
                    <div className="spinner mr-2 w-5 h-5"></div>
                    Starting Video...
                  </>
                ) : (
                  <>
                    <i className={`mr-2 ${videoSettings.hasQuickAdvantage ? 'fas fa-bolt' : 'fas fa-play'}`}></i>
                    {videoSettings.hasQuickAdvantage ? 'Quick Watch' : 'Start Watching'} ({formatTime(videoSettings.videoDuration)})
                  </>
                )}
              </button>

              {/* Submit button for when 50 videos are completed */}
              {canSubmit && localVideoCount >= 50 && (
                <button
                  onClick={submitVideo}
                  disabled={isSubmitting || localVideoCount !== 50 || todayVideos >= 50}
                  className={`text-lg px-8 py-4 transition-all duration-200 ${
                    isSubmitting || localVideoCount !== 50 || todayVideos >= 50
                      ? 'btn-disabled cursor-not-allowed opacity-50 bg-gray-500'
                      : 'btn-success bg-green-500 hover:bg-green-600 active:bg-green-700 transform hover:scale-105 active:scale-95'
                  }`}
                  style={{ pointerEvents: isSubmitting ? 'none' : 'auto' }}
                >
                  {isSubmitting ? (
                    <>
                      <div className="spinner mr-2 w-5 h-5"></div>
                      Submitting All Videos...
                    </>
                  ) : localVideoCount !== 50 ? (
                    <>
                      <i className="fas fa-exclamation-triangle mr-2"></i>
                      Need {50 - localVideoCount} More Videos
                    </>
                  ) : todayVideos >= 50 ? (
                    <>
                      <i className="fas fa-check-circle mr-2"></i>
                      Already Submitted Today
                    </>
                  ) : (
                    <>
                      <i className="fas fa-trophy mr-2"></i>
                      Submit & Earn ₹{videoSettings.earningPerBatch}
                    </>
                  )}
                </button>
              )}

              {/* Progress indicator */}
              {localVideoCount > 0 && localVideoCount < 50 && (
                <div className="mt-4 bg-blue-500/20 border border-blue-400/30 rounded-lg p-3">
                  <div className="flex items-center justify-center text-center">
                    <i className="fas fa-info-circle text-blue-400 mr-2"></i>
                    <span className="text-blue-300 text-sm">
                      Progress: {localVideoCount}/50 videos completed ({50 - localVideoCount} remaining)
                    </span>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-3xl font-bold text-white">
                {formatTime(timeRemaining)}
              </div>

              {/* Pause indicator */}
              {isPaused && (
                <div className="bg-red-500/20 border border-red-400/30 rounded-lg p-3 mb-4">
                  <div className="flex items-center justify-center text-center">
                    <i className="fas fa-pause text-red-400 mr-2"></i>
                    <span className="text-red-300 text-sm font-medium">
                      Timer Paused - Please stay on this page to continue watching
                    </span>
                  </div>
                </div>
              )}

              <div className="bg-white/20 rounded-full h-3 max-w-md mx-auto">
                <div
                  className={`h-3 rounded-full transition-all duration-1000 ${isPaused ? 'bg-red-500' : 'bg-youtube-red'}`}
                  style={{
                    width: `${((videoSettings.videoDuration - timeRemaining) / videoSettings.videoDuration) * 100}%`
                  }}
                ></div>
              </div>
              <div className="space-x-4">
                <button
                  onClick={stopWatching}
                  className="btn-secondary"
                >
                  <i className="fas fa-stop mr-2"></i>
                  Stop Watching
                </button>
                {canNext && (
                  <button
                    onClick={nextVideo}
                    disabled={isSubmitting || !canNext}
                    className={`${isSubmitting ? 'btn-disabled cursor-not-allowed opacity-50' : 'btn-primary'}`}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="spinner mr-2 w-4 h-4"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-arrow-right mr-2"></i>
                        Next Video
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Daily Watch Times Section */}
      {dailyWatchTimes.length > 0 && (
        <div className="glass-card p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-white">
              <i className="fas fa-clock mr-2"></i>
              Today's Watch History
            </h2>
            <div className="text-white/70 text-sm">
              Total: {dailyWatchTimes.length} videos watched
            </div>
          </div>

          <div className="max-h-64 overflow-y-auto">
            <div className="grid gap-2">
              {dailyWatchTimes.map((watchTime, index) => (
                <div key={index} className="bg-white/10 rounded-lg p-3 flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="bg-youtube-red/20 rounded-full p-2 mr-3">
                      <i className="fas fa-play text-youtube-red text-sm"></i>
                    </div>
                    <div>
                      <p className="text-white font-medium">Video #{index + 1}</p>
                      <p className="text-white/70 text-sm">
                        {watchTime.toLocaleDateString('en-IN', {
                          weekday: 'short',
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-medium">
                      {watchTime.toLocaleTimeString('en-IN', {
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: true
                      })}
                    </p>
                    <p className="text-white/70 text-xs">
                      {formatTimeAgo(watchTime)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {dailyWatchTimes.length >= 50 && (
            <div className="mt-4 bg-green-500/20 border border-green-400/30 rounded-lg p-3">
              <div className="flex items-center justify-center text-center">
                <i className="fas fa-trophy text-green-400 mr-2"></i>
                <span className="text-green-300 text-sm font-medium">
                  Daily target completed! Great job! 🎉
                </span>
              </div>
            </div>
          )}
        </div>
      )}

    </div>
  )
}
