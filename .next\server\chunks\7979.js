"use strict";exports.id=7979,exports.ids=[7979],exports.modules={33784:(e,t,o)=>{o.d(t,{db:()=>i,j2:()=>n});var r=o(67989),a=o(63385),s=o(75535),c=o(70146);let l=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),n=(0,a.xI)(l),i=(0,s.aU)(l);(0,c.c7)(l)},51278:(e,t,o)=>{o.d(t,{CQ:()=>_,Dl:()=>n,G9:()=>d,M4:()=>g,Mb:()=>m,_f:()=>u,g4:()=>s,nS:()=>i});var r=o(33784),a=o(77567);function s(e){try{let t=new Date().toDateString(),o=`video_session_${e}_${t}`,r=`watch_times_${e}_${t}`,a=`daily_watch_times_${e}_${t}`,s=localStorage.getItem(`backup_timestamp_${e}`);if(!s)return!1;if(new Date(parseInt(s)).toDateString()!==t)return c(e),!1;let l=localStorage.getItem(`backup_${o}`),n=localStorage.getItem(`backup_${r}`),i=localStorage.getItem(`backup_${a}`),u=!1;if(l&&(localStorage.setItem(o,l),u=!0),n&&(localStorage.setItem(r,n),u=!0),i&&(localStorage.setItem(a,i),u=!0),u)return console.log("Session data restored for user:",e,{sessionCount:l,watchTimesCount:n?JSON.parse(n).length:0,dailyWatchTimesCount:i?JSON.parse(i).length:0}),c(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function c(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function l(e,t=!1){try{t&&function(e){try{let t=new Date().toDateString(),o=`video_session_${e}_${t}`,r=`watch_times_${e}_${t}`,a=`daily_watch_times_${e}_${t}`,s=localStorage.getItem(o),c=localStorage.getItem(r),l=localStorage.getItem(a);s&&localStorage.setItem(`backup_${o}`,s),c&&localStorage.setItem(`backup_${r}`,c),l&&localStorage.setItem(`backup_${a}`,l),localStorage.setItem(`backup_timestamp_${e}`,Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:s,watchTimesCount:c?JSON.parse(c).length:0,dailyWatchTimesCount:l?JSON.parse(l).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),console.log("\uD83E\uDDF9 Starting comprehensive localStorage cleanup for user:",e);let o=Object.keys(localStorage),r=0;o.forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("daily_watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.startsWith("notification_")||t.startsWith("wallet_")||t.startsWith("transaction_")||t.startsWith("work_")||t.startsWith("session_")||t.includes("mytube_")||t.includes("user_")||t.includes("_uid_")||t.includes("firebase"))&&(localStorage.removeItem(t),r++,console.log("\uD83D\uDDD1️ Cleared key:",t))}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache","userPreferences","dashboardCache","profileCache","withdrawalCache","planCache","videoCache","lastActiveUser","currentSession","activeUserId"].forEach(e=>{localStorage.getItem(e)&&(localStorage.removeItem(e),r++,console.log("\uD83D\uDDD1️ Cleared common key:",e))}),console.log(`✅ Local storage cleanup completed for user ${e}: ${r} keys cleared`,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}function n(e=!1){try{if(console.log("\uD83E\uDDF9 Starting COMPLETE localStorage cleanup..."),e){let e=Object.keys(localStorage),t={};e.forEach(e=>{e.startsWith("backup_")&&(t[e]=localStorage.getItem(e)||"")}),localStorage.clear(),Object.entries(t).forEach(([e,t])=>{localStorage.setItem(e,t)}),console.log(`✅ Complete cleanup done, preserved ${Object.keys(t).length} backup keys`)}else localStorage.clear(),console.log("✅ Complete localStorage cleared (nuclear option)")}catch(e){console.error("Error in complete localStorage cleanup:",e)}}function i(e){try{console.log("\uD83D\uDD12 Isolating session for user:",e),Object.keys(localStorage).forEach(t=>{(t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("daily_watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_"))&&!t.includes(e)&&(localStorage.removeItem(t),console.log("\uD83D\uDDD1️ Removed other user data:",t))}),localStorage.setItem("activeUserId",e),localStorage.setItem("sessionIsolatedAt",Date.now().toString()),console.log("✅ Session isolated for user:",e)}catch(e){console.error("Error isolating user session:",e)}}async function u(e,t="/login"){try{if((await a.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&l(e,!1),await r.j2.signOut(),a.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function g(e,t="/login",o=!0){try{e&&l(e,o),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function d(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let o=localStorage.getItem(e);o&&new Date(parseInt(o)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}function h(e){try{let t=localStorage.getItem("activeUserId");if(!t)return console.warn("⚠️ No active user ID found in localStorage"),!1;if(t!==e)return console.warn("⚠️ Session user mismatch:",{activeUserId:t,requestedUserId:e}),!1;return!0}catch(e){return console.error("Error validating user session:",e),!1}}function m(e,t){try{if(!h(t))return console.warn("⚠️ Blocked localStorage access due to session validation failure"),null;if(e.includes("_")&&!e.includes(t)&&(e.startsWith("video_")||e.startsWith("watch_")||e.startsWith("daily_")))return console.warn("⚠️ Blocked access to other user's data:",e),null;return localStorage.getItem(e)}catch(e){return console.error("Error in secure localStorage get:",e),null}}function _(e,t,o){try{if(!h(o))return console.warn("⚠️ Blocked localStorage write due to session validation failure"),!1;if(e.includes("_")&&!e.includes(o)&&(e.startsWith("video_")||e.startsWith("watch_")||e.startsWith("daily_")))return console.warn("⚠️ Blocked write to other user's data:",e),!1;return localStorage.setItem(e,t),!0}catch(e){return console.error("Error in secure localStorage set:",e),!1}}},87979:(e,t,o)=>{o.d(t,{Nu:()=>c,hD:()=>s,wC:()=>l});var r=o(43210);o(63385),o(33784);var a=o(51278);function s(){let[e,t]=(0,r.useState)(null),[o,s]=(0,r.useState)(!0),c=async()=>{try{await (0,a.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:o,signOut:c}}function c(){let{user:e,loading:t}=s();return{user:e,loading:t}}function l(){let{user:e,loading:t}=s(),[o,a]=(0,r.useState)(!1),[c,l]=(0,r.useState)(!0);return{user:e,loading:t||c,isAdmin:o}}}};