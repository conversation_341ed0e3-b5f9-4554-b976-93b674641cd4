'use client'

import { useState, useEffect } from 'react'
import { getUserNotifications, markNotificationAsRead, isNotificationRead, getUnreadNotificationCount, Notification } from '@/lib/dataService'

interface NotificationPanelProps {
  userId: string
  isOpen: boolean
  onClose: () => void
}

export default function NotificationPanel({ userId, isOpen, onClose }: NotificationPanelProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (isOpen && userId) {
      loadNotifications()
    }
  }, [isOpen, userId])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      const userNotifications = await getUserNotifications(userId, 20)
      setNotifications(userNotifications)
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleNotificationClick = (notification: Notification) => {
    if (notification.id && !isNotificationRead(notification.id, userId)) {
      markNotificationAsRead(notification.id, userId)
      // Force re-render to update read status
      setNotifications([...notifications])
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return 'fas fa-check-circle text-green-500'
      case 'warning':
        return 'fas fa-exclamation-triangle text-yellow-500'
      case 'error':
        return 'fas fa-times-circle text-red-500'
      default:
        return 'fas fa-info-circle text-blue-500'
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
    
    if (diffInSeconds < 60) {
      return 'Just now'
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `${hours} hour${hours > 1 ? 's' : ''} ago`
    } else {
      const days = Math.floor(diffInSeconds / 86400)
      return `${days} day${days > 1 ? 's' : ''} ago`
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-16 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-bold text-gray-900">
            <i className="fas fa-bell mr-2"></i>
            Notifications
          </h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadNotifications}
              disabled={loading}
              className="text-gray-500 hover:text-gray-700 transition-colors p-1"
              title="Refresh notifications"
            >
              <i className={`fas fa-sync-alt ${loading ? 'animate-spin' : ''}`}></i>
            </button>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 transition-colors"
            >
              <i className="fas fa-times text-xl"></i>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[60vh]">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="spinner w-8 h-8"></div>
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-8">
              <i className="fas fa-bell-slash text-gray-300 text-4xl mb-4"></i>
              <p className="text-gray-500">No notifications yet</p>
              <p className="text-gray-400 text-sm">You'll see important updates here</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {notifications.map((notification) => {
                const isRead = notification.id ? isNotificationRead(notification.id, userId) : false
                
                return (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                      !isRead ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        <i className={getNotificationIcon(notification.type)}></i>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className={`text-sm font-medium ${
                            !isRead ? 'text-gray-900' : 'text-gray-700'
                          }`}>
                            {notification.title}
                          </h4>
                          {!isRead && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                          )}
                        </div>
                        
                        <p className={`text-sm mt-1 ${
                          !isRead ? 'text-gray-800' : 'text-gray-600'
                        }`}>
                          {notification.message}
                        </p>
                        
                        <p className="text-xs text-gray-500 mt-2">
                          {formatTimeAgo(notification.createdAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        {notifications.length > 0 && (
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <button
              onClick={() => {
                // Mark all as read
                notifications.forEach(notification => {
                  if (notification.id && !isNotificationRead(notification.id, userId)) {
                    markNotificationAsRead(notification.id, userId)
                  }
                })
                setNotifications([...notifications])
              }}
              className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              Mark all as read
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

// Notification Bell Component
interface NotificationBellProps {
  userId: string
  onClick: () => void
}

export function NotificationBell({ userId, onClick }: NotificationBellProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (userId) {
      loadNotifications()
      // Refresh notifications every 15 seconds for better real-time updates
      const interval = setInterval(loadNotifications, 15000)
      return () => clearInterval(interval)
    }
  }, [userId])

  const loadNotifications = async () => {
    try {
      setIsLoading(true)
      const userNotifications = await getUserNotifications(userId, 20)
      setNotifications(userNotifications)
      const newUnreadCount = getUnreadNotificationCount(userNotifications, userId)

      // Show a brief animation if new notifications arrived
      if (newUnreadCount > unreadCount && unreadCount > 0) {
        // Animate the bell
        const bellElement = document.querySelector('.notification-bell')
        if (bellElement) {
          bellElement.classList.add('animate-bounce')
          setTimeout(() => {
            bellElement.classList.remove('animate-bounce')
          }, 1000)
        }
      }

      setUnreadCount(newUnreadCount)
      console.log(`Loaded ${userNotifications.length} notifications, ${newUnreadCount} unread`)
    } catch (error) {
      console.error('Error loading notifications for bell:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <button
      onClick={onClick}
      className="relative p-2 text-white hover:text-yellow-300 transition-colors"
      title={`${unreadCount} unread notifications`}
    >
      <i className={`fas fa-bell text-xl notification-bell ${isLoading ? 'animate-pulse' : ''}`}></i>
      {unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse">
          {unreadCount > 9 ? '9+' : unreadCount}
        </span>
      )}
      {isLoading && (
        <span className="absolute -bottom-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-3 w-3 flex items-center justify-center">
          <i className="fas fa-sync-alt text-xs animate-spin"></i>
        </span>
      )}
    </button>
  )
}
