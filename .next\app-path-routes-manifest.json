{"/_not-found/page": "/_not-found", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/page": "/admin", "/admin/notifications/page": "/admin/notifications", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/settings/page": "/admin/settings", "/admin/upload-users/page": "/admin/upload-users", "/clear-cache/page": "/clear-cache", "/dashboard/page": "/dashboard", "/admin/transactions/page": "/admin/transactions", "/admin/withdrawals/page": "/admin/withdrawals", "/admin/users/page": "/admin/users", "/debug-firestore/page": "/debug-firestore", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-registration/page": "/debug-registration", "/debug-registration-simple/page": "/debug-registration-simple", "/forgot-password/page": "/forgot-password", "/login/page": "/login", "/profile/page": "/profile", "/plans/page": "/plans", "/refer/page": "/refer", "/register/page": "/register", "/page": "/", "/registration-diagnostics/page": "/registration-diagnostics", "/reset-password/page": "/reset-password", "/test-firebase-connection/page": "/test-firebase-connection", "/support/page": "/support", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-registration/page": "/test-registration", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}