{"/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/leaves/page": "/admin/leaves", "/_not-found/page": "/_not-found", "/admin/page": "/admin", "/admin/settings/page": "/admin/settings", "/admin/login/page": "/admin/login", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/upload-users/page": "/admin/upload-users", "/admin/setup/page": "/admin/setup", "/admin/notifications/page": "/admin/notifications", "/admin/transactions/page": "/admin/transactions", "/dashboard/page": "/dashboard", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/admin/simple-upload/page": "/admin/simple-upload", "/forgot-password/page": "/forgot-password", "/clear-cache/page": "/clear-cache", "/debug-registration/page": "/debug-registration", "/page": "/", "/admin/withdrawals/page": "/admin/withdrawals", "/profile/page": "/profile", "/debug-registration-simple/page": "/debug-registration-simple", "/register/page": "/register", "/reset-password/page": "/reset-password", "/plans/page": "/plans", "/refer/page": "/refer", "/test-firebase-connection/page": "/test-firebase-connection", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-registration/page": "/test-registration", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/login/page": "/login", "/support/page": "/support", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/test-reg-simple/page": "/test-reg-simple", "/admin/users/page": "/admin/users", "/wallet/page": "/wallet", "/work/page": "/work", "/transactions/page": "/transactions"}