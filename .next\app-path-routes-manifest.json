{"/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-active-days/page": "/admin/fix-active-days", "/_not-found/page": "/_not-found", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/setup/page": "/admin/setup", "/admin/page": "/admin", "/admin/settings/page": "/admin/settings", "/admin/upload-users/page": "/admin/upload-users", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/clear-cache/page": "/clear-cache", "/debug-firestore/page": "/debug-firestore", "/admin/transactions/page": "/admin/transactions", "/dashboard/page": "/dashboard", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-registration/page": "/debug-registration", "/admin/users/page": "/admin/users", "/debug-firestore-issue/page": "/debug-firestore-issue", "/page": "/", "/debug-registration-simple/page": "/debug-registration-simple", "/login/page": "/login", "/register/page": "/register", "/forgot-password/page": "/forgot-password", "/profile/page": "/profile", "/plans/page": "/plans", "/reset-password/page": "/reset-password", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/refer/page": "/refer", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/support/page": "/support", "/test-reg-simple/page": "/test-reg-simple", "/test-firebase/page": "/test-firebase", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/test-registration/page": "/test-registration", "/test-firestore/page": "/test-firestore", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}