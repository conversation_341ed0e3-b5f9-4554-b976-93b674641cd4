{"/admin/daily-active-days/page": "/admin/daily-active-days", "/_not-found/page": "/_not-found", "/admin/leaves/page": "/admin/leaves", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/setup/page": "/admin/setup", "/admin/transactions/page": "/admin/transactions", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/upload-users/page": "/admin/upload-users", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/withdrawals/page": "/admin/withdrawals", "/clear-cache/page": "/clear-cache", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/users/page": "/admin/users", "/debug-firestore/page": "/debug-firestore", "/dashboard/page": "/dashboard", "/debug-registration-simple/page": "/debug-registration-simple", "/login/page": "/login", "/debug-registration/page": "/debug-registration", "/forgot-password/page": "/forgot-password", "/plans/page": "/plans", "/refer/page": "/refer", "/register/page": "/register", "/page": "/", "/registration-diagnostics/page": "/registration-diagnostics", "/reset-password/page": "/reset-password", "/profile/page": "/profile", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase/page": "/test-firebase", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-registration/page": "/test-registration", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}