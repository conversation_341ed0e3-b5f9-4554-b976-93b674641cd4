{"/admin/fix-active-days/page": "/admin/fix-active-days", "/_not-found/page": "/_not-found", "/admin/leaves/page": "/admin/leaves", "/admin/login/page": "/admin/login", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/page": "/admin", "/admin/settings/page": "/admin/settings", "/admin/notifications/page": "/admin/notifications", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/transactions/page": "/admin/transactions", "/admin/upload-users/page": "/admin/upload-users", "/admin/setup/page": "/admin/setup", "/clear-cache/page": "/clear-cache", "/admin/users/page": "/admin/users", "/admin/withdrawals/page": "/admin/withdrawals", "/dashboard/page": "/dashboard", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-registration/page": "/debug-registration", "/forgot-password/page": "/forgot-password", "/login/page": "/login", "/plans/page": "/plans", "/profile/page": "/profile", "/refer/page": "/refer", "/page": "/", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase/page": "/test-firebase", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-registration/page": "/test-registration", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/transactions/page": "/transactions", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/work/page": "/work"}