{"/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/page": "/admin", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/upload-users/page": "/admin/upload-users", "/_not-found/page": "/_not-found", "/admin/transactions/page": "/admin/transactions", "/clear-cache/page": "/clear-cache", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/withdrawals/page": "/admin/withdrawals", "/dashboard/page": "/dashboard", "/admin/users/page": "/admin/users", "/debug-firestore/page": "/debug-firestore", "/login/page": "/login", "/debug-firestore-issue/page": "/debug-firestore-issue", "/forgot-password/page": "/forgot-password", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-registration/page": "/debug-registration", "/plans/page": "/plans", "/refer/page": "/refer", "/profile/page": "/profile", "/page": "/", "/register/page": "/register", "/reset-password/page": "/reset-password", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/support/page": "/support", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-reg-simple/page": "/test-reg-simple", "/test-firestore/page": "/test-firestore", "/test-registration/page": "/test-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/test-simple-registration/page": "/test-simple-registration", "/work/page": "/work"}