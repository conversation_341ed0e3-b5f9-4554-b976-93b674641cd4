{"/_not-found/page": "/_not-found", "/admin/leaves/page": "/admin/leaves", "/admin/login/page": "/admin/login", "/admin/notifications/page": "/admin/notifications", "/admin/transactions/page": "/admin/transactions", "/admin/page": "/admin", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/test-blocking/page": "/admin/test-blocking", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/users/page": "/admin/users", "/debug-registration-simple/page": "/debug-registration-simple", "/clear-cache/page": "/clear-cache", "/admin/upload-users/page": "/admin/upload-users", "/dashboard/page": "/dashboard", "/forgot-password/page": "/forgot-password", "/admin/withdrawals/page": "/admin/withdrawals", "/login/page": "/login", "/plans/page": "/plans", "/admin/daily-active-days/page": "/admin/daily-active-days", "/page": "/", "/debug-firestore/page": "/debug-firestore", "/profile/page": "/profile", "/refer/page": "/refer", "/test-firebase-connection/page": "/test-firebase-connection", "/reset-password/page": "/reset-password", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/support/page": "/support", "/test-reg-simple/page": "/test-reg-simple", "/register/page": "/register", "/test-firestore/page": "/test-firestore", "/test-registration/page": "/test-registration", "/test-simple-registration/page": "/test-simple-registration", "/debug-registration/page": "/debug-registration", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase/page": "/test-firebase", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/work/page": "/work", "/transactions/page": "/transactions"}