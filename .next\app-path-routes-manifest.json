{"/_not-found/page": "/_not-found", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/leaves/page": "/admin/leaves", "/admin/login/page": "/admin/login", "/admin/notifications/page": "/admin/notifications", "/admin/page": "/admin", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/transactions/page": "/admin/transactions", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/upload-users/page": "/admin/upload-users", "/admin/withdrawals/page": "/admin/withdrawals", "/clear-cache/page": "/clear-cache", "/admin/users/page": "/admin/users", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/debug-registration/page": "/debug-registration", "/debug-registration-simple/page": "/debug-registration-simple", "/forgot-password/page": "/forgot-password", "/login/page": "/login", "/plans/page": "/plans", "/profile/page": "/profile", "/page": "/", "/refer/page": "/refer", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/support/page": "/support", "/reset-password/page": "/reset-password", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firestore/page": "/test-firestore", "/test-firebase/page": "/test-firebase", "/test-reg-simple/page": "/test-reg-simple", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-registration/page": "/test-registration", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/dashboard/page": "/dashboard", "/wallet/page": "/wallet", "/work/page": "/work"}