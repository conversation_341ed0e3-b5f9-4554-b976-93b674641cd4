(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},744:(e,s,t)=>{"use strict";t.d(s,{J:()=>i});var r=t(43210),a=t(3582);function i(e){let[s,t]=(0,r.useState)(!1),[i,l]=(0,r.useState)(!0);return{hasBlockingNotifications:s,isChecking:i,checkForBlockingNotifications:async()=>{try{l(!0);let s=await (0,a.iA)(e);t(s)}catch(e){console.error("Error checking for blocking notifications:",e),t(!1)}finally{l(!1)}},markAllAsRead:()=>{t(!1)}}}},2554:(e,s,t)=>{Promise.resolve().then(t.bind(t,87032))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13170:(e,s,t)=>{Promise.resolve().then(t.bind(t,75758))},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59373:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75758)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\profile\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75758:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\profile\\page.tsx","default")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87032:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(60687),a=t(43210),i=t(85814),l=t.n(i),n=t(87979),o=t(744),d=t(3582),c=t(51278),u=t(63385),m=t(98873),x=t(77567);function p(){let{user:e,loading:s}=(0,n.hD)(),{hasBlockingNotifications:t,isChecking:i,markAllAsRead:p}=(0,o.J)(e?.uid||null),[h,f]=(0,a.useState)(null),[b,g]=(0,a.useState)(!0),[w,j]=(0,a.useState)(!1),[N,v]=(0,a.useState)({name:"",email:"",mobile:"",currentPassword:"",newPassword:"",confirmPassword:""}),[y,P]=(0,a.useState)(!1),[C,k]=(0,a.useState)(!1),[q,S]=(0,a.useState)(!1),[A,E]=(0,a.useState)(!1),[M,D]=(0,a.useState)(!1),[U,R]=(0,a.useState)(0),_=async()=>{if(!N.name.trim())return void x.A.fire({icon:"error",title:"Validation Error",text:"Name is required"});if(N.mobile&&!/^[6-9]\d{9}$/.test(N.mobile))return void x.A.fire({icon:"error",title:"Validation Error",text:"Please enter a valid 10-digit mobile number"});if(N.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(N.email))return void x.A.fire({icon:"error",title:"Validation Error",text:"Please enter a valid email address"});if(M){if(!N.currentPassword)return void x.A.fire({icon:"error",title:"Validation Error",text:"Current password is required to change password"});if(!N.newPassword||N.newPassword.length<6)return void x.A.fire({icon:"error",title:"Validation Error",text:"New password must be at least 6 characters long"});if(N.newPassword!==N.confirmPassword)return void x.A.fire({icon:"error",title:"Validation Error",text:"New password and confirm password do not match"})}try{if(P(!0),M&&N.currentPassword&&N.newPassword)try{let s=u.IX.credential(e.email,N.currentPassword);await (0,u.kZ)(e,s),await (0,u.f3)(e,N.newPassword),x.A.fire({icon:"success",title:"Password Updated",text:"Your password has been updated successfully",timer:2e3,showConfirmButton:!1})}catch(s){console.error("Error updating password:",s);let e="Failed to update password. Please try again.";"auth/wrong-password"===s.code?e="Current password is incorrect":"auth/too-many-requests"===s.code&&(e="Too many failed attempts. Please try again later."),x.A.fire({icon:"error",title:"Password Update Failed",text:e});return}if(N.email!==h?.email&&N.email)try{await (0,u.Ww)(e,N.email)}catch(s){console.error("Error updating email:",s);let e="Failed to update email. Please try again.";"auth/email-already-in-use"===s.code?e="This email is already in use by another account":"auth/requires-recent-login"===s.code&&(e="Please log out and log back in before changing your email"),x.A.fire({icon:"error",title:"Email Update Failed",text:e});return}let s={name:N.name.trim(),mobile:N.mobile};N.email!==h?.email&&N.email&&(s.email=N.email),await (0,d.b6)(e.uid,s),f(e=>e?{...e,...s}:null),j(!1),D(!1),x.A.fire({icon:"success",title:"Profile Updated",text:"Your profile has been updated successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error updating profile:",e),x.A.fire({icon:"error",title:"Update Failed",text:"Failed to update profile. Please try again."})}finally{P(!1)}};return s||b||i?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:s?"Loading...":i?"Checking notifications...":"Loading profile..."})]})}):t&&e?(0,r.jsx)(m.A,{userId:e.uid,onAllRead:p}):(0,r.jsxs)("div",{className:"min-h-screen p-4",children:[(0,r.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(l(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"My Profile"}),(0,r.jsxs)("button",{onClick:()=>{(0,c._f)(e?.uid,"/login")},className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-sign-out-alt mr-2"}),"Logout"]})]})}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,r.jsx)("i",{className:"fas fa-user mr-2"}),"Profile Information"]}),!w&&(0,r.jsxs)("button",{onClick:()=>{j(!0)},className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-edit mr-2"}),"Edit"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Full Name"}),w?(0,r.jsx)("input",{type:"text",value:N.name,onChange:e=>v(s=>({...s,name:e.target.value})),className:"form-input",placeholder:"Enter your full name"}):(0,r.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:h?.name||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Email Address"}),w?(0,r.jsx)("input",{type:"email",value:N.email,onChange:e=>v(s=>({...s,email:e.target.value})),className:"form-input",placeholder:"Enter your email address"}):(0,r.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:h?.email||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Mobile Number"}),w?(0,r.jsx)("input",{type:"tel",value:N.mobile,onChange:e=>v(s=>({...s,mobile:e.target.value})),className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10}):(0,r.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:h?.mobile||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Member Since"}),(0,r.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:h?.joinedDate?.toLocaleDateString()||"Unknown"})]}),w&&(0,r.jsxs)("div",{className:"border-t border-white/20 pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Change Password"}),(0,r.jsxs)("button",{onClick:()=>D(!M),className:`glass-button px-4 py-2 text-white ${M?"bg-red-500/20":"bg-blue-500/20"}`,children:[(0,r.jsx)("i",{className:`fas ${M?"fa-times":"fa-key"} mr-2`}),M?"Cancel":"Change Password"]})]}),M&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:C?"text":"password",value:N.currentPassword,onChange:e=>v(s=>({...s,currentPassword:e.target.value})),className:"form-input pr-12",placeholder:"Enter your current password"}),(0,r.jsx)("button",{type:"button",onClick:()=>k(!C),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,r.jsx)("i",{className:`fas ${C?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:q?"text":"password",value:N.newPassword,onChange:e=>v(s=>({...s,newPassword:e.target.value})),className:"form-input pr-12",placeholder:"Enter new password (min 6 characters)"}),(0,r.jsx)("button",{type:"button",onClick:()=>S(!q),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,r.jsx)("i",{className:`fas ${q?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Confirm New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:A?"text":"password",value:N.confirmPassword,onChange:e=>v(s=>({...s,confirmPassword:e.target.value})),className:"form-input pr-12",placeholder:"Confirm your new password"}),(0,r.jsx)("button",{type:"button",onClick:()=>E(!A),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,r.jsx)("i",{className:`fas ${A?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,r.jsx)("div",{className:"bg-yellow-500/20 p-3 rounded-lg",children:(0,r.jsxs)("p",{className:"text-yellow-300 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),"Password must be at least 6 characters long. You will need to log in again after changing your password."]})})]})]})]}),w&&(0,r.jsxs)("div",{className:"flex gap-4 mt-6",children:[(0,r.jsx)("button",{onClick:_,disabled:y,className:"btn-primary flex-1",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-save mr-2"}),"Save Changes"]})}),(0,r.jsxs)("button",{onClick:()=>{j(!1),D(!1),v({name:h?.name||"",email:h?.email||"",mobile:h?.mobile||"",currentPassword:"",newPassword:"",confirmPassword:""})},className:"btn-secondary flex-1",children:[(0,r.jsx)("i",{className:"fas fa-times mr-2"}),"Cancel"]})]})]}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-crown mr-2"}),"Plan Information"]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Current Plan"}),(0,r.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:h?.plan||"Trial"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Active Days"}),(0,r.jsxs)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:[U," days"]})]}),h?.planExpiry&&(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Plan Expires"}),(0,r.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:h.planExpiry.toLocaleDateString()})]})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(l(),{href:"/plans",className:"btn-primary",children:[(0,r.jsx)("i",{className:"fas fa-upgrade mr-2"}),"Upgrade Plan"]})})]}),(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-users mr-2"}),"Referral Information"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Your Referral Code"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg flex-1 font-mono",children:h?.referralCode||"Not generated"}),(0,r.jsx)("button",{onClick:()=>{h?.referralCode&&(navigator.clipboard.writeText(h.referralCode),x.A.fire({icon:"success",title:"Copied!",text:"Referral code copied to clipboard",timer:1500,showConfirmButton:!1}))},className:"glass-button px-4 py-2 text-white",title:"Copy referral code",children:(0,r.jsx)("i",{className:"fas fa-copy"})})]})]}),h?.referredBy&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Referred By"}),(0,r.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg font-mono",children:h.referredBy})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("button",{onClick:()=>{if(h?.referralCode){let e=`${window.location.origin}/register?ref=${h.referralCode}`;navigator.share?navigator.share({title:"Join MyTube and Start Earning",text:"Join MyTube using my referral code and start earning money by watching videos!",url:e}):(navigator.clipboard.writeText(e),x.A.fire({icon:"success",title:"Link Copied!",text:"Referral link copied to clipboard",timer:2e3,showConfirmButton:!1}))}},className:"btn-primary flex-1",children:[(0,r.jsx)("i",{className:"fas fa-share mr-2"}),"Share Referral Link"]}),(0,r.jsxs)(l(),{href:"/refer",className:"btn-secondary flex-1 text-center",children:[(0,r.jsx)("i",{className:"fas fa-users mr-2"}),"View Referrals"]})]})]})]})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98873:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var r=t(60687),a=t(43210),i=t(3582);function l({userId:e,onAllRead:s}){let[t,l]=(0,a.useState)([]),[n,o]=(0,a.useState)(0),[d,c]=(0,a.useState)(!0),u=async()=>{let r=t[n];r?.id&&(await (0,i.bA)(r.id,e),n<t.length-1?o(n+1):s())};if(d)return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===t.length)return null;let m=t[n];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(m.type)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,r.jsxs)("p",{className:"text-blue-100 text-sm",children:[n+1," of ",t.length," notifications"]})]})]}),(0,r.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,r.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:m.title}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,r.jsx)("p",{className:"text-gray-800 leading-relaxed",children:m.message})}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,r.jsxs)("span",{children:["From: ",m.createdBy]}),(0,r.jsx)("span",{children:(e=>{let s=Math.floor((new Date().getTime()-e.getTime())/1e3);return s<60?"Just now":s<3600?`${Math.floor(s/60)} minutes ago`:s<86400?`${Math.floor(s/3600)} hours ago`:`${Math.floor(s/86400)} days ago`})(m.createdAt)})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[n+1,"/",t.length]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${(n+1)/t.length*100}%`}})})]}),(0,r.jsxs)("button",{onClick:u,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,r.jsx)("i",{className:"fas fa-check"}),(0,r.jsx)("span",{children:n<t.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("i",{className:"fas fa-info-circle"}),(0,r.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[6204,6958,7567,8441,3582,7979],()=>t(59373));module.exports=r})();