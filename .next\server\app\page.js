(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21204:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\page.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},24871:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(60687),r=t(43210),l=t(77567);function i({variant:e="homepage",className:s=""}){let{isInstallable:t,isInstalled:i,installApp:n,getInstallInstructions:c}=function(){let[e,s]=(0,r.useState)(null),[t,a]=(0,r.useState)(!1),[l,i]=(0,r.useState)(!1);return{isInstallable:t,isInstalled:l,installApp:async()=>{if(!e)return!1;try{await e.prompt();let{outcome:t}=await e.userChoice;if("accepted"===t)return i(!0),a(!1),s(null),!0;return!1}catch(e){return console.error("Error installing app:",e),!1}},getInstallInstructions:()=>{let e=navigator.userAgent.toLowerCase();return e.includes("chrome")&&!e.includes("edg")?{browser:"Chrome",steps:["Click the install button above","Or click the install icon in the address bar",'Click "Install" in the popup']}:e.includes("firefox")?{browser:"Firefox",steps:["Click the menu button (☰)",'Select "Install this site as an app"','Click "Install" in the dialog']}:e.includes("safari")?{browser:"Safari",steps:["Tap the Share button",'Scroll down and tap "Add to Home Screen"','Tap "Add" to install']}:e.includes("edg")?{browser:"Edge",steps:["Click the install button above","Or click the app icon in the address bar",'Click "Install" in the popup']}:{browser:"Your Browser",steps:["Look for an install option in your browser menu","Or check the address bar for an install icon","Follow your browser's installation prompts"]}}}}(),[o,x]=(0,r.useState)(!1),d=async()=>{await n()?l.A.fire({icon:"success",title:"App Installed!",text:"MyTube has been installed on your device. You can now access it from your home screen.",timer:3e3,showConfirmButton:!1}):t||x(!0)},m=()=>{let e=c();l.A.fire({title:`Install MyTube on ${e.browser}`,html:`
        <div class="text-left">
          <p class="mb-4 text-gray-600">Follow these steps to install MyTube as an app:</p>
          <ol class="list-decimal list-inside space-y-2">
            ${e.steps.map(e=>`<li class="text-gray-700">${e}</li>`).join("")}
          </ol>
          <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <p class="text-sm text-blue-800">
              <i class="fas fa-info-circle mr-2"></i>
              Installing the app gives you faster access, offline capabilities, and a native app experience!
            </p>
          </div>
        </div>
      `,confirmButtonText:"Got it!",confirmButtonColor:"#3b82f6"})};return i?(0,a.jsx)("div",{className:`${s}`,children:"homepage"===e?(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-check-circle text-4xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"App Installed!"}),(0,a.jsx)("p",{className:"text-white/80",children:"MyTube is installed on your device"})]}):(0,a.jsxs)("div",{className:"flex items-center text-green-400",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"App Installed"})]})}):"homepage"===e?(0,a.jsxs)("div",{className:`glass-card p-8 hover:scale-105 transition-transform ${s}`,children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-5xl text-purple-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Get the best experience with our mobile app"}),(0,a.jsx)("div",{className:"space-y-3",children:t?(0,a.jsxs)("button",{onClick:d,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Install Now"]}):(0,a.jsxs)("button",{onClick:m,className:"w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Install"]})}),(0,a.jsx)("div",{className:"mt-4 text-white/60 text-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-bolt mr-1"}),"Faster"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-wifi mr-1"}),"Offline"]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-home mr-1"}),"Home Screen"]})]})})]}):(0,a.jsx)("div",{className:`glass-card p-4 ${s}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-purple-400 text-xl mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Install MyTube App"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Get faster access & offline features"})]})]}),t?(0,a.jsxs)("button",{onClick:d,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-download mr-1"}),"Install"]}):(0,a.jsxs)("button",{onClick:m,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),"How to"]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},40495:(e,s,t)=>{Promise.resolve().then(t.bind(t,75694))},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71653:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>x,routeModule:()=>m,tree:()=>o});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21204)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}],x=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75694:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(60687),r=t(43210),l=t(85814),i=t.n(l),n=t(24871),c=t(30474),o=t(77567);function x(){let[e,s]=(0,r.useState)(!1),t="2.1.0",l=async()=>{try{if(s(!0),!(await o.A.fire({title:"Clear Cache & Data?",html:`
          <div class="text-left">
            <p class="mb-3"><strong>This will clear:</strong></p>
            <ul class="text-sm space-y-1 mb-4">
              <li>• Browser cache & stored data</li>
              <li>• Local storage & session data</li>
              <li>• Cookies & preferences</li>
              <li>• Cached videos & images</li>
              <li>• All temporary files</li>
            </ul>
            <p class="text-sm text-gray-600">
              <strong>Why clear cache?</strong> Get the latest version of MyTube with all new features and bug fixes.
            </p>
          </div>
        `,icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Clear Everything!",cancelButtonText:"Cancel",allowOutsideClick:!1})).isConfirmed)return void s(!1);o.A.fire({title:"Clearing Cache & Data...",html:`
          <div class="text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p>Please wait while we clear all cached data...</p>
            <p class="text-sm text-gray-600 mt-2">This may take a few seconds</p>
          </div>
        `,allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1});try{localStorage.clear(),console.log("✅ localStorage cleared")}catch(e){console.warn("⚠️ Could not clear localStorage:",e)}try{sessionStorage.clear(),console.log("✅ sessionStorage cleared")}catch(e){console.warn("⚠️ Could not clear sessionStorage:",e)}try{if("indexedDB"in window){for(let e of["firebaseLocalStorageDb","firebase-heartbeat-database","firebase-installations-database"])try{await Promise.race([new Promise((s,t)=>{let a=indexedDB.deleteDatabase(e);a.onsuccess=()=>s(!0),a.onerror=()=>s(!0),a.onblocked=()=>s(!0)}),new Promise((e,s)=>setTimeout(()=>s(Error("Timeout")),3e3))])}catch(s){console.warn(`⚠️ Could not clear IndexedDB ${e}:`,s)}console.log("✅ IndexedDB cleared")}}catch(e){console.warn("⚠️ Could not clear IndexedDB:",e)}try{if("serviceWorker"in navigator&&"caches"in window){let e=await Promise.race([caches.keys(),new Promise((e,s)=>setTimeout(()=>s(Error("Cache keys timeout")),5e3))]);await Promise.race([Promise.all(e.map(e=>caches.delete(e))),new Promise((e,s)=>setTimeout(()=>s(Error("Cache deletion timeout")),5e3))]),console.log("✅ Service Worker cache cleared")}}catch(e){console.warn("⚠️ Could not clear Service Worker cache:",e)}try{document.cookie.split(";").forEach(e=>{let s=e.indexOf("="),t=s>-1?e.substring(0,s):e;document.cookie=`${t}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`,document.cookie=`${t}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`,document.cookie=`${t}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`}),console.log("✅ Cookies cleared")}catch(e){console.warn("⚠️ Could not clear cookies:",e)}await new Promise(e=>setTimeout(e,1e3)),o.A.fire({icon:"success",title:"Cache & Data Cleared!",html:`
          <div class="text-center">
            <p class="mb-3">✅ All cached data has been cleared successfully!</p>
            <p class="text-sm text-gray-600 mb-3">
              The page will now reload to load the latest version of MyTube.
            </p>
            <p class="text-sm text-green-600 font-semibold">
              🎉 You now have the freshest version with all updates!
            </p>
          </div>
        `,timer:3e3,showConfirmButton:!0,confirmButtonText:"Reload Now",allowOutsideClick:!1}).then(()=>{window.location.reload()})}catch(e){console.error("Error clearing cache and data:",e),o.A.fire({icon:"error",title:"Clear Failed",html:`
          <div class="text-left">
            <p class="mb-2">Some data could not be cleared:</p>
            <p class="text-sm text-gray-600">${e instanceof Error?e.message:"Unknown error"}</p>
            <p class="text-sm text-blue-600 mt-3">
              Try refreshing the page manually (Ctrl+F5 or Cmd+Shift+R)
            </p>
          </div>
        `,confirmButtonText:"OK"})}finally{s(!1)}};return(0,a.jsxs)("main",{className:"min-h-screen",children:[(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 z-50 p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:32,height:32,className:"mr-2"}),(0,a.jsx)("span",{className:"text-white text-xl font-bold",children:"MyTube"})]}),(0,a.jsxs)("div",{className:"flex space-x-2 sm:space-x-4",children:[(0,a.jsx)("button",{onClick:l,disabled:e,className:"nav-link text-sm sm:text-base",title:"Clear cache & data to get latest version",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-3 h-3 mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Clearing..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Clear Cache"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Cache"})]})}),(0,a.jsxs)(i(),{href:"#pricing",className:"nav-link text-sm sm:text-base",children:[(0,a.jsx)("i",{className:"fas fa-crown mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Plans"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Plans"})]}),(0,a.jsxs)(i(),{href:"/login",className:"nav-link text-sm sm:text-base",children:[(0,a.jsx)("i",{className:"fas fa-sign-in-alt mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Login"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Login"})]})]})]})}),(0,a.jsx)("section",{className:"min-h-screen flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[(0,a.jsx)(c.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:60,height:60,className:"mr-4"}),(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"MyTube"})]}),(0,a.jsx)("h1",{className:"text-5xl md:text-7xl font-bold mb-6 gradient-text",children:"Watch Videos & Earn Money"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto",children:"Watch videos and earn up to ₹30,000 per month. Start your journey to financial freedom today by completing simple video watching tasks!"})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mb-12",children:[(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsx)("i",{className:"fas fa-play-circle text-4xl text-youtube-red mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Trending Videos"}),(0,a.jsx)("p",{className:"text-white/80",children:"Watch popular content daily"})]}),(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave text-4xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Instant Earnings"}),(0,a.jsx)("p",{className:"text-white/80",children:"Get paid for every video watched"})]}),(0,a.jsxs)("div",{className:"feature-card",children:[(0,a.jsx)("i",{className:"fas fa-bolt text-4xl text-yellow-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Fast & Simple"}),(0,a.jsx)("p",{className:"text-white/80",children:"Easy video watching process"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(i(),{href:"/login",className:"btn-primary inline-flex items-center text-lg px-8 py-4",children:[(0,a.jsx)("i",{className:"fas fa-rocket mr-3"}),"Start Earning Now"]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mt-6",children:[(0,a.jsxs)(i(),{href:"/how-it-works",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How It Works"]}),(0,a.jsxs)(i(),{href:"/faq",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-question-circle mr-2"}),"FAQ"]})]})]})]})}),(0,a.jsx)("section",{id:"pricing",className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Choose Your Earning Plan"}),(0,a.jsx)("p",{className:"text-xl text-white/80 max-w-3xl mx-auto",children:"Start with our free trial or upgrade to premium plans for higher earnings. Watch videos and earn money with flexible pricing options."})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"glass-card p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Trial"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"Free"}),(0,a.jsx)("span",{className:"text-white/60 ml-2",children:"/ 2 days"})]}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"Earn ₹10 per 50 videos"})]}),(0,a.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"2 days access"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"₹10 per 50 videos"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Basic support"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Video duration: 30 seconds"]})]}),(0,a.jsx)(i(),{href:"/register",className:"w-full btn-secondary block text-center",children:"Start Free Trial"})]}),(0,a.jsxs)("div",{className:"glass-card p-8 relative ring-2 ring-yellow-400",children:[(0,a.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,a.jsx)("span",{className:"bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold",children:"Most Popular"})}),(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Gold"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"₹3,999"}),(0,a.jsx)("span",{className:"text-white/60 ml-2",children:"/ 30 days"})]}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"Earn ₹200 per 50 videos"})]}),(0,a.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"30 days access"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"₹200 per 50 videos"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Video duration: 3 minutes"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Referral bonus: ₹400"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Priority support"]})]}),(0,a.jsx)(i(),{href:"/plans",className:"w-full bg-yellow-400 text-black py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-all duration-300 block text-center",children:"Choose Gold"})]}),(0,a.jsxs)("div",{className:"glass-card p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Diamond"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("span",{className:"text-4xl font-bold text-white",children:"₹9,999"}),(0,a.jsx)("span",{className:"text-white/60 ml-2",children:"/ 30 days"})]}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"Earn ₹400 per 50 videos"})]}),(0,a.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"30 days access"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"₹400 per 50 videos"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Video duration: 1 minute"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"Referral bonus: ₹1200"]}),(0,a.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),"VIP support"]})]}),(0,a.jsx)(i(),{href:"/plans",className:"w-full btn-primary block text-center",children:"Choose Diamond"})]})]}),(0,a.jsx)("div",{className:"text-center mt-12",children:(0,a.jsxs)(i(),{href:"/plans",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-crown mr-2"}),"View All Plans"]})})]})}),(0,a.jsx)("section",{className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Need Help?"}),(0,a.jsx)("p",{className:"text-xl text-white/80 mb-12 max-w-2xl mx-auto",children:"Our support team is here to help you get started and answer any questions about earning with MyTube."}),(0,a.jsx)("div",{className:"glass-card p-8 mb-12 max-w-2xl mx-auto clear-cache-section",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt text-5xl text-blue-400 mb-4 clear-cache-icon"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Get Latest Version"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Having issues or want the latest features? Clear your cache and data to get the freshest version of MyTube with all updates and bug fixes."}),(0,a.jsx)("button",{onClick:l,disabled:e,className:`clear-cache-btn inline-flex items-center text-lg px-8 py-4 text-white font-semibold rounded-lg ${e?"btn-processing":""}`,children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-3 w-5 h-5"}),"Clearing Cache..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-3"}),"Clear Cache & Data"]})}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-4",children:"✨ Recommended if you're experiencing issues or want the latest features"}),(0,a.jsxs)("div",{className:"mt-4 inline-flex items-center bg-white/10 rounded-full px-4 py-2 text-xs text-white/80",children:[(0,a.jsx)("i",{className:"fas fa-code-branch mr-2"}),"Version ",t," • Updated ","2025-01-19"]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-4 text-xs text-white/60",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-database mr-2"}),"Clears Storage"]}),(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-cookie-bite mr-2"}),"Removes Cookies"]}),(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-memory mr-2"}),"Clears Cache"]}),(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Fresh Download"]})]})]})}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[(0,a.jsxs)("a",{href:"https://wa.me/917676636990",target:"_blank",rel:"noopener noreferrer",className:"glass-card p-8 hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fab fa-whatsapp text-5xl text-green-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"WhatsApp Support"}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Get instant help via WhatsApp"}),(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:"+91 7676636990"}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"9 AM - 6 PM (Working days)"})]}),(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"glass-card p-8 hover:scale-105 transition-transform",children:[(0,a.jsx)("i",{className:"fas fa-envelope text-5xl text-blue-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Email Support"}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Send us detailed queries"}),(0,a.jsx)("p",{className:"text-blue-400 font-semibold",children:"<EMAIL>"}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"9 AM - 6 PM (Working days)"})]}),(0,a.jsx)(n.A,{variant:"homepage"})]})]})}),(0,a.jsx)("footer",{className:"py-12 px-4 border-t border-white/20",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto text-center",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,a.jsxs)("div",{className:"mb-4 md:mb-0",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"MyTube"}),(0,a.jsx)("p",{className:"text-white/60",children:"Earn money by watching videos"})]}),(0,a.jsxs)("div",{className:"text-white/60 text-sm",children:[(0,a.jsx)("p",{children:"\xa9 2024 MyTube. All rights reserved."}),(0,a.jsxs)("div",{className:"mt-2 flex items-center justify-center md:justify-end",children:[(0,a.jsx)("i",{className:"fas fa-code-branch mr-2 text-xs"}),(0,a.jsxs)("span",{className:"text-xs",children:["v",t]}),(0,a.jsx)("span",{className:"mx-2",children:"•"}),(0,a.jsx)("button",{onClick:l,disabled:e,className:"text-xs text-blue-400 hover:text-blue-300 transition-colors duration-200 underline",children:e?"Clearing...":"Clear Cache"})]})]})]})})})]})}},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87351:(e,s,t)=>{Promise.resolve().then(t.bind(t,21204))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[6204,7567,8441],()=>t(71653));module.exports=a})();