"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9567],{9567:(e,t,a)=>{a.d(t,{applyUserLeave:()=>u,cancelUserLeave:()=>v,createAdminLeave:()=>c,debugAdminLeaveStatus:()=>s,deleteAdminLeave:()=>d,getAdminLeaves:()=>l,getUserLeaves:()=>g,getUserMonthlyLeaveCount:()=>D,isAdminLeaveDay:()=>i,isUserOnLeave:()=>m,q8:()=>y});var o=a(6104),r=a(5317);let n={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function c(e){try{return(await (0,r.gS)((0,r.collection)(o.db,n.adminLeaves),{...e,date:r.Dc.fromDate(e.date),createdAt:r.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function l(){try{let e=(0,r.P)((0,r.collection)(o.db,n.adminLeaves),(0,r.My)("date","asc")),t=(await (0,r.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function s(){try{let e=new Date;console.log("\uD83D\uDD0D Debug: Checking admin leave status for today:",e.toDateString());let t=await i(e);console.log("\uD83D\uDCCA Debug: Admin leave result:",t);let a=await l();console.log("\uD83D\uDCC5 Debug: All admin leaves in database:",a);let o=a.filter(t=>t.date.toDateString()===e.toDateString());console.log("\uD83D\uDCC5 Debug: Today's admin leaves:",o)}catch(e){console.error("❌ Debug: Error checking admin leave status:",e)}}async function d(e){try{await (0,r.kd)((0,r.H9)(o.db,n.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function i(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let c=(0,r.P)((0,r.collection)(o.db,n.adminLeaves),(0,r._M)("date",">=",r.Dc.fromDate(t)),(0,r._M)("date","<=",r.Dc.fromDate(a))),l=await (0,r.getDocs)(c),s=!l.empty;return s?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),s}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function u(e){try{let t,a,c,l=new Date,s=l.getFullYear(),d=l.getMonth()+1,i=await D(e.userId,s,d),u="pending";return i<4&&(u="approved",t="system",c=r.Dc.now(),a="Auto-approved: ".concat(i+1,"/").concat(4," monthly leaves used")),{id:(await (0,r.gS)((0,r.collection)(o.db,n.userLeaves),{...e,date:r.Dc.fromDate(e.date),status:u,appliedAt:r.Dc.now(),...t&&{reviewedBy:t},...c&&{reviewedAt:c},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:i+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function g(e){try{let t=(0,r.P)((0,r.collection)(o.db,n.userLeaves),(0,r._M)("userId","==",e),(0,r.My)("date","desc"));return(await (0,r.getDocs)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:null==(t=e.data().reviewedAt)?void 0:t.toDate()}})}catch(e){throw console.error("Error getting user leaves:",e),e}}async function v(e){try{await (0,r.kd)((0,r.H9)(o.db,n.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function D(e,t,a){try{let c=new Date(t,a-1,1),l=new Date(t,a,0,23,59,59,999),s=(0,r.P)((0,r.collection)(o.db,n.userLeaves),(0,r._M)("userId","==",e),(0,r._M)("status","==","approved"),(0,r._M)("date",">=",r.Dc.fromDate(c)),(0,r._M)("date","<=",r.Dc.fromDate(l)));return(await (0,r.getDocs)(s)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function m(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let c=new Date(t);c.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",c.toISOString());let l=(0,r.P)((0,r.collection)(o.db,n.userLeaves),(0,r._M)("userId","==",e),(0,r._M)("status","==","approved"),(0,r._M)("date",">=",r.Dc.fromDate(a)),(0,r._M)("date","<=",r.Dc.fromDate(c))),s=await (0,r.getDocs)(l),d=!s.empty;return d?console.log("\uD83D\uDC64 Found user leave(s) for today:",s.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),d}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function y(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await i(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await m(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}}}]);