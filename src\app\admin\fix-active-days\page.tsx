'use client'

import { useState } from 'react'
import { useRequireAuth } from '@/hooks/useAuth'
import { fixAllUsersActiveDays, resetDailyVideoCount } from '@/lib/dataService'
import Swal from 'sweetalert2'

export default function FixActiveDaysPage() {
  const { user, loading } = useRequireAuth()
  const [isFixing, setIsFixing] = useState(false)
  const [isResetting, setIsResetting] = useState(false)
  const [results, setResults] = useState<any>(null)

  // Check if user is admin
  const isAdmin = user?.email === '<EMAIL>'

  const handleFixActiveDays = async () => {
    if (!isAdmin) {
      Swal.fire({
        icon: 'error',
        title: 'Access Denied',
        text: 'Only admin can perform this action.'
      })
      return
    }

    const result = await Swal.fire({
      icon: 'warning',
      title: 'Fix All Users Active Days',
      text: 'This will recalculate and update active days for all users. This may take a while. Continue?',
      showCancelButton: true,
      confirmButtonText: 'Yes, Fix All',
      cancelButtonText: 'Cancel'
    })

    if (!result.isConfirmed) return

    try {
      setIsFixing(true)
      const fixResults = await fixAllUsersActiveDays()
      setResults(fixResults)

      Swal.fire({
        icon: 'success',
        title: 'Active Days Fixed!',
        html: `
          <div class="text-left">
            <p><strong>Fixed:</strong> ${fixResults.fixedCount} users</p>
            <p><strong>Errors:</strong> ${fixResults.errorCount} users</p>
          </div>
        `,
        timer: 5000
      })
    } catch (error) {
      console.error('Error fixing active days:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to fix active days. Check console for details.'
      })
    } finally {
      setIsFixing(false)
    }
  }

  const handleResetAllDailyVideos = async () => {
    if (!isAdmin) {
      Swal.fire({
        icon: 'error',
        title: 'Access Denied',
        text: 'Only admin can perform this action.'
      })
      return
    }

    const result = await Swal.fire({
      icon: 'warning',
      title: 'Reset All Daily Video Counts',
      text: 'This will reset today\'s video count to 0 for all users. Continue?',
      showCancelButton: true,
      confirmButtonText: 'Yes, Reset All',
      cancelButtonText: 'Cancel'
    })

    if (!result.isConfirmed) return

    try {
      setIsResetting(true)
      
      // Get all users and reset their daily counts
      const { getDocs, collection } = await import('firebase/firestore')
      const { db } = await import('@/lib/firebase')
      const { COLLECTIONS } = await import('@/lib/dataService')
      
      const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
      let resetCount = 0
      let errorCount = 0

      for (const userDoc of usersSnapshot.docs) {
        try {
          await resetDailyVideoCount(userDoc.id)
          resetCount++
        } catch (error) {
          console.error(`Error resetting daily count for user ${userDoc.id}:`, error)
          errorCount++
        }
      }

      Swal.fire({
        icon: 'success',
        title: 'Daily Counts Reset!',
        html: `
          <div class="text-left">
            <p><strong>Reset:</strong> ${resetCount} users</p>
            <p><strong>Errors:</strong> ${errorCount} users</p>
          </div>
        `,
        timer: 5000
      })
    } catch (error) {
      console.error('Error resetting daily counts:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to reset daily counts. Check console for details.'
      })
    } finally {
      setIsResetting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-400 mb-4">Access Denied</h1>
          <p className="text-white mb-4">Only admin can access this page.</p>
          <a href="/admin" className="btn-primary">
            Back to Admin
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <div className="glass-card p-6">
          <h1 className="text-2xl font-bold text-white mb-6">
            <i className="fas fa-tools mr-2"></i>
            Fix Active Days & Daily Counts
          </h1>

          <div className="space-y-6">
            {/* Fix Active Days Section */}
            <div className="bg-white/10 rounded-lg p-4">
              <h2 className="text-xl font-semibold text-white mb-3">
                <i className="fas fa-calendar-check mr-2"></i>
                Fix Active Days
              </h2>
              <p className="text-white/80 mb-4">
                Recalculates and updates active days for all users based on their plan activation date and leave history.
              </p>
              <button
                onClick={handleFixActiveDays}
                disabled={isFixing}
                className={`btn-primary ${isFixing ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isFixing ? (
                  <>
                    <div className="spinner mr-2 w-4 h-4"></div>
                    Fixing Active Days...
                  </>
                ) : (
                  <>
                    <i className="fas fa-wrench mr-2"></i>
                    Fix All Users Active Days
                  </>
                )}
              </button>
            </div>

            {/* Reset Daily Videos Section */}
            <div className="bg-white/10 rounded-lg p-4">
              <h2 className="text-xl font-semibold text-white mb-3">
                <i className="fas fa-redo mr-2"></i>
                Reset Daily Video Counts
              </h2>
              <p className="text-white/80 mb-4">
                Resets today's video count to 0 for all users. Use this if daily counts are showing incorrect values.
              </p>
              <button
                onClick={handleResetAllDailyVideos}
                disabled={isResetting}
                className={`btn-secondary ${isResetting ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isResetting ? (
                  <>
                    <div className="spinner mr-2 w-4 h-4"></div>
                    Resetting Daily Counts...
                  </>
                ) : (
                  <>
                    <i className="fas fa-sync-alt mr-2"></i>
                    Reset All Daily Counts
                  </>
                )}
              </button>
            </div>

            {/* Results Section */}
            {results && (
              <div className="bg-green-500/20 border border-green-400/30 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-green-300 mb-2">
                  <i className="fas fa-check-circle mr-2"></i>
                  Last Operation Results
                </h3>
                <div className="text-white">
                  <p><strong>Fixed:</strong> {results.fixedCount} users</p>
                  <p><strong>Errors:</strong> {results.errorCount} users</p>
                </div>
              </div>
            )}

            {/* Back Button */}
            <div className="text-center">
              <a href="/admin" className="btn-secondary">
                <i className="fas fa-arrow-left mr-2"></i>
                Back to Admin Dashboard
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
