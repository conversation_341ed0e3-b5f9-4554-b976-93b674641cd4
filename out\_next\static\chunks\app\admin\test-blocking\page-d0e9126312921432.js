(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6393],{2719:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var i=a(5155),t=a(2115),n=a(6874),l=a.n(n),r=a(6681),c=a(3592),o=a(4752),d=a.n(o);function m(){let{user:e,loading:s,isAdmin:a}=(0,r.wC)(),[n,o]=(0,t.useState)(!1),m=async()=>{try{o(!0),await (0,c.z8)({title:"\uD83D\uDEA8 Important System Update",message:"This is a test notification. Users must acknowledge this message before they can continue using the platform. This ensures important announcements are seen by all users.",type:"warning",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),d().fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users. Users will need to acknowledge this before accessing any features.",timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error sending blocking notification:",e),d().fire({icon:"error",title:"Send Failed",text:"Failed to send blocking notification. Please try again."})}finally{o(!1)}},x=async()=>{try{o(!0),await (0,c.z8)({title:"Another Test Notification",message:"This is another test notification. All notifications are now blocking and users must acknowledge them.",type:"info",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),d().fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users.",timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error sending notification:",e),d().fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{o(!1)}};return s?(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):(0,i.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,i.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,i.jsx)("div",{className:"flex items-center justify-between px-6 py-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(l(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Blocking Notifications"})]})})}),(0,i.jsx)("div",{className:"p-6",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,i.jsxs)("h2",{className:"text-lg font-bold text-blue-900 mb-3",children:[(0,i.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Test Notifications"]}),(0,i.jsxs)("div",{className:"text-blue-800 space-y-2",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"1."})," Send a notification using the buttons below"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"2."})," Open a new tab and go to the user dashboard or work page"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"3."})," You should see a full-page modal that blocks all activities until acknowledged"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"4."}),' Click "Acknowledge" to dismiss the notification']}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"5."})," All notifications are now blocking/mandatory for better user engagement"]})]})]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,i.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 text-2xl"})}),(0,i.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDEA8 Warning Notification"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends a warning notification that users must acknowledge before continuing"}),(0,i.jsx)("button",{onClick:m,disabled:n,className:"w-full bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:n?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Warning Notification"]})})]})}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("i",{className:"fas fa-bell text-blue-600 text-2xl"})}),(0,i.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDCE2 Info Notification"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends an info notification that users must acknowledge before continuing"}),(0,i.jsx)("button",{onClick:x,disabled:n,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:n?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Info Notification"]})})]})})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mt-6",children:[(0,i.jsxs)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:[(0,i.jsx)("i",{className:"fas fa-check-circle mr-2 text-green-500"}),"Notification Features (All Blocking)"]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-shield-alt text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Blocks all user activities until acknowledged"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-eye text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Forces users to read important announcements"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-users text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Can target all users or specific users"})]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-mobile-alt text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Works on all pages (dashboard, work, wallet, etc.)"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-chart-line text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Progress indicator for multiple notifications"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-clock text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Persistent until user acknowledges"})]})]})]})]})]})})]})}},4552:(e,s,a)=>{Promise.resolve().then(a.bind(a,2719))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3592,6681,8441,1684,7358],()=>s(4552)),_N_E=e.O()}]);