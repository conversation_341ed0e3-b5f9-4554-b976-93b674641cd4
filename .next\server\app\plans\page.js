(()=>{var e={};e.id=481,e.ids=[481],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21265:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),i=t(43210),o=t(85814),n=t.n(o),a=t(87979),l=t(77567);let d=[{id:"trial",name:"Trial",price:0,duration:2,earningPerVideo:10,videoDuration:30,features:["2 days access","₹10 per batch (50 videos)","Basic support","Video duration: 30 seconds"]},{id:"starter",name:"Starter",price:499,duration:30,earningPerVideo:25,videoDuration:300,features:["30 days access","₹25 per batch (50 videos)","Priority support","Referral bonus: ₹50","Video duration: 5 minutes"]},{id:"basic",name:"Basic",price:1499,duration:30,earningPerVideo:75,videoDuration:300,features:["30 days access","₹75 per batch (50 videos)","Priority support","Referral bonus: ₹150","50 videos credited to total count","Video duration: 5 minutes"],popular:!0},{id:"premium",name:"Premium",price:2999,duration:30,earningPerVideo:150,videoDuration:300,features:["30 days access","₹150 per batch (50 videos)","Premium support","Referral bonus: ₹300","50 videos credited to total count","Video duration: 5 minutes"]},{id:"gold",name:"Gold",price:3999,duration:30,earningPerVideo:200,videoDuration:180,features:["30 days access","₹200 per batch (50 videos)","Premium support","Referral bonus: ₹400","50 videos credited to total count","Video duration: 3 minutes","Priority customer support"]},{id:"platinum",name:"Platinum",price:5999,duration:30,earningPerVideo:250,videoDuration:120,features:["30 days access","₹250 per batch (50 videos)","Premium support","Referral bonus: ₹700","50 videos credited to total count","Video duration: 2 minutes","Dedicated account manager","Early access to new features"]},{id:"diamond",name:"Diamond",price:9999,duration:30,earningPerVideo:400,videoDuration:60,features:["30 days access","₹400 per batch (50 videos)","VIP support","Referral bonus: ₹1200","50 videos credited to total count","Video duration: 1 minute","Dedicated account manager","Early access to new features","Exclusive earning opportunities"]}];function c(){let{user:e,loading:r}=(0,a.hD)(),[t,o]=(0,i.useState)(null),[c,u]=(0,i.useState)(!1),p=async r=>{if(!e)return void l.A.fire({icon:"info",title:"Login Required",text:"Please login to purchase a plan",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed&&(window.location.href="/login")});if("trial"===r.id)return void l.A.fire({icon:"info",title:"Trial Plan",text:"You are already on the trial plan. Upgrade to a paid plan for better earnings!"});o(r.id),u(!0);try{await new Promise(e=>setTimeout(e,2e3)),l.A.fire({icon:"info",title:"Payment Integration Required",html:`
          <p>To complete your purchase of the <strong>${r.name}</strong> plan (₹${r.price}), please contact our support team.</p>
          <br>
          <p><strong>Plan Details:</strong></p>
          <ul style="text-align: left; margin: 10px 0;">
            <li>Duration: ${r.duration} days</li>
            <li>Earning: ₹${r.earningPerVideo} per 50 videos</li>
            <li>Video duration: ${r.videoDuration<60?`${r.videoDuration} seconds`:`${Math.floor(r.videoDuration/60)} minute${r.videoDuration>=120?"s":""}`}</li>
          </ul>
          <br>
          <p><strong>Contact Options:</strong></p>
          <p>📧 Email: <strong><EMAIL></strong></p>
        `,confirmButtonText:"Contact Support",showCancelButton:!0,cancelButtonText:"Cancel"})}catch(e){console.error("Error processing plan selection:",e),l.A.fire({icon:"error",title:"Error",text:"Failed to process plan selection. Please try again."})}finally{u(!1),o(null)}};return r?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(n(),{href:e?"/dashboard":"/",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Choose Your Plan"}),(0,s.jsx)("div",{className:"w-20"})," "]})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Start Earning with MyTube"}),(0,s.jsx)("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Choose the perfect plan for your earning goals. Watch videos and earn money with our flexible pricing options."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6",children:d.map(e=>(0,s.jsxs)("div",{className:`glass-card p-8 relative ${e.popular?"ring-2 ring-yellow-400":""}`,children:[e.popular&&(0,s.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,s.jsx)("span",{className:"bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold",children:"Most Popular"})}),(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:e.name}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("span",{className:"text-4xl font-bold text-white",children:["₹",e.price]}),e.price>0&&(0,s.jsxs)("span",{className:"text-white/60 ml-2",children:["/ ",e.duration," days"]})]}),(0,s.jsxs)("p",{className:"text-green-400 font-semibold",children:["Earn ₹",e.earningPerVideo," per 50 videos"]})]}),(0,s.jsx)("ul",{className:"space-y-3 mb-8",children:e.features.map((e,r)=>(0,s.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,s.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),e]},r))}),(0,s.jsx)("button",{onClick:()=>p(e),disabled:c&&t===e.id,className:`w-full py-3 rounded-lg font-semibold transition-all duration-300 ${e.popular?"bg-yellow-400 text-black hover:bg-yellow-500":0===e.price?"bg-gray-600 text-white hover:bg-gray-700":"bg-youtube-red text-white hover:bg-red-700"} disabled:opacity-50`,children:c&&t===e.id?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5 inline-block"}),"Processing..."]}):0===e.price?"Current Plan":`Choose ${e.name}`})]},e.id))}),(0,s.jsxs)("div",{className:"mt-12 glass-card p-8",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Plan Benefits Explained"]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Earning Structure"}),(0,s.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,s.jsx)("li",{children:"• Watch 50 videos daily to earn the full amount"}),(0,s.jsx)("li",{children:"• Each video must be watched for the full duration"}),(0,s.jsx)("li",{children:"• Earnings are credited to your earning wallet"}),(0,s.jsx)("li",{children:"• Transfer earnings to main wallet for withdrawal"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Video Duration Benefits"}),(0,s.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,s.jsx)("li",{children:"• Higher plans have shorter video durations"}),(0,s.jsx)("li",{children:"• Complete daily targets faster with premium plans"}),(0,s.jsx)("li",{children:"• Video duration is fixed per plan"}),(0,s.jsx)("li",{children:"• All videos must be watched completely"})]})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 text-center",children:[(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"Need help choosing a plan? Contact us during business hours (9 AM - 6 PM, working days):"}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center text-white hover:text-blue-400 transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-envelope mr-2"}),"<EMAIL>"]})})]})]})]})}},21820:e=>{"use strict";e.exports=require("os")},26147:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),i=t(48088),o=t(88170),n=t.n(o),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["plans",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,41587)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\plans\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\plans\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/plans/page",pathname:"/plans",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41587:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\plans\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\plans\\page.tsx","default")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85613:(e,r,t)=>{Promise.resolve().then(t.bind(t,21265))},87821:(e,r,t)=>{Promise.resolve().then(t.bind(t,41587))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6204,6958,7567,8441,7979],()=>t(26147));module.exports=s})();