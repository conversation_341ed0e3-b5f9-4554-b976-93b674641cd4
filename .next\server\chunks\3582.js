"use strict";exports.id=3582,exports.ids=[3582],exports.modules={3582:(e,t,a)=>{a.d(t,{AX:()=>F,COLLECTIONS:()=>n,FIELD_NAMES:()=>i,GA:()=>b,Gl:()=>H,HY:()=>w,I0:()=>g,II:()=>B,IK:()=>I,Kc:()=>k,Oe:()=>h,Q6:()=>N,QD:()=>W,Ss:()=>_,_f:()=>P,addTransaction:()=>u,b6:()=>l,bA:()=>G,calculateUserActiveDays:()=>v,checkAndRunDailyProcess:()=>D,fP:()=>C,getLiveActiveDays:()=>m,getPlanValidityDays:()=>S,getUserData:()=>s,getVideoCountData:()=>d,getWalletData:()=>c,gj:()=>A,gx:()=>E,i8:()=>z,iA:()=>Q,isUserPlanExpired:()=>U,mm:()=>q,mv:()=>Z,pl:()=>y,pu:()=>Y,submitBatchVideos:()=>f,ul:()=>L,updateUserActiveDays:()=>p,updateWalletBalance:()=>V,w1:()=>T,wD:()=>$,wT:()=>R,x4:()=>J,xj:()=>O,z8:()=>M,zb:()=>x});var r=a(75535),o=a(33784);let i={name:"name",email:"email",mobile:"mobile",referralCode:"referralCode",referredBy:"referredBy",referralBonusCredited:"referralBonusCredited",plan:"plan",planExpiry:"planExpiry",activeDays:"activeDays",joinedDate:"joinedDate",wallet:"wallet",bankAccountHolderName:"bankAccountHolderName",bankAccountNumber:"bankAccountNumber",bankIfscCode:"bankIfscCode",bankName:"bankName",bankDetailsUpdated:"bankDetailsUpdated",totalVideos:"totalVideos",todayVideos:"todayVideos",lastVideoDate:"lastVideoDate",videoDuration:"videoDuration",quickVideoAdvantage:"quickVideoAdvantage",quickVideoAdvantageExpiry:"quickVideoAdvantageExpiry",quickVideoAdvantageDays:"quickVideoAdvantageDays",quickVideoAdvantageRemainingDays:"quickVideoAdvantageRemainingDays",quickVideoAdvantageSeconds:"quickVideoAdvantageSeconds",quickVideoAdvantageGrantedBy:"quickVideoAdvantageGrantedBy",quickVideoAdvantageGrantedAt:"quickVideoAdvantageGrantedAt",manuallySetActiveDays:"manuallySetActiveDays",lastActiveDaysUpdate:"lastActiveDaysUpdate",type:"type",amount:"amount",date:"date",status:"status",description:"description",userId:"userId"},n={users:"users",transactions:"transactions",withdrawals:"withdrawals",plans:"plans",settings:"settings",notifications:"notifications",adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserData:",e),null;let t=await (0,r.x7)((0,r.H9)(o.db,n.users,e));if(t.exists()){let e=t.data(),a={name:String(e[i.name]||""),email:String(e[i.email]||""),mobile:String(e[i.mobile]||""),referralCode:String(e[i.referralCode]||""),referredBy:String(e[i.referredBy]||""),plan:String(e[i.plan]||"Trial"),planExpiry:e[i.planExpiry]?.toDate()||null,activeDays:Number(e[i.activeDays]||0),joinedDate:e[i.joinedDate]?.toDate()||new Date,videoDuration:Number(e[i.videoDuration]||("Trial"===e[i.plan]?30:300)),quickVideoAdvantage:!!e[i.quickVideoAdvantage],quickVideoAdvantageExpiry:e[i.quickVideoAdvantageExpiry]?.toDate()||null,quickVideoAdvantageDays:Number(e[i.quickVideoAdvantageDays]||0),quickVideoAdvantageRemainingDays:Number(e[i.quickVideoAdvantageRemainingDays]||0),quickVideoAdvantageSeconds:Number(e[i.quickVideoAdvantageSeconds]||30),quickVideoAdvantageGrantedBy:String(e[i.quickVideoAdvantageGrantedBy]||""),quickVideoAdvantageGrantedAt:e[i.quickVideoAdvantageGrantedAt]?.toDate()||null};return console.log("getUserData result:",a),a}return null}catch(e){return console.error("Error getting user data:",e),null}}async function c(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getWalletData:",e),{wallet:0};let t=await (0,r.x7)((0,r.H9)(o.db,n.users,e));if(t.exists()){let e=t.data(),a={wallet:Number(e[i.wallet]||0)};return console.log("getWalletData result:",a),a}return{wallet:0}}catch(e){return console.error("Error getting wallet data:",e),{wallet:0}}}async function d(e){try{let t=(0,r.H9)(o.db,n.users,e),a=await (0,r.x7)(t);if(a.exists()){let o=a.data(),n=o[i.totalVideos]||0,s=o[i.todayVideos]||0,c=o[i.lastVideoDate]?.toDate(),d=new Date;if((!c||c.toDateString()!==d.toDateString())&&s>0){console.log(`🔄 Resetting daily video count for user ${e} (was ${s})`),await (0,r.mZ)(t,{[i.todayVideos]:0}),s=0;try{await p(e)}catch(e){console.error("Error updating active days during daily reset:",e)}try{await D()}catch(e){console.error("Error in daily process check:",e)}}return{totalVideos:n,todayVideos:s,remainingVideos:Math.max(0,50-s)}}return{totalVideos:0,todayVideos:0,remainingVideos:50}}catch(e){throw console.error("Error getting video count data:",e),e}}async function l(e,t){try{await (0,r.mZ)((0,r.H9)(o.db,n.users,e),t)}catch(e){throw console.error("Error updating user data:",e),e}}async function u(e,t){try{let a={[i.userId]:e,[i.type]:t.type,[i.amount]:t.amount,[i.description]:t.description,[i.status]:t.status||"completed",[i.date]:r.Dc.now()};await (0,r.gS)((0,r.collection)(o.db,n.transactions),a)}catch(e){throw console.error("Error adding transaction:",e),e}}async function g(e,t=10){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getTransactions:",e),[];let a=(0,r.P)((0,r.collection)(o.db,n.transactions),(0,r._M)(i.userId,"==",e),(0,r.AB)(t)),s=(await (0,r.getDocs)(a)).docs.map(e=>({id:e.id,...e.data(),date:e.data()[i.date]?.toDate()}));return s.sort((e,t)=>{let a=e.date||new Date(0);return(t.date||new Date(0)).getTime()-a.getTime()}),s}catch(e){return console.error("Error getting transactions:",e),[]}}async function y(e){try{let t=(0,r.P)((0,r.collection)(o.db,n.users),(0,r._M)(i.referredBy,"==",e));return(await (0,r.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[i.joinedDate]?.toDate()}))}catch(e){throw console.error("Error getting referrals:",e),e}}async function f(e,t=50){try{let a,s;if(50!==t)throw Error(`Invalid batch size: ${t}. Expected exactly 50 videos.`);let c=new Date,d=(0,r.H9)(o.db,n.users,e),l=await (0,r.x7)(d);if(!l.exists())throw Error("User not found");let u=l.data(),g=u[i.lastVideoDate]?.toDate(),y=u[i.todayVideos]||0,f=u[i.totalVideos]||0;if(y>=50)throw Error("Daily video limit already reached. Cannot submit more videos today.");return(!g||g.toDateString()!==c.toDateString())&&y>0?(console.log(`🔄 Resetting daily count and submitting batch for user ${e}`),a=50):a=Math.min(y+50,50),s=f+50,await (0,r.mZ)(d,{[i.totalVideos]:s,[i.todayVideos]:a,[i.lastVideoDate]:r.Dc.fromDate(c)}),console.log(`✅ Batch submission successful for user ${e}: +50 videos (Total: ${s}, Today: ${a})`),{totalVideos:s,todayVideos:a,videosAdded:50}}catch(e){throw console.error("Error submitting batch videos:",e),e}}async function w(e){try{let t=(0,r.H9)(o.db,n.users,e);await (0,r.mZ)(t,{[i.todayVideos]:0}),console.log(`✅ Reset daily video count for user ${e}`)}catch(e){throw console.error("Error resetting daily video count:",e),e}}async function m(e){try{console.log(`🔍 Getting live active days for user ${e}`);let t=await s(e);if(!t)return console.error("User data not found for live active days:",e),1;let a=t.activeDays||1;return console.log(`📊 Live active days for user ${e}: ${a}`),a}catch(e){return console.error("Error getting live active days:",e),1}}async function v(e){try{let t=await s(e);if(!t)return console.error("User data not found for active days calculation:",e),1;let r=new Date,o=1;if("Trial"===t.plan){let e=t.joinedDate||new Date,a=Math.floor((r.getTime()-e.getTime())/864e5);o=Math.max(1,a+1)}else{let i=t.planExpiry?new Date(t.planExpiry.getTime()-24*S(t.plan)*36e5):t.joinedDate||new Date,n=Math.floor((r.getTime()-i.getTime())/864e5),{isAdminLeaveDay:s,isUserOnLeave:c}=await a.e(7087).then(a.bind(a,87087)),d=0;for(let t=0;t<=n;t++){let a=new Date(i.getTime()+24*t*36e5),r=await s(a),o=await c(e,a);(r||o)&&d++}o=Math.max(1,n-d+1)}return o}catch(e){return console.error("Error calculating user active days:",e),1}}async function p(e,t=!1){try{let a,c=await s(e);if(!c)return console.error("User data not found for active days update:",e),1;let d=(0,r.H9)(o.db,n.users,e),l=(await (0,r.x7)(d)).data(),u=l?.manuallySetActiveDays||!1;if(u&&!t)return console.log(`⏭️ Skipping active days auto-update for user ${e} - manually set by admin (current: ${c.activeDays})`),c.activeDays||1;u&&t?(console.log(`⚠️ Force update requested but active days manually set for user ${e} - keeping current value`),a=c.activeDays||1):a=await v(e);let g=c.activeDays||0;return a!==g&&(console.log(`📅 Updating active days for user ${e}: ${g} → ${a}`),await (0,r.mZ)(d,{[i.activeDays]:a})),a}catch(e){throw console.error("Error updating user active days:",e),e}}async function D(){try{let e=new Date().toDateString(),t=await (0,r.x7)((0,r.H9)(o.db,"system","dailyReset")),a=t.exists()?t.data()?.lastResetDate:null;if(a&&a===e)return console.log("⏭️ Daily process already completed today"),null;{let t=1;if(a){let e=new Date(a),r=new Date().getTime()-e.getTime();t=Math.floor(r/864e5)}console.log(`🌅 Running daily process for all users (${t} day(s) catchup)...`);let i=await h();try{let a=(0,r.H9)(o.db,"system","dailyReset");await (0,r.BN)(a,{lastResetDate:e,lastResetTimestamp:r.Dc.now(),lastResult:i,daysCaughtUp:t,triggeredBy:"automatic_daily_check"},{merge:!0}),console.log(`✅ Daily process completed for ${t} day(s):`,i)}catch(e){console.error("Error updating daily process tracking:",e)}return i}}catch(e){throw console.error("Error in centralized daily process:",e),e}}async function h(){try{console.log("\uD83C\uDF05 Starting daily active days increment...");let e=new Date,t=e.toDateString(),{isAdminLeaveDay:s}=await a.e(7087).then(a.bind(a,87087));if(await s(e))return console.log("⏸️ Skipping active days increment - Admin leave day"),{incrementedCount:0,skippedCount:0,errorCount:0,reason:"Admin leave day"};let c=await (0,r.getDocs)((0,r.collection)(o.db,n.users)),d=0,l=0,g=0;for(let s of c.docs)try{let c=s.data(),g=s.id,y=c[i.lastActiveDaysUpdate]?.toDate();if(y&&y.toDateString()===t){l++;continue}let{isUserOnLeave:f}=await a.e(7087).then(a.bind(a,87087));if(await f(g,e)){console.log(`⏸️ Skipping active days increment for user ${g} - User leave day`),await (0,r.mZ)((0,r.H9)(o.db,n.users,g),{[i.lastActiveDaysUpdate]:r.Dc.fromDate(e)}),l++;continue}let w=c[i.activeDays]||1,m=w+1;console.log(`📅 Daily active days increment for user ${g}: ${w} → ${m}`);let v={[i.activeDays]:m,[i.lastActiveDaysUpdate]:r.Dc.fromDate(e)},p=c[i.quickVideoAdvantage]||!1,D=c[i.quickVideoAdvantageRemainingDays]||0;if(p&&D>0){let e=D-1;if(v[i.quickVideoAdvantageRemainingDays]=e,e<=0){v[i.quickVideoAdvantage]=!1,v[i.quickVideoAdvantageExpiry]=null,console.log(`⏰ Quick video advantage expired for user ${g}`);try{await u(g,{type:"quick_advantage_expired",amount:0,description:"Quick video advantage expired (time limit reached)"})}catch(e){console.error("Error adding expiry transaction:",e)}}else console.log(`⏰ Quick video advantage for user ${g}: ${D} → ${e} days remaining`)}await (0,r.mZ)((0,r.H9)(o.db,n.users,g),v),d++,console.log(`📅 Updated active days for user ${g}: ${w} → ${m}`)}catch(e){console.error(`Error updating active days for user ${s.id}:`,e),g++}return console.log(`✅ Daily active days increment completed: ${d} incremented, ${l} skipped, ${g} errors`),{incrementedCount:d,skippedCount:l,errorCount:g}}catch(e){throw console.error("Error in daily active days increment:",e),e}}async function b(){try{console.log("\uD83D\uDD04 Starting quick video advantage system migration...");let e=await (0,r.getDocs)((0,r.collection)(o.db,n.users)),t=0,a=0,s=0;for(let c of e.docs)try{let e=c.data(),s=c.id;if(!e[i.quickVideoAdvantage]||void 0!==e[i.quickVideoAdvantageRemainingDays]){a++;continue}let d=0,l=e[i.quickVideoAdvantageExpiry]?.toDate();if(l){let e=new Date,t=l.getTime()-e.getTime();d=Math.max(0,Math.ceil(t/864e5))}let u={[i.quickVideoAdvantageRemainingDays]:d};d<=0&&(u[i.quickVideoAdvantage]=!1,u[i.quickVideoAdvantageExpiry]=null),await (0,r.mZ)((0,r.H9)(o.db,n.users,s),u),t++,console.log(`✅ Migrated user ${s}: ${d} days remaining`)}catch(e){console.error(`Error migrating user ${c.id}:`,e),s++}return console.log(`✅ Quick video advantage migration completed: ${t} migrated, ${a} skipped, ${s} errors`),{migratedCount:t,skippedCount:a,errorCount:s}}catch(e){throw console.error("Error migrating quick video advantage system:",e),e}}async function A(){try{console.log("\uD83D\uDD27 Starting to fix all users active days...");let e=await (0,r.getDocs)((0,r.collection)(o.db,n.users)),t=0,a=0;for(let r of e.docs)try{await p(r.id,!0),t++}catch(e){console.error(`Error fixing active days for user ${r.id}:`,e),a++}return console.log(`✅ Fixed active days for ${t} users, ${a} errors`),{fixedCount:t,errorCount:a}}catch(e){throw console.error("Error fixing all users active days:",e),e}}async function k(){try{console.log("\uD83D\uDD04 Starting to recalculate all users active days with centralized formula...");let e=await (0,r.getDocs)((0,r.collection)(o.db,n.users)),t=0,a=0;for(let s of e.docs)try{let e=s.data(),a=s.id,c=await v(a),d=e.activeDays||0;c!==d&&(await (0,r.mZ)((0,r.H9)(o.db,n.users,a),{[i.activeDays]:c,[i.manuallySetActiveDays]:!1}),console.log(`📅 Recalculated active days for user ${a}: ${d} → ${c}`),t++)}catch(e){console.error(`Error recalculating active days for user ${s.id}:`,e),a++}return console.log(`✅ Recalculated active days for ${t} users, ${a} errors`),{recalculatedCount:t,errorCount:a}}catch(e){throw console.error("Error recalculating all users active days:",e),e}}async function E(){try{console.log("\uD83D\uDE80 Starting forced daily process catchup for all users...");let e=await (0,r.x7)((0,r.H9)(o.db,"system","dailyReset")),t=e.exists()?e.data()?.lastResetDate:null;console.log("Last daily process date:",t);let a=await h(),i=new Date().toDateString(),n=(0,r.H9)(o.db,"system","dailyReset");return await (0,r.BN)(n,{lastResetDate:i,lastResetTimestamp:r.Dc.now(),lastResult:a,forcedCatchup:!0,forcedCatchupTimestamp:r.Dc.now()},{merge:!0}),console.log("✅ Forced daily process catchup completed:",a),a}catch(e){throw console.error("Error in forced daily process catchup:",e),e}}async function $(){try{console.log("\uD83D\uDD04 Resetting all users lastActiveDaysUpdate field...");let e=await (0,r.getDocs)((0,r.collection)(o.db,n.users)),t=0,a=0;for(let s of e.docs)try{let e=s.id;await (0,r.mZ)((0,r.H9)(o.db,n.users,e),{[i.lastActiveDaysUpdate]:null}),t++,console.log(`✅ Reset lastActiveDaysUpdate for user ${e}`)}catch(e){console.error(`Error resetting lastActiveDaysUpdate for user ${s.id}:`,e),a++}return console.log(`✅ Reset lastActiveDaysUpdate for ${t} users, ${a} errors`),{resetCount:t,errorCount:a}}catch(e){throw console.error("Error resetting all users lastActiveDaysUpdate:",e),e}}async function V(e,t){try{let a=(0,r.H9)(o.db,n.users,e);await (0,r.mZ)(a,{[i.wallet]:(0,r.GV)(t)})}catch(e){throw console.error("Error updating wallet balance:",e),e}}async function q(e,t){try{if(!e||"string"!=typeof e)throw Error("Invalid userId provided");var a=t;let{accountHolderName:s,accountNumber:c,ifscCode:d,bankName:l}=a;if(!s||s.trim().length<2)throw Error("Account holder name must be at least 2 characters long");if(!c||!/^\d{9,18}$/.test(c.trim()))throw Error("Account number must be 9-18 digits");if(!d||!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(d.trim().toUpperCase()))throw Error("Invalid IFSC code format (e.g., SBIN0001234)");if(!l||l.trim().length<2)throw Error("Bank name must be at least 2 characters long");let u=(0,r.H9)(o.db,n.users,e);await (0,r.mZ)(u,{[i.bankAccountHolderName]:t.accountHolderName.trim(),[i.bankAccountNumber]:t.accountNumber.trim(),[i.bankIfscCode]:t.ifscCode.trim().toUpperCase(),[i.bankName]:t.bankName.trim(),[i.bankDetailsUpdated]:r.Dc.now()}),console.log("Bank details saved successfully for user:",e)}catch(e){throw console.error("Error saving bank details:",e),e}}async function x(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getBankDetails:",e),null;let t=await (0,r.x7)((0,r.H9)(o.db,n.users,e));if(t.exists()){let e=t.data();if(e[i.bankAccountNumber]){let t={accountHolderName:String(e[i.bankAccountHolderName]||""),accountNumber:String(e[i.bankAccountNumber]||""),ifscCode:String(e[i.bankIfscCode]||""),bankName:String(e[i.bankName]||"")};return console.log("getBankDetails result found"),t}}return console.log("No bank details found for user"),null}catch(e){return console.error("Error getting bank details:",e),null}}function S(e){return({Trial:2,Starter:30,Basic:30,Premium:30,Gold:30,Platinum:30,Diamond:30,499:30,1499:30,2999:30,3999:30,5999:30,9999:30})[e]||2}async function U(e){try{let t=await s(e);if(!t)return{expired:!0,reason:"User data not found"};let a=await m(e);if("Trial"===t.plan){let e=Math.max(0,3-a);return{expired:a>=3,reason:a>=3?"Trial period expired":void 0,daysLeft:e,activeDays:a}}if(t.planExpiry){let e=new Date,r=e>t.planExpiry,o=r?0:Math.ceil((t.planExpiry.getTime()-e.getTime())/864e5);return{expired:r,reason:r?"Plan subscription expired":void 0,daysLeft:o,activeDays:a}}let r=S(t.plan),o=Math.max(0,r+1-a),i=a>=r;return{expired:i,reason:i?`Plan validity period (${r} days) exceeded based on active days`:void 0,daysLeft:o,activeDays:a}}catch(e){return console.error("Error checking plan expiry:",e),{expired:!0,reason:"Error checking plan status"}}}async function B(e,t,a){try{let s=(0,r.H9)(o.db,n.users,e);if("Trial"===t)await (0,r.mZ)(s,{[i.planExpiry]:null});else{let o;if(a)o=a;else{let e=S(t),a=new Date;o=new Date(a.getTime()+24*e*36e5)}await (0,r.mZ)(s,{[i.planExpiry]:r.Dc.fromDate(o)}),console.log(`Updated plan expiry for user ${e} to ${o.toDateString()}`)}}catch(e){throw console.error("Error updating plan expiry:",e),e}}async function I(e,t,a){try{if("Trial"!==t||"Trial"===a)return void console.log("Referral bonus only applies when upgrading from Trial to paid plan");console.log(`Processing referral bonus for user ${e} upgrading from ${t} to ${a}`);let s=await (0,r.x7)((0,r.H9)(o.db,n.users,e));if(!s.exists())return void console.log("User not found");let c=s.data(),d=c[i.referredBy],l=c[i.referralBonusCredited];if(!d)return void console.log("User was not referred by anyone, skipping bonus processing");if(l)return void console.log("Referral bonus already credited for this user, skipping");console.log("Finding referrer with code:",d);let g=(0,r.P)((0,r.collection)(o.db,n.users),(0,r._M)(i.referralCode,"==",d),(0,r.AB)(1)),y=await (0,r.getDocs)(g);if(y.empty)return void console.log("Referral code not found:",d);let f=y.docs[0].id,w={Trial:0,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200}[a]||0;if(console.log(`Found referrer: ${f}, bonus amount: ₹${w}`),w>0){await V(f,w);let t=(0,r.H9)(o.db,n.users,f);await (0,r.mZ)(t,{[i.totalVideos]:(0,r.GV)(50)});let s=(0,r.H9)(o.db,n.users,e);await (0,r.mZ)(s,{[i.referralBonusCredited]:!0}),await u(f,{type:"referral_bonus",amount:w,description:`Referral bonus for ${a} plan upgrade + 50 bonus videos (User: ${c[i.name]})`}),console.log(`✅ Referral bonus processed: ₹${w} + 50 videos for referrer ${f}`)}else console.log("No bonus amount calculated, skipping")}catch(e){console.error("❌ Error processing referral bonus:",e)}}async function N(e){try{var t;let a=await s(e);if(!a)return{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1};let r=!!(t=a).quickVideoAdvantage&&(void 0!==t.quickVideoAdvantageRemainingDays?t.quickVideoAdvantageRemainingDays>0:!!t.quickVideoAdvantageExpiry&&new Date<t.quickVideoAdvantageExpiry),o=a.videoDuration;return r?o=a.quickVideoAdvantageSeconds||30:o&&"Trial"!==a.plan||(o=({Trial:30,Starter:300,Basic:300,Premium:300,Gold:180,Platinum:120,Diamond:60})[a.plan]||30),{videoDuration:o,earningPerBatch:({Trial:10,Starter:25,Basic:75,Premium:150,Gold:200,Platinum:250,Diamond:400})[a.plan]||10,plan:a.plan,hasQuickAdvantage:r,quickAdvantageExpiry:a.quickVideoAdvantageExpiry}}catch(e){return console.error("Error getting user video settings:",e),{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1}}}async function T(e,t,a,s=30){try{if(t<=0||t>365)throw Error("Days must be between 1 and 365");if(s<1||s>420)throw Error("Seconds must be between 1 and 420 (7 minutes)");let c=new Date,d=new Date(c.getTime()+24*t*36e5),l=(0,r.H9)(o.db,n.users,e);return await (0,r.mZ)(l,{[i.quickVideoAdvantage]:!0,[i.quickVideoAdvantageExpiry]:r.Dc.fromDate(d),[i.quickVideoAdvantageDays]:t,[i.quickVideoAdvantageRemainingDays]:t,[i.quickVideoAdvantageSeconds]:s,[i.quickVideoAdvantageGrantedBy]:a,[i.quickVideoAdvantageGrantedAt]:r.Dc.fromDate(c)}),console.log(`Granted quick video advantage to user ${e} for ${t} days until ${d.toDateString()}`),await u(e,{type:"quick_advantage_granted",amount:0,description:`Quick video advantage granted for ${t} days by ${a}`}),{success:!0,expiry:d}}catch(e){throw console.error("Error granting quick video advantage:",e),e}}async function R(e,t){try{let a=(0,r.H9)(o.db,n.users,e);return await (0,r.mZ)(a,{[i.quickVideoAdvantage]:!1,[i.quickVideoAdvantageExpiry]:null,[i.quickVideoAdvantageDays]:0,[i.quickVideoAdvantageRemainingDays]:0,[i.quickVideoAdvantageSeconds]:30,[i.quickVideoAdvantageGrantedBy]:"",[i.quickVideoAdvantageGrantedAt]:null}),console.log(`Removed quick video advantage from user ${e}`),await u(e,{type:"quick_advantage_removed",amount:0,description:`Quick video advantage removed by ${t}`}),{success:!0}}catch(e){throw console.error("Error removing quick video advantage:",e),e}}async function H(e,t){try{let a=[1,10,30].includes(t),s=t>=60&&t<=420;if(!a&&!s)throw Error("Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration");let c=(0,r.H9)(o.db,n.users,e);await (0,r.mZ)(c,{[i.videoDuration]:t}),console.log(`Updated video duration for user ${e} to ${t} seconds`)}catch(e){throw console.error("Error updating user video duration:",e),e}}async function M(e){try{let t={title:e.title,message:e.message,type:e.type,targetUsers:e.targetUsers,userIds:e.userIds||[],createdAt:r.Dc.now(),createdBy:e.createdBy};console.log("Adding notification to Firestore:",t);let a=await (0,r.gS)((0,r.collection)(o.db,n.notifications),t);console.log("Notification added successfully with ID:",a.id);let i=await (0,r.x7)(a);return i.exists()?console.log("Notification verified in database:",i.data()):console.warn("Notification not found after adding"),a.id}catch(e){throw console.error("Error adding notification:",e),e}}async function _(e,t=20){try{let a,i;if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserNotifications:",e),[];console.log(`Loading notifications for user: ${e}`);try{let e=(0,r.P)((0,r.collection)(o.db,n.notifications),(0,r._M)("targetUsers","==","all"),(0,r.My)("createdAt","desc"),(0,r.AB)(t));a=await (0,r.getDocs)(e),console.log(`Found ${a.docs.length} notifications for all users`)}catch(i){console.warn("Error querying all users notifications, trying without orderBy:",i);let e=(0,r.P)((0,r.collection)(o.db,n.notifications),(0,r._M)("targetUsers","==","all"),(0,r.AB)(t));a=await (0,r.getDocs)(e)}try{let a=(0,r.P)((0,r.collection)(o.db,n.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.My)("createdAt","desc"),(0,r.AB)(t));i=await (0,r.getDocs)(a),console.log(`Found ${i.docs.length} notifications for specific user`)}catch(s){console.warn("Error querying specific user notifications, trying without orderBy:",s);let a=(0,r.P)((0,r.collection)(o.db,n.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.AB)(t));i=await (0,r.getDocs)(a)}let s=[];a.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),i.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),s.sort((e,t)=>t.createdAt.getTime()-e.createdAt.getTime());let c=s.slice(0,t);return console.log(`Returning ${c.length} total notifications for user`),c}catch(e){return console.error("Error getting user notifications:",e),[]}}async function P(e=50){try{let t=(0,r.P)((0,r.collection)(o.db,n.notifications),(0,r.My)("createdAt","desc"),(0,r.AB)(e));return(await (0,r.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date}))}catch(e){return console.error("Error getting all notifications:",e),[]}}async function C(e){try{if(!e||"string"!=typeof e)throw Error("Invalid notification ID provided");console.log("Deleting notification:",e),await (0,r.kd)((0,r.H9)(o.db,n.notifications,e)),console.log("Notification deleted successfully")}catch(e){throw console.error("Error deleting notification:",e),e}}async function G(e,t){try{let a=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");a.includes(e)||(a.push(e),localStorage.setItem(`read_notifications_${t}`,JSON.stringify(a)))}catch(e){console.error("Error marking notification as read:",e)}}function Z(e,t){try{return JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]").includes(e)}catch(e){return console.error("Error checking notification read status:",e),!1}}function L(e,t){try{let a=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");return e.filter(e=>!a.includes(e.id)).length}catch(e){return console.error("Error getting unread notification count:",e),0}}async function F(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUnreadNotifications:",e),[];console.log(`Loading unread notifications for user: ${e}`);let t=await _(e,50),a=JSON.parse(localStorage.getItem(`read_notifications_${e}`)||"[]"),r=t.filter(e=>e.id&&!a.includes(e.id));return console.log(`Found ${r.length} unread notifications`),r}catch(e){return console.error("Error getting unread notifications:",e),[]}}async function Q(e){try{return(await F(e)).length>0}catch(e){return console.error("Error checking for unread notifications:",e),!1}}async function j(e){try{let t=(0,r.P)((0,r.collection)(o.db,n.withdrawals),(0,r._M)("userId","==",e),(0,r._M)("status","==","pending"),(0,r.AB)(1));return!(await (0,r.getDocs)(t)).empty}catch(e){return console.error("Error checking pending withdrawals:",e),!1}}async function W(e){try{let t=await s(e);if(!t)return{allowed:!1,reason:"Unable to verify user information. Please try again."};if("Trial"===t.plan)return{allowed:!1,reason:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals."};if(await j(e))return{allowed:!1,reason:"You have a pending withdrawal request. Please wait for it to be processed before submitting a new request."};let r=new Date,o=r.getHours();if(o<10||o>=18)return{allowed:!1,reason:"Withdrawals are only allowed between 10:00 AM to 6:00 PM"};let{isAdminLeaveDay:i}=await a.e(7087).then(a.bind(a,87087));if(await i(r))return{allowed:!1,reason:"Withdrawals are not allowed on admin leave/holiday days"};let{isUserOnLeave:n}=await a.e(7087).then(a.bind(a,87087));if(await n(e,r))return{allowed:!1,reason:"Withdrawals are not allowed on your leave days"};return{allowed:!0}}catch(e){return console.error("Error checking withdrawal allowed:",e),{allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."}}}async function O(e,t,a){try{if(t<50)throw Error("Minimum withdrawal amount is ₹50");let s=await (0,r.c4)(o.db,async s=>{let c=(0,r.H9)(o.db,n.users,e),d=await s.get(c);if(!d.exists())throw Error("User not found");let l=d.data()[i.wallet]||0,u=await W(e);if(!u.allowed)throw Error(u.reason);if(l<t)throw Error(`Insufficient wallet balance. Available: ₹${l}, Requested: ₹${t}`);let g=l-t;s.update(c,{[i.wallet]:g});let y={userId:e,amount:t,bankDetails:a,status:"pending",date:r.Dc.now(),createdAt:r.Dc.now()},f=(0,r.H9)((0,r.collection)(o.db,n.withdrawals));s.set(f,y);let w={userId:e,type:"withdrawal_request",amount:-t,description:`Withdrawal request submitted - ₹${t} debited from wallet`,date:r.Dc.now(),balanceAfter:g},m=(0,r.H9)((0,r.collection)(o.db,n.transactions));return s.set(m,w),f.id});return console.log(`✅ Withdrawal request created successfully: ${s}`),s}catch(e){throw console.error("Error creating withdrawal request:",e),e}}async function z(e,t=20){try{let a=(0,r.P)((0,r.collection)(o.db,n.withdrawals),(0,r._M)("userId","==",e),(0,r.My)("date","desc"),(0,r.AB)(t));return(await (0,r.getDocs)(a)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}))}catch(e){return console.error("Error getting user withdrawals:",e),[]}}async function J(){try{try{let e=(0,r.collection)(o.db,n.users),t=((await (0,r.d_)(e)).data().count+1).toString().padStart(4,"0");return`MYN${t}`}catch(a){console.warn("Failed to get count from server, using fallback method:",a);let e=Date.now().toString().slice(-4),t=Math.random().toString(36).substring(2,4).toUpperCase();return`MYN${e}${t}`}}catch(t){console.error("Error generating unique referral code:",t);let e=Date.now().toString().slice(-4);return`MYN${e}`}}async function Y(){return J()}}};