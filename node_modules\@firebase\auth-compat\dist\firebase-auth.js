!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)}(this,function(Vi,ji){"use strict";try{!function(){function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=e(Vi);const t={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var i=e?this.byteToCharMapWebSafe_:this.byteToCharMap_;const s=[];for(let n=0;n<r.length;n+=3){var o=r[n],a=n+1<r.length,c=a?r[n+1]:0,u=n+2<r.length,l=u?r[n+2]:0;let e=(15&c)<<2|l>>6,t=63&l;u||(t=64,a||(e=64)),s.push(i[o>>2],i[(3&o)<<4|c>>4],i[e],i[t])}return s.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(function(n){const r=[];let i=0;for(let t=0;t<n.length;t++){let e=n.charCodeAt(t);e<128?r[i++]=e:(e<2048?r[i++]=e>>6|192:(55296==(64512&e)&&t+1<n.length&&56320==(64512&n.charCodeAt(t+1))?(e=65536+((1023&e)<<10)+(1023&n.charCodeAt(++t)),r[i++]=e>>18|240,r[i++]=e>>12&63|128):r[i++]=e>>12|224,r[i++]=e>>6&63|128),r[i++]=63&e|128)}return r}(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):function(e){const t=[];let n=0,r=0;for(;n<e.length;){var i,s,o=e[n++];o<128?t[r++]=String.fromCharCode(o):191<o&&o<224?(i=e[n++],t[r++]=String.fromCharCode((31&o)<<6|63&i)):239<o&&o<365?(s=((7&o)<<18|(63&e[n++])<<12|(63&e[n++])<<6|63&e[n++])-65536,t[r++]=String.fromCharCode(55296+(s>>10)),t[r++]=String.fromCharCode(56320+(1023&s))):(i=e[n++],s=e[n++],t[r++]=String.fromCharCode((15&o)<<12|(63&i)<<6|63&s))}return t.join("")}(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(t,e){this.init_();var n=e?this.charToByteMapWebSafe_:this.charToByteMap_;const r=[];for(let e=0;e<t.length;){var i=n[t.charAt(e++)],s=e<t.length?n[t.charAt(e)]:0;++e;var o=e<t.length?n[t.charAt(e)]:64;++e;var a=e<t.length?n[t.charAt(e)]:64;if(++e,null==i||null==s||null==o||null==a)throw new c;r.push(i<<2|s>>4),64!==o&&(r.push(s<<4&240|o>>2),64!==a&&r.push(o<<6&192|a))}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class c extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const s=function(e){try{return t.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};const n=()=>function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}().__FIREBASE_DEFAULTS__,r=()=>{if("undefined"!=typeof process&&void 0!==process.env){var e=process.env.__FIREBASE_DEFAULTS__;return e?JSON.parse(e):void 0}},o=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&s(e[1]);return t&&JSON.parse(t)}},a=()=>{try{return n()||r()||o()}catch(e){return void console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`)}};var u,l;function d(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function h(){var e=null===(e=a())||void 0===e?void 0:e.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){return!1}}function p(){var e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function f(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function m(){const e=d();return 0<=e.indexOf("MSIE ")||0<=e.indexOf("Trident/")}function v(){try{return"object"==typeof indexedDB}catch(e){return!1}}class g extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,g.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,y.prototype.create)}}class y{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){var r,n=t[0]||{},t=`${this.service}/${e}`,e=this.errors[e],e=e?(r=n,e.replace(_,(e,t)=>{var n=r[t];return null!=n?String(n):`<${t}?>`})):"Error",e=`${this.serviceName}: ${e} (${t}).`;return new g(t,e,n)}}const _=/\{\$([^}]+)}/g;function I(e){const t=[];for(const[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.push(encodeURIComponent(n)+"="+encodeURIComponent(e))}):t.push(encodeURIComponent(n)+"="+encodeURIComponent(r));return t.length?"&"+t.join("&"):""}function w(e){const n={},t=e.replace(/^\?/,"").split("&");return t.forEach(e=>{var t;e&&([t,e]=e.split("="),n[decodeURIComponent(t)]=decodeURIComponent(e))}),n}function b(e){var t=e.indexOf("?");if(!t)return"";var n=e.indexOf("#",t);return e.substring(t,0<n?n:void 0)}class T{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,n){let r;if(void 0===e&&void 0===t&&void 0===n)throw new Error("Missing Observer.");r=function(e,t){if("object"!=typeof e||null===e)return!1;for(const n of t)if(n in e&&"function"==typeof e[n])return!0;return!1}(e,["next","error","complete"])?e:{next:e,error:t,complete:n},void 0===r.next&&(r.next=E),void 0===r.error&&(r.error=E),void 0===r.complete&&(r.complete=E);n=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?r.error(this.finalError):r.complete()}catch(e){}}),this.observers.push(r),n}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],--this.observerCount,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(e,t){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function E(){}function k(e){return e&&e._delegate?e._delegate:e}(l=u=u||{})[l.DEBUG=0]="DEBUG",l[l.VERBOSE=1]="VERBOSE",l[l.INFO=2]="INFO",l[l.WARN=3]="WARN",l[l.ERROR=4]="ERROR",l[l.SILENT=5]="SILENT";const S={debug:u.DEBUG,verbose:u.VERBOSE,info:u.INFO,warn:u.WARN,error:u.ERROR,silent:u.SILENT},A=u.INFO,R={[u.DEBUG]:"log",[u.VERBOSE]:"log",[u.INFO]:"info",[u.WARN]:"warn",[u.ERROR]:"error"},P=(e,t,...n)=>{if(!(t<e.logLevel)){var r=(new Date).toISOString(),i=R[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${r}]  ${e.name}:`,...n)}};function O(e,t){var n={};for(i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)t.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]]);return n}function C(e,o,a,c){return new(a=a||Promise)(function(n,t){function r(e){try{s(c.next(e))}catch(e){t(e)}}function i(e){try{s(c.throw(e))}catch(e){t(e)}}function s(e){var t;e.done?n(e.value):((t=e.value)instanceof a?t:new a(function(e){e(t)})).then(r,i)}s((c=c.apply(e,o||[])).next())})}function N(n,r){var i,s,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},e={next:t(0),throw:t(1),return:t(2)};return"function"==typeof Symbol&&(e[Symbol.iterator]=function(){return this}),e;function t(t){return function(e){return function(t){if(i)throw new TypeError("Generator is already executing.");for(;a;)try{if(i=1,s&&(o=2&t[0]?s.return:t[0]?s.throw||((o=s.return)&&o.call(s),0):s.next)&&!(o=o.call(s,t[1])).done)return o;switch(s=0,(t=o?[2&t[0],o.value]:t)[0]){case 0:case 1:o=t;break;case 4:return a.label++,{value:t[1],done:!1};case 5:a.label++,s=t[1],t=[0];continue;case 7:t=a.ops.pop(),a.trys.pop();continue;default:if(!(o=0<(o=a.trys).length&&o[o.length-1])&&(6===t[0]||2===t[0])){a=0;continue}if(3===t[0]&&(!o||t[1]>o[0]&&t[1]<o[3])){a.label=t[1];break}if(6===t[0]&&a.label<o[1]){a.label=o[1],o=t;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(t);break}o[2]&&a.ops.pop(),a.trys.pop();continue}t=r.call(n,a)}catch(e){t=[6,e],s=0}finally{i=o=0}if(5&t[0])throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}([t,e])}}}class L{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}const D={FACEBOOK:"facebook.com",GITHUB:"github.com",GOOGLE:"google.com",PASSWORD:"password",PHONE:"phone",TWITTER:"twitter.com"},U={EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"};function M(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}function F(){return{"admin-restricted-operation":"This operation is restricted to administrators only.","argument-error":"","app-not-authorized":"This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.","app-not-installed":"The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.","captcha-check-failed":"The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.","code-expired":"The SMS code has expired. Please re-send the verification code to try again.","cordova-not-ready":"Cordova framework is not ready.","cors-unsupported":"This browser is not supported.","credential-already-in-use":"This credential is already associated with a different user account.","custom-token-mismatch":"The custom token corresponds to a different audience.","requires-recent-login":"This operation is sensitive and requires recent authentication. Log in again before retrying this request.","dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.","dynamic-link-not-activated":"Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.","email-change-needs-verification":"Multi-factor users must always have a verified email.","email-already-in-use":"The email address is already in use by another account.","emulator-config-failed":'Auth instance has already been used to make a network call. Auth can no longer be configured to use the emulator. Try calling "connectAuthEmulator()" sooner.',"expired-action-code":"The action code has expired.","cancelled-popup-request":"This operation has been cancelled due to another conflicting popup being opened.","internal-error":"An internal AuthError has occurred.","invalid-app-credential":"The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.","invalid-app-id":"The mobile app identifier is not registered for the current project.","invalid-user-token":"This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.","invalid-auth-event":"An internal AuthError has occurred.","invalid-verification-code":"The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.","invalid-continue-uri":"The continue URL provided in the request is invalid.","invalid-cordova-configuration":"The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.","invalid-custom-token":"The custom token format is incorrect. Please check the documentation.","invalid-dynamic-link-domain":"The provided dynamic link domain is not configured or authorized for the current project.","invalid-email":"The email address is badly formatted.","invalid-emulator-scheme":"Emulator URL must start with a valid scheme (http:// or https://).","invalid-api-key":"Your API key is invalid, please check you have copied it correctly.","invalid-cert-hash":"The SHA-1 certificate hash provided is invalid.","invalid-credential":"The supplied auth credential is incorrect, malformed or has expired.","invalid-message-payload":"The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-multi-factor-session":"The request does not contain a valid proof of first factor successful sign-in.","invalid-oauth-provider":"EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.","invalid-oauth-client-id":"The OAuth client ID provided is either invalid or does not match the specified API key.","unauthorized-domain":"This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.","invalid-action-code":"The action code is invalid. This can happen if the code is malformed, expired, or has already been used.","wrong-password":"The password is invalid or the user does not have a password.","invalid-persistence-type":"The specified persistence type is invalid. It can only be local, session or none.","invalid-phone-number":"The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].","invalid-provider-id":"The specified provider ID is invalid.","invalid-recipient-email":"The email corresponding to this action failed to send as the provided recipient email address is invalid.","invalid-sender":"The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-verification-id":"The verification ID used to create the phone auth credential is invalid.","invalid-tenant-id":"The Auth instance's tenant ID is invalid.","login-blocked":"Login blocked by user-provided method: {$originalMessage}","missing-android-pkg-name":"An Android Package Name must be provided if the Android App is required to be installed.","auth-domain-config-required":"Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.","missing-app-credential":"The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.","missing-verification-code":"The phone auth credential was created with an empty SMS verification code.","missing-continue-uri":"A continue URL must be provided in the request.","missing-iframe-start":"An internal AuthError has occurred.","missing-ios-bundle-id":"An iOS Bundle ID must be provided if an App Store ID is provided.","missing-or-invalid-nonce":"The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.","missing-password":"A non-empty password must be provided","missing-multi-factor-info":"No second factor identifier is provided.","missing-multi-factor-session":"The request is missing proof of first factor successful sign-in.","missing-phone-number":"To send verification codes, provide a phone number for the recipient.","missing-verification-id":"The phone auth credential was created with an empty verification ID.","app-deleted":"This instance of FirebaseApp has been deleted.","multi-factor-info-not-found":"The user does not have a second factor matching the identifier provided.","multi-factor-auth-required":"Proof of ownership of a second factor is required to complete sign-in.","account-exists-with-different-credential":"An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.","network-request-failed":"A network AuthError (such as timeout, interrupted connection or unreachable host) has occurred.","no-auth-event":"An internal AuthError has occurred.","no-such-provider":"User was not linked to an account with the given provider.","null-user":"A null user object was provided as the argument for an operation which requires a non-null user object.","operation-not-allowed":"The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.","operation-not-supported-in-this-environment":'This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',"popup-blocked":"Unable to establish a connection with the popup. It may have been blocked by the browser.","popup-closed-by-user":"The popup has been closed by the user before finalizing the operation.","provider-already-linked":"User can only be linked to one identity for the given provider.","quota-exceeded":"The project's quota for this operation has been exceeded.","redirect-cancelled-by-user":"The redirect operation has been cancelled by the user before finalizing.","redirect-operation-pending":"A redirect sign-in operation is already pending.","rejected-credential":"The request contains malformed or mismatching credentials.","second-factor-already-in-use":"The second factor is already enrolled on this account.","maximum-second-factor-count-exceeded":"The maximum allowed number of second factors on a user has been exceeded.","tenant-id-mismatch":"The provided tenant ID does not match the Auth instance's tenant ID",timeout:"The operation has timed out.","user-token-expired":"The user's credential is no longer valid. The user must sign in again.","too-many-requests":"We have blocked all requests from this device due to unusual activity. Try again later.","unauthorized-continue-uri":"The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.","unsupported-first-factor":"Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.","unsupported-persistence-type":"The current environment does not support the specified persistence type.","unsupported-tenant-operation":"This operation is not supported in a multi-tenant context.","unverified-email":"The operation requires a verified email.","user-cancelled":"The user did not grant your application the permissions it requested.","user-not-found":"There is no user record corresponding to this identifier. The user may have been deleted.","user-disabled":"The user account has been disabled by an administrator.","user-mismatch":"The supplied credentials do not correspond to the previously signed in user.","user-signed-out":"","weak-password":"The password must be 6 characters long or more.","web-storage-unsupported":"This browser is not supported or 3rd party cookies and data may be disabled.","already-initialized":"initializeAuth() has already been called with different options. To avoid this error, call initializeAuth() with the same options as when it was originally called, or call getAuth() to return the already initialized instance.","missing-recaptcha-token":"The reCAPTCHA token is missing when sending request to the backend.","invalid-recaptcha-token":"The reCAPTCHA token is invalid when sending request to the backend.","invalid-recaptcha-action":"The reCAPTCHA action is invalid when sending request to the backend.","recaptcha-not-enabled":"reCAPTCHA Enterprise integration is not enabled for this project.","missing-client-type":"The reCAPTCHA client type is missing when sending request to the backend.","missing-recaptcha-version":"The reCAPTCHA version is missing when sending request to the backend.","invalid-req-type":"Invalid request parameters.","invalid-recaptcha-version":"The reCAPTCHA version is invalid when sending request to the backend.","unsupported-password-policy-schema-version":"The password policy received from the backend uses a schema version that is not supported by this version of the Firebase SDK.","password-does-not-meet-requirements":"The password does not meet the requirements."}}const V=M,j=new y("auth","Firebase",M()),x=new class{constructor(e){this.name=e,this._logLevel=A,this._logHandler=P,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in u))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?S[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,u.DEBUG,...e),this._logHandler(this,u.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,u.VERBOSE,...e),this._logHandler(this,u.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,u.INFO,...e),this._logHandler(this,u.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,u.WARN,...e),this._logHandler(this,u.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,u.ERROR,...e),this._logHandler(this,u.ERROR,...e)}}("@firebase/auth");function H(e,...t){x.logLevel<=u.ERROR&&x.error(`Auth (${ji.SDK_VERSION}): ${e}`,...t)}function W(e,...t){throw K(e,...t)}function q(e,...t){return K(e,...t)}function z(e,t,n){n=Object.assign(Object.assign({},V()),{[t]:n});const r=new y("auth","Firebase",n);return r.create(t,{appName:e.name})}function B(e){return z(e,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function G(e,t,n){if(!(t instanceof n))throw n.name!==t.constructor.name&&W(e,"argument-error"),z(e,"argument-error",`Type of ${t.constructor.name} does not match expected instance.`+"Did you pass a reference from a different Auth SDK?")}function K(e,...t){if("string"==typeof e)return j.create(e,...t);{var n=t[0];const r=[...t.slice(1)];return r[0]&&(r[0].appName=e.name),e._errorFactory.create(n,...r)}}function $(e,t,...n){if(!e)throw K(t,...n)}function J(e){e="INTERNAL ASSERTION FAILED: "+e;throw H(e),new Error(e)}function Y(e,t){e||J(t)}function X(){var e;return"undefined"!=typeof self&&(null===(e=self.location)||void 0===e?void 0:e.href)||""}function Q(){return"http:"===Z()||"https:"===Z()}function Z(){var e;return"undefined"!=typeof self&&(null===(e=self.location)||void 0===e?void 0:e.protocol)||null}class ee{constructor(e,t){Y((this.shortDelay=e)<(this.longDelay=t),"Short delay should be less than long delay!"),this.isMobile="undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(d())||f()}get(){return"undefined"!=typeof navigator&&navigator&&"onLine"in navigator&&"boolean"==typeof navigator.onLine&&(Q()||p()||"connection"in navigator)&&!navigator.onLine?Math.min(5e3,this.shortDelay):this.isMobile?this.longDelay:this.shortDelay}}function te(e,t){Y(e.emulator,"Emulator should always be set here");var e=e.emulator["url"];return t?`${e}${t.startsWith("/")?t.slice(1):t}`:e}class ne{static initialize(e,t,n){this.fetchImpl=e,t&&(this.headersImpl=t),n&&(this.responseImpl=n)}static fetch(){return this.fetchImpl||("undefined"!=typeof self&&"fetch"in self?self.fetch:"undefined"!=typeof globalThis&&globalThis.fetch?globalThis.fetch:"undefined"!=typeof fetch?fetch:void J("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static headers(){return this.headersImpl||("undefined"!=typeof self&&"Headers"in self?self.Headers:"undefined"!=typeof globalThis&&globalThis.Headers?globalThis.Headers:"undefined"!=typeof Headers?Headers:void J("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static response(){return this.responseImpl||("undefined"!=typeof self&&"Response"in self?self.Response:"undefined"!=typeof globalThis&&globalThis.Response?globalThis.Response:"undefined"!=typeof Response?Response:void J("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}}const re={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"},ie=new ee(3e4,6e4);function se(e,t){return e.tenantId&&!t.tenantId?Object.assign(Object.assign({},t),{tenantId:e.tenantId}):t}async function oe(s,o,a,c,e={}){return ae(s,e,async()=>{let e={},t={};c&&("GET"===o?t=c:e={body:JSON.stringify(c)});var n=I(Object.assign({key:s.config.apiKey},t)).slice(1);const r=await s._getAdditionalHeaders();r["Content-Type"]="application/json",s.languageCode&&(r["X-Firebase-Locale"]=s.languageCode);const i=Object.assign({method:o,headers:r},e);return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent||(i.referrerPolicy="no-referrer"),ne.fetch()(ue(s,s.config.apiHost,a,n),i)})}async function ae(t,e,n){t._canInitEmulator=!1;e=Object.assign(Object.assign({},re),e);try{const s=new le(t),o=await Promise.race([n(),s.promise]);s.clearNetworkTimeout();var r=await o.json();if("needConfirmation"in r)throw de(t,"account-exists-with-different-credential",r);if(o.ok&&!("errorMessage"in r))return r;{const a=o.ok?r.errorMessage:r.error.message,[c,u]=a.split(" : ");if("FEDERATED_USER_ID_ALREADY_LINKED"===c)throw de(t,"credential-already-in-use",r);if("EMAIL_EXISTS"===c)throw de(t,"email-already-in-use",r);if("USER_DISABLED"===c)throw de(t,"user-disabled",r);var i=e[c]||c.toLowerCase().replace(/[_\s]+/g,"-");if(u)throw z(t,i,u);W(t,i)}}catch(e){if(e instanceof g)throw e;W(t,"network-request-failed",{message:String(e)})}}async function ce(e,t,n,r,i={}){i=await oe(e,t,n,r,i);return"mfaPendingCredential"in i&&W(e,"multi-factor-auth-required",{_serverResponse:i}),i}function ue(e,t,n,r){r=`${t}${n}?${r}`;return e.config.emulator?te(e.config,r):`${e.config.apiScheme}://${r}`}class le{constructor(e){this.auth=e,this.timer=null,this.promise=new Promise((e,t)=>{this.timer=setTimeout(()=>t(q(this.auth,"network-request-failed")),ie.get())})}clearNetworkTimeout(){clearTimeout(this.timer)}}function de(e,t,n){const r={appName:e.name};n.email&&(r.email=n.email),n.phoneNumber&&(r.phoneNumber=n.phoneNumber);const i=q(e,t,r);return i.customData._tokenResponse=n,i}function he(e){return void 0!==e&&void 0!==e.getResponse}function pe(e){return void 0!==e&&void 0!==e.enterprise}class fe{constructor(e){if(this.siteKey="",this.recaptchaEnforcementState=[],void 0===e.recaptchaKey)throw new Error("recaptchaKey undefined");this.siteKey=e.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=e.recaptchaEnforcementState}getProviderEnforcementState(e){if(!this.recaptchaEnforcementState||0===this.recaptchaEnforcementState.length)return null;for(const t of this.recaptchaEnforcementState)if(t.provider&&t.provider===e)return function(e){switch(e){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}}(t.enforcementState);return null}isProviderEnabled(e){return"ENFORCE"===this.getProviderEnforcementState(e)||"AUDIT"===this.getProviderEnforcementState(e)}}async function me(e,t){return oe(e,"POST","/v1/accounts:lookup",t)}function ve(e){if(e)try{const t=new Date(Number(e));if(!isNaN(t.getTime()))return t.toUTCString()}catch(e){}}function ge(e){return 1e3*Number(e)}function ye(e){var[t,n,e]=e.split(".");if(void 0===t||void 0===n||void 0===e)return H("JWT malformed, contained fewer than 3 sections"),null;try{var r=s(n);return r?JSON.parse(r):(H("Failed to decode base64 JWT payload"),null)}catch(e){return H("Caught error parsing JWT payload as JSON",null==e?void 0:e.toString()),null}}function _e(e){e=ye(e);return $(e,"internal-error"),$(void 0!==e.exp,"internal-error"),$(void 0!==e.iat,"internal-error"),Number(e.exp)-Number(e.iat)}async function Ie(t,n,e=!1){if(e)return n;try{return n}catch(e){throw e instanceof g&&(n=[e["code"]][0],"auth/user-disabled"===n||"auth/user-token-expired"===n)&&t.auth.currentUser===t&&await t.auth.signOut(),e}}class we{constructor(e){this.user=e,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,null!==this.timerId&&clearTimeout(this.timerId))}getInterval(e){if(e){var t=this.errorBackoff;return this.errorBackoff=Math.min(2*this.errorBackoff,96e4),t}this.errorBackoff=3e4;t=(null!==(t=this.user.stsTokenManager.expirationTime)&&void 0!==t?t:0)-Date.now()-3e5;return Math.max(0,t)}schedule(e=!1){this.isRunning&&(e=this.getInterval(e),this.timerId=setTimeout(async()=>{await this.iteration()},e))}async iteration(){try{await this.user.getIdToken(!0)}catch(e){return void("auth/network-request-failed"===(null==e?void 0:e.code)&&this.schedule(!0))}this.schedule()}}class be{constructor(e,t){this.createdAt=e,this.lastLoginAt=t,this._initializeTime()}_initializeTime(){this.lastSignInTime=ve(this.lastLoginAt),this.creationTime=ve(this.createdAt)}_copy(e){this.createdAt=e.createdAt,this.lastLoginAt=e.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}}async function Te(e){var t=e.auth,n=await e.getIdToken(),r=await Ie(e,me(t,{idToken:n}));$(null==r?void 0:r.users.length,t,"internal-error");var i=r.users[0];e._notifyReloadListener(i);t=null!==(n=i.providerUserInfo)&&void 0!==n&&n.length?Ee(i.providerUserInfo):[],r=function(e,n){e=e.filter(t=>!n.some(e=>e.providerId===t.providerId));return[...e,...n]}(e.providerData,t),n=e.isAnonymous,t=!(e.email&&i.passwordHash||null!=r&&r.length),t=!!n&&t,t={uid:i.localId,displayName:i.displayName||null,photoURL:i.photoUrl||null,email:i.email||null,emailVerified:i.emailVerified||!1,phoneNumber:i.phoneNumber||null,tenantId:i.tenantId||null,providerData:r,metadata:new be(i.createdAt,i.lastLoginAt),isAnonymous:t};Object.assign(e,t)}function Ee(e){return e.map(e=>{var t=e["providerId"],e=O(e,["providerId"]);return{providerId:t,uid:e.rawId||"",displayName:e.displayName||null,email:e.email||null,phoneNumber:e.phoneNumber||null,photoURL:e.photoUrl||null}})}class ke{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(e){$(e.idToken,"internal-error"),$(void 0!==e.idToken,"internal-error"),$(void 0!==e.refreshToken,"internal-error");var t="expiresIn"in e&&void 0!==e.expiresIn?Number(e.expiresIn):_e(e.idToken);this.updateTokensAndExpiration(e.idToken,e.refreshToken,t)}updateFromIdToken(e){$(0!==e.length,"internal-error");var t=_e(e);this.updateTokensAndExpiration(e,null,t)}async getToken(e,t=!1){return t||!this.accessToken||this.isExpired?($(this.refreshToken,e,"user-token-expired"),this.refreshToken?(await this.refresh(e,this.refreshToken),this.accessToken):null):this.accessToken}clearRefreshToken(){this.refreshToken=null}async refresh(e,t){var i,s,{accessToken:t,refreshToken:e,expiresIn:n}=(s=t,await{accessToken:(n=await ae(i=e,{},async()=>{var e=I({grant_type:"refresh_token",refresh_token:s}).slice(1),{tokenApiHost:t,apiKey:n}=i.config,n=ue(i,t,"/v1/token",`key=${n}`);const r=await i._getAdditionalHeaders();return r["Content-Type"]="application/x-www-form-urlencoded",ne.fetch()(n,{method:"POST",headers:r,body:e})})).access_token,expiresIn:n.expires_in,refreshToken:n.refresh_token});this.updateTokensAndExpiration(t,e,Number(n))}updateTokensAndExpiration(e,t,n){this.refreshToken=t||null,this.accessToken=e||null,this.expirationTime=Date.now()+1e3*n}static fromJSON(e,t){var{refreshToken:n,accessToken:r,expirationTime:t}=t;const i=new ke;return n&&($("string"==typeof n,"internal-error",{appName:e}),i.refreshToken=n),r&&($("string"==typeof r,"internal-error",{appName:e}),i.accessToken=r),t&&($("number"==typeof t,"internal-error",{appName:e}),i.expirationTime=t),i}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(e){this.accessToken=e.accessToken,this.refreshToken=e.refreshToken,this.expirationTime=e.expirationTime}_clone(){return Object.assign(new ke,this.toJSON())}_performRefresh(){return J("not implemented")}}function Se(e,t){$("string"==typeof e||void 0===e,"internal-error",{appName:t})}class Ae{constructor(e){var{uid:t,auth:n,stsTokenManager:r}=e,e=O(e,["uid","auth","stsTokenManager"]);this.providerId="firebase",this.proactiveRefresh=new we(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=t,this.auth=n,this.stsTokenManager=r,this.accessToken=r.accessToken,this.displayName=e.displayName||null,this.email=e.email||null,this.emailVerified=e.emailVerified||!1,this.phoneNumber=e.phoneNumber||null,this.photoURL=e.photoURL||null,this.isAnonymous=e.isAnonymous||!1,this.tenantId=e.tenantId||null,this.providerData=e.providerData?[...e.providerData]:[],this.metadata=new be(e.createdAt||void 0,e.lastLoginAt||void 0)}async getIdToken(e){e=await Ie(this,this.stsTokenManager.getToken(this.auth,e));return $(e,this.auth,"internal-error"),this.accessToken!==e&&(this.accessToken=e,await this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),e}getIdTokenResult(e){return async function(e,t=!1){const n=k(e);var r=await n.getIdToken(t),i=ye(r);return $(i&&i.exp&&i.auth_time&&i.iat,n.auth,"internal-error"),e="object"==typeof i.firebase?i.firebase:void 0,t=null==e?void 0:e.sign_in_provider,{claims:i,token:r,authTime:ve(ge(i.auth_time)),issuedAtTime:ve(ge(i.iat)),expirationTime:ve(ge(i.exp)),signInProvider:t||null,signInSecondFactor:(null==e?void 0:e.sign_in_second_factor)||null}}(this,e)}reload(){return async function(e){const t=k(e);await Te(t),await t.auth._persistUserIfCurrent(t),t.auth._notifyListenersIfCurrent(t)}(this)}_assign(e){this!==e&&($(this.uid===e.uid,this.auth,"internal-error"),this.displayName=e.displayName,this.photoURL=e.photoURL,this.email=e.email,this.emailVerified=e.emailVerified,this.phoneNumber=e.phoneNumber,this.isAnonymous=e.isAnonymous,this.tenantId=e.tenantId,this.providerData=e.providerData.map(e=>Object.assign({},e)),this.metadata._copy(e.metadata),this.stsTokenManager._assign(e.stsTokenManager))}_clone(e){const t=new Ae(Object.assign(Object.assign({},this),{auth:e,stsTokenManager:this.stsTokenManager._clone()}));return t.metadata._copy(this.metadata),t}_onReload(e){$(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=e,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(e){this.reloadListener?this.reloadListener(e):this.reloadUserInfo=e}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}async _updateTokensIfNecessary(e,t=!1){let n=!1;e.idToken&&e.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(e),n=!0),t&&await Te(this),await this.auth._persistUserIfCurrent(this),n&&this.auth._notifyListenersIfCurrent(this)}async delete(){if(ji._isFirebaseServerApp(this.auth.app))return Promise.reject(B(this.auth));var e=await this.getIdToken();return await Ie(this,async function(e,t){return oe(e,"POST","/v1/accounts:delete",t)}(this.auth,{idToken:e})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut()}toJSON(){return Object.assign(Object.assign({uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(e=>Object.assign({},e)),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId},this.metadata.toJSON()),{apiKey:this.auth.config.apiKey,appName:this.auth.name})}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(e,t){var n=null!==(u=t.displayName)&&void 0!==u?u:void 0,r=null!==(o=t.email)&&void 0!==o?o:void 0,i=null!==(c=t.phoneNumber)&&void 0!==c?c:void 0,s=null!==(a=t.photoURL)&&void 0!==a?a:void 0,o=null!==(u=t.tenantId)&&void 0!==u?u:void 0,a=null!==(c=t._redirectEventId)&&void 0!==c?c:void 0,c=null!==(u=t.createdAt)&&void 0!==u?u:void 0,u=null!==(u=t.lastLoginAt)&&void 0!==u?u:void 0;const{uid:l,emailVerified:d,isAnonymous:h,providerData:p,stsTokenManager:f}=t;$(l&&f,e,"internal-error");t=ke.fromJSON(this.name,f);$("string"==typeof l,e,"internal-error"),Se(n,e.name),Se(r,e.name),$("boolean"==typeof d,e,"internal-error"),$("boolean"==typeof h,e,"internal-error"),Se(i,e.name),Se(s,e.name),Se(o,e.name),Se(a,e.name),Se(c,e.name),Se(u,e.name);const m=new Ae({uid:l,auth:e,email:r,emailVerified:d,displayName:n,isAnonymous:h,photoURL:s,phoneNumber:i,tenantId:o,stsTokenManager:t,createdAt:c,lastLoginAt:u});return p&&Array.isArray(p)&&(m.providerData=p.map(e=>Object.assign({},e))),a&&(m._redirectEventId=a),m}static async _fromIdTokenResponse(e,t,n=!1){const r=new ke;r.updateFromServerResponse(t);n=new Ae({uid:t.localId,auth:e,stsTokenManager:r,isAnonymous:n});return await Te(n),n}static async _fromGetAccountInfoResponse(e,t,n){var r=t.users[0];$(void 0!==r.localId,"internal-error");var i=void 0!==r.providerUserInfo?Ee(r.providerUserInfo):[],t=!(r.email&&r.passwordHash||null!=i&&i.length);const s=new ke;s.updateFromIdToken(n);t=new Ae({uid:r.localId,auth:e,stsTokenManager:s,isAnonymous:t}),i={uid:r.localId,displayName:r.displayName||null,photoURL:r.photoUrl||null,email:r.email||null,emailVerified:r.emailVerified||!1,phoneNumber:r.phoneNumber||null,tenantId:r.tenantId||null,providerData:i,metadata:new be(r.createdAt,r.lastLoginAt),isAnonymous:!(r.email&&r.passwordHash||null!=i&&i.length)};return Object.assign(t,i),t}}const Re=new Map;function Pe(e){Y(e instanceof Function,"Expected a class definition");let t=Re.get(e);return t?Y(t instanceof e,"Instance stored in cache mismatched with class"):(t=new e,Re.set(e,t)),t}class Oe{constructor(){this.type="NONE",this.storage={}}async _isAvailable(){return!0}async _set(e,t){this.storage[e]=t}async _get(e){e=this.storage[e];return void 0===e?null:e}async _remove(e){delete this.storage[e]}_addListener(e,t){}_removeListener(e,t){}}Oe.type="NONE";const Ce=Oe;function Ne(e,t,n){return`firebase:${e}:${t}:${n}`}class Le{constructor(e,t,n){this.persistence=e,this.auth=t,this.userKey=n;var{config:e,name:n}=this.auth;this.fullUserKey=Ne(this.userKey,e.apiKey,n),this.fullPersistenceKey=Ne("persistence",e.apiKey,n),this.boundEventHandler=t._onStorageEvent.bind(t),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(e){return this.persistence._set(this.fullUserKey,e.toJSON())}async getCurrentUser(){var e=await this.persistence._get(this.fullUserKey);return e?Ae._fromJSON(this.auth,e):null}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}async setPersistence(e){if(this.persistence!==e){var t=await this.getCurrentUser();return await this.removeCurrentUser(),this.persistence=e,t?this.setCurrentUser(t):void 0}}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static async create(e,t,n="authUser"){if(!t.length)return new Le(Pe(Ce),e,n);const r=(await Promise.all(t.map(async e=>{if(await e._isAvailable())return e}))).filter(e=>e);let i=r[0]||Pe(Ce);const s=Ne(n,e.config.apiKey,e.name);let o=null;for(const l of t)try{var a=await l._get(s);if(a){var c=Ae._fromJSON(e,a);l!==i&&(o=c),i=l;break}}catch(e){}var u=r.filter(e=>e._shouldAllowMigration);return i._shouldAllowMigration&&u.length&&(i=u[0],o&&await i._set(s,o.toJSON()),await Promise.all(t.map(async e=>{if(e!==i)try{await e._remove(s)}catch(e){}}))),new Le(i,e,n)}}function De(e){const t=e.toLowerCase();if(t.includes("opera/")||t.includes("opr/")||t.includes("opios/"))return"Opera";if(Ve(t))return"IEMobile";if(t.includes("msie")||t.includes("trident/"))return"IE";if(t.includes("edge/"))return"Edge";if(Ue(t))return"Firefox";if(t.includes("silk/"))return"Silk";if(xe(t))return"Blackberry";if(He(t))return"Webos";if(Me(t))return"Safari";if((t.includes("chrome/")||Fe(t))&&!t.includes("edge/"))return"Chrome";if(je(t))return"Android";e=e.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/);return 2===(null==e?void 0:e.length)?e[1]:"Other"}function Ue(e=d()){return/firefox\//i.test(e)}function Me(e=d()){const t=e.toLowerCase();return t.includes("safari/")&&!t.includes("chrome/")&&!t.includes("crios/")&&!t.includes("android")}function Fe(e=d()){return/crios\//i.test(e)}function Ve(e=d()){return/iemobile/i.test(e)}function je(e=d()){return/android/i.test(e)}function xe(e=d()){return/blackberry/i.test(e)}function He(e=d()){return/webos/i.test(e)}function We(e=d()){return/iphone|ipad|ipod/i.test(e)||/macintosh/i.test(e)&&/mobile/i.test(e)}function qe(e=d()){return We(e)||je(e)||He(e)||xe(e)||/windows phone/i.test(e)||Ve(e)}function ze(e,t=[]){let n;switch(e){case"Browser":n=De(d());break;case"Worker":n=`${De(d())}-${e}`;break;default:n=e}t=t.length?t.join(","):"FirebaseCore-web";return`${n}/JsCore/${ji.SDK_VERSION}/${t}`}class Be{constructor(e){this.auth=e,this.queue=[]}pushCallback(r,e){var t=n=>new Promise((e,t)=>{try{e(r(n))}catch(e){t(e)}});t.onAbort=e,this.queue.push(t);const n=this.queue.length-1;return()=>{this.queue[n]=()=>Promise.resolve()}}async runMiddleware(e){if(this.auth.currentUser!==e){const t=[];try{for(const n of this.queue)await n(e),n.onAbort&&t.push(n.onAbort)}catch(e){t.reverse();for(const r of t)try{r()}catch(e){}throw this.auth._errorFactory.create("login-blocked",{originalMessage:null==e?void 0:e.message})}}}}class Ge{constructor(e){var t,n=e.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=null!==(t=n.minPasswordLength)&&void 0!==t?t:6,n.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=n.maxPasswordLength),void 0!==n.containsLowercaseCharacter&&(this.customStrengthOptions.containsLowercaseLetter=n.containsLowercaseCharacter),void 0!==n.containsUppercaseCharacter&&(this.customStrengthOptions.containsUppercaseLetter=n.containsUppercaseCharacter),void 0!==n.containsNumericCharacter&&(this.customStrengthOptions.containsNumericCharacter=n.containsNumericCharacter),void 0!==n.containsNonAlphanumericCharacter&&(this.customStrengthOptions.containsNonAlphanumericCharacter=n.containsNonAlphanumericCharacter),this.enforcementState=e.enforcementState,"ENFORCEMENT_STATE_UNSPECIFIED"===this.enforcementState&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=null!==(n=null===(n=e.allowedNonAlphanumericCharacters)||void 0===n?void 0:n.join(""))&&void 0!==n?n:"",this.forceUpgradeOnSignin=null!==(n=e.forceUpgradeOnSignin)&&void 0!==n&&n,this.schemaVersion=e.schemaVersion}validatePassword(e){var t,n,r;const i={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(e,i),this.validatePasswordCharacterOptions(e,i),i.isValid&&(i.isValid=null===(t=i.meetsMinPasswordLength)||void 0===t||t),i.isValid&&(i.isValid=null===(t=i.meetsMaxPasswordLength)||void 0===t||t),i.isValid&&(i.isValid=null===(n=i.containsLowercaseLetter)||void 0===n||n),i.isValid&&(i.isValid=null===(n=i.containsUppercaseLetter)||void 0===n||n),i.isValid&&(i.isValid=null===(r=i.containsNumericCharacter)||void 0===r||r),i.isValid&&(i.isValid=null===(r=i.containsNonAlphanumericCharacter)||void 0===r||r),i}validatePasswordLengthOptions(e,t){var n=this.customStrengthOptions.minPasswordLength,r=this.customStrengthOptions.maxPasswordLength;n&&(t.meetsMinPasswordLength=e.length>=n),r&&(t.meetsMaxPasswordLength=e.length<=r)}validatePasswordCharacterOptions(t,n){var r;this.updatePasswordCharacterOptionsStatuses(n,!1,!1,!1,!1);for(let e=0;e<t.length;e++)r=t.charAt(e),this.updatePasswordCharacterOptionsStatuses(n,"a"<=r&&r<="z","A"<=r&&r<="Z","0"<=r&&r<="9",this.allowedNonAlphanumericCharacters.includes(r))}updatePasswordCharacterOptionsStatuses(e,t,n,r,i){this.customStrengthOptions.containsLowercaseLetter&&(e.containsLowercaseLetter||(e.containsLowercaseLetter=t)),this.customStrengthOptions.containsUppercaseLetter&&(e.containsUppercaseLetter||(e.containsUppercaseLetter=n)),this.customStrengthOptions.containsNumericCharacter&&(e.containsNumericCharacter||(e.containsNumericCharacter=r)),this.customStrengthOptions.containsNonAlphanumericCharacter&&(e.containsNonAlphanumericCharacter||(e.containsNonAlphanumericCharacter=i))}}class Ke{constructor(e,t,n,r){this.app=e,this.heartbeatServiceProvider=t,this.appCheckServiceProvider=n,this.config=r,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new Je(this),this.idTokenSubscription=new Je(this),this.beforeStateQueue=new Be(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=j,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=e.name,this.clientVersion=r.sdkClientVersion}_initializeWithPersistence(t,n){return n&&(this._popupRedirectResolver=Pe(n)),this._initializationPromise=this.queue(async()=>{var e;if(!this._deleted&&(this.persistenceManager=await Le.create(this,t),!this._deleted)){if(null!==(e=this._popupRedirectResolver)&&void 0!==e&&e._shouldInitProactively)try{await this._popupRedirectResolver._initialize(this)}catch(e){}await this.initializeCurrentUser(n),this.lastNotifiedUid=(null===(e=this.currentUser)||void 0===e?void 0:e.uid)||null,this._deleted||(this._isInitialized=!0)}}),this._initializationPromise}async _onStorageEvent(){if(!this._deleted){var e=await this.assertedPersistence.getCurrentUser();if(this.currentUser||e)return this.currentUser&&e&&this.currentUser.uid===e.uid?(this._currentUser._assign(e),void await this.currentUser.getIdToken()):void await this._updateCurrentUser(e,!0)}}async initializeCurrentUserFromIdToken(e){try{var t=await me(this,{idToken:e}),n=await Ae._fromGetAccountInfoResponse(this,t,e);await this.directlySetCurrentUser(n)}catch(e){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",e),await this.directlySetCurrentUser(null)}}async initializeCurrentUser(e){if(ji._isFirebaseServerApp(this.app)){const o=this.app.settings.authIdToken;return o?new Promise(e=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(o).then(e,e))}):this.directlySetCurrentUser(null)}var t,n,r=await this.assertedPersistence.getCurrentUser();let i=r,s=!1;if(e&&this.config.authDomain&&(await this.getOrInitRedirectPersistenceManager(),t=null===(n=this.redirectUser)||void 0===n?void 0:n._redirectEventId,n=null===i||void 0===i?void 0:i._redirectEventId,e=await this.tryRedirectSignIn(e),t&&t!==n||null==e||!e.user||(i=e.user,s=!0)),!i)return this.directlySetCurrentUser(null);if(i._redirectEventId)return $(this._popupRedirectResolver,this,"argument-error"),await this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===i._redirectEventId?this.directlySetCurrentUser(i):this.reloadAndSetCurrentUserOrClear(i);if(s)try{await this.beforeStateQueue.runMiddleware(i)}catch(e){i=r,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(e))}return i?this.reloadAndSetCurrentUserOrClear(i):this.directlySetCurrentUser(null)}async tryRedirectSignIn(e){let t=null;try{t=await this._popupRedirectResolver._completeRedirectFn(this,e,!0)}catch(e){await this._setRedirectUser(null)}return t}async reloadAndSetCurrentUserOrClear(e){try{await Te(e)}catch(e){if("auth/network-request-failed"!==(null==e?void 0:e.code))return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(e)}useDeviceLanguage(){this.languageCode=function(){if("undefined"==typeof navigator)return null;var e=navigator;return e.languages&&e.languages[0]||e.language||null}()}async _delete(){this._deleted=!0}async updateCurrentUser(e){if(ji._isFirebaseServerApp(this.app))return Promise.reject(B(this));const t=e?k(e):null;return t&&$(t.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(t&&t._clone(this))}async _updateCurrentUser(e,t=!1){if(!this._deleted)return e&&$(this.tenantId===e.tenantId,this,"tenant-id-mismatch"),t||await this.beforeStateQueue.runMiddleware(e),this.queue(async()=>{await this.directlySetCurrentUser(e),this.notifyAuthListeners()})}async signOut(){return ji._isFirebaseServerApp(this.app)?Promise.reject(B(this)):(await this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&await this._setRedirectUser(null),this._updateCurrentUser(null,!0))}setPersistence(e){return ji._isFirebaseServerApp(this.app)?Promise.reject(B(this)):this.queue(async()=>{await this.assertedPersistence.setPersistence(Pe(e))})}_getRecaptchaConfig(){return null==this.tenantId?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}async validatePassword(e){this._getPasswordPolicyInternal()||await this._updatePasswordPolicy();const t=this._getPasswordPolicyInternal();return t.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):t.validatePassword(e)}_getPasswordPolicyInternal(){return null===this.tenantId?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}async _updatePasswordPolicy(){var e=await oe(e=this,"GET","/v2/passwordPolicy",se(e,{})),e=new Ge(e);null===this.tenantId?this._projectPasswordPolicy=e:this._tenantPasswordPolicies[this.tenantId]=e}_getPersistence(){return this.assertedPersistence.persistence.type}_updateErrorMap(e){this._errorFactory=new y("auth","Firebase",e())}onAuthStateChanged(e,t,n){return this.registerStateListener(this.authStateSubscription,e,t,n)}beforeAuthStateChanged(e,t){return this.beforeStateQueue.pushCallback(e,t)}onIdTokenChanged(e,t,n){return this.registerStateListener(this.idTokenSubscription,e,t,n)}authStateReady(){return new Promise((e,t)=>{if(this.currentUser)e();else{const n=this.onAuthStateChanged(()=>{n(),e()},t)}})}async revokeAccessToken(e){if(this.currentUser){const n={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:e,idToken:await this.currentUser.getIdToken()};null!=this.tenantId&&(n.tenantId=this.tenantId),t=this,e=n,await oe(t,"POST","/v2/accounts:revokeToken",se(t,e))}var t}toJSON(){var e;return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:null===(e=this._currentUser)||void 0===e?void 0:e.toJSON()}}async _setRedirectUser(e,t){const n=await this.getOrInitRedirectPersistenceManager(t);return null===e?n.removeCurrentUser():n.setCurrentUser(e)}async getOrInitRedirectPersistenceManager(e){return this.redirectPersistenceManager||($(e=e&&Pe(e)||this._popupRedirectResolver,this,"argument-error"),this.redirectPersistenceManager=await Le.create(this,[Pe(e._redirectPersistence)],"redirectUser"),this.redirectUser=await this.redirectPersistenceManager.getCurrentUser()),this.redirectPersistenceManager}async _redirectUserForId(e){var t;return this._isInitialized&&await this.queue(async()=>{}),(null===(t=this._currentUser)||void 0===t?void 0:t._redirectEventId)===e?this._currentUser:(null===(t=this.redirectUser)||void 0===t?void 0:t._redirectEventId)===e?this.redirectUser:null}async _persistUserIfCurrent(e){if(e===this.currentUser)return this.queue(async()=>this.directlySetCurrentUser(e))}_notifyListenersIfCurrent(e){e===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:${this.name}`}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){var e;this._isInitialized&&(this.idTokenSubscription.next(this.currentUser),e=null!==(e=null===(e=this.currentUser)||void 0===e?void 0:e.uid)&&void 0!==e?e:null,this.lastNotifiedUid!==e&&(this.lastNotifiedUid=e,this.authStateSubscription.next(this.currentUser)))}registerStateListener(e,t,n,r){if(this._deleted)return()=>{};const i="function"==typeof t?t:t.next.bind(t);let s=!1;const o=this._isInitialized?Promise.resolve():this._initializationPromise;if($(o,this,"internal-error"),o.then(()=>{s||i(this.currentUser)}),"function"==typeof t){const a=e.addObserver(t,n,r);return()=>{s=!0,a()}}{const c=e.addObserver(t);return()=>{s=!0,c()}}}async directlySetCurrentUser(e){this.currentUser&&this.currentUser!==e&&this._currentUser._stopProactiveRefresh(),e&&this.isProactiveRefreshEnabled&&e._startProactiveRefresh(),(this.currentUser=e)?await this.assertedPersistence.setCurrentUser(e):await this.assertedPersistence.removeCurrentUser()}queue(e){return this.operations=this.operations.then(e,e),this.operations}get assertedPersistence(){return $(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(e){e&&!this.frameworks.includes(e)&&(this.frameworks.push(e),this.frameworks.sort(),this.clientVersion=ze(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}async _getAdditionalHeaders(){const e={"X-Client-Version":this.clientVersion};this.app.options.appId&&(e["X-Firebase-gmpid"]=this.app.options.appId);var t=await(null===(t=this.heartbeatServiceProvider.getImmediate({optional:!0}))||void 0===t?void 0:t.getHeartbeatsHeader());t&&(e["X-Firebase-Client"]=t);t=await this._getAppCheckToken();return t&&(e["X-Firebase-AppCheck"]=t),e}async _getAppCheckToken(){var e,t,n=await(null===(t=this.appCheckServiceProvider.getImmediate({optional:!0}))||void 0===t?void 0:t.getToken());return null!=n&&n.error&&(e=`Error while retrieving App Check token: ${n.error}`,t=[],x.logLevel<=u.WARN&&x.warn(`Auth (${ji.SDK_VERSION}): ${e}`,...t)),null==n?void 0:n.token}}function $e(e){return k(e)}class Je{constructor(e){this.auth=e,this.observer=null,this.addObserver=function(e,t){const n=new T(e,t);return n.subscribe.bind(n)}(e=>this.observer=e)}get next(){return $(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}}let Ye={async loadJS(){throw new Error("Unable to load external scripts")},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};function Xe(e){return Ye.loadJS(e)}function Qe(e){return`__${e}${Math.floor(1e6*Math.random())}`}class Ze{constructor(e){this.type="recaptcha-enterprise",this.auth=$e(e)}async verify(i="verify",e=!1){async function t(r){if(!e){if(null==r.tenantId&&null!=r._agentRecaptchaConfig)return r._agentRecaptchaConfig.siteKey;if(null!=r.tenantId&&void 0!==r._tenantRecaptchaConfigs[r.tenantId])return r._tenantRecaptchaConfigs[r.tenantId].siteKey}return new Promise(async(t,n)=>{!async function(e,t){return oe(e,"GET","/v2/recaptchaConfig",se(e,t))}(r,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then(e=>{if(void 0!==e.recaptchaKey){e=new fe(e);return null==r.tenantId?r._agentRecaptchaConfig=e:r._tenantRecaptchaConfigs[r.tenantId]=e,t(e.siteKey)}n(new Error("recaptcha Enterprise site key undefined"))}).catch(e=>{n(e)})})}function s(e,t,n){const r=window.grecaptcha;pe(r)?r.enterprise.ready(()=>{r.enterprise.execute(e,{action:i}).then(e=>{t(e)}).catch(()=>{t("NO_RECAPTCHA")})}):n(Error("No reCAPTCHA enterprise script loaded."))}return new Promise((n,r)=>{t(this.auth).then(t=>{if(!e&&pe(window.grecaptcha))s(t,n,r);else if("undefined"!=typeof window){let e=Ye.recaptchaEnterpriseScript;0!==e.length&&(e+=t),Xe(e).then(()=>{s(t,n,r)}).catch(e=>{r(e)})}else r(new Error("RecaptchaVerifier is only supported in browser"))}).catch(e=>{r(e)})})}}async function et(e,t,n,r=!1){const i=new Ze(e);let s;try{s=await i.verify(n)}catch(e){s=await i.verify(n,!0)}t=Object.assign({},t);return r?Object.assign(t,{captchaResp:s}):Object.assign(t,{captchaResponse:s}),Object.assign(t,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(t,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"}),t}async function tt(t,n,r,i){if(null!==(e=t._getRecaptchaConfig())&&void 0!==e&&e.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")){var e=await et(t,n,r,"getOobCode"===r);return i(t,e)}return i(t,n).catch(async e=>{if("auth/missing-recaptcha-token"!==e.code)return Promise.reject(e);console.log(`${r} is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow.`);e=await et(t,n,r,"getOobCode"===r);return i(t,e)})}function nt(e,t,n){const r=$e(e);$(r._canInitEmulator,r,"emulator-config-failed"),$(/^https?:\/\//.test(t),r,"invalid-emulator-scheme");e=!(null==n||!n.disableWarnings);const i=rt(t);var{host:n,port:t}=function(e){const t=rt(e),n=/(\/\/)?([^?#/]+)/.exec(e.substr(t.length));if(!n)return{host:"",port:null};const r=n[2].split("@").pop()||"",i=/^(\[[^\]]+\])(:|$)/.exec(r);{if(i){var s=i[1];return{host:s,port:it(r.substr(s.length+1))}}var[e,s]=r.split(":");return{host:e,port:it(s)}}}(t);r.config.emulator={url:`${i}//${n}${null===t?"":`:${t}`}/`},r.settings.appVerificationDisabledForTesting=!0,r.emulatorConfig=Object.freeze({host:n,port:t,protocol:i.replace(":",""),options:Object.freeze({disableWarnings:e})}),e||function(){function e(){const e=document.createElement("p"),t=e.style;e.innerText="Running in emulator mode. Do not use with production credentials.",t.position="fixed",t.width="100%",t.backgroundColor="#ffffff",t.border=".1em solid #000000",t.color="#b50000",t.bottom="0px",t.left="0px",t.margin="0px",t.zIndex="10000",t.textAlign="center",e.classList.add("firebase-emulator-warning"),document.body.appendChild(e)}"undefined"!=typeof console&&"function"==typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials.");"undefined"!=typeof window&&"undefined"!=typeof document&&("loading"===document.readyState?window.addEventListener("DOMContentLoaded",e):e())}()}function rt(e){var t=e.indexOf(":");return t<0?"":e.substr(0,t+1)}function it(e){if(!e)return null;e=Number(e);return isNaN(e)?null:e}class st{constructor(e,t){this.providerId=e,this.signInMethod=t}toJSON(){return J("not implemented")}_getIdTokenResponse(e){return J("not implemented")}_linkToIdToken(e,t){return J("not implemented")}_getReauthenticationResolver(e){return J("not implemented")}}async function ot(e,t){return oe(e,"POST","/v1/accounts:resetPassword",se(e,t))}async function at(e,t){return oe(e,"POST","/v1/accounts:signUp",t)}async function ct(e,t){return ce(e,"POST","/v1/accounts:signInWithPassword",se(e,t))}async function ut(e,t){return oe(e,"POST","/v1/accounts:sendOobCode",se(e,t))}async function lt(e,t){return ut(e,t)}async function dt(e,t){return ut(e,t)}class ht extends st{constructor(e,t,n,r=null){super("password",n),this._email=e,this._password=t,this._tenantId=r}static _fromEmailAndPassword(e,t){return new ht(e,t,"password")}static _fromEmailAndCode(e,t,n=null){return new ht(e,t,"emailLink",n)}toJSON(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}}static fromJSON(e){e="string"==typeof e?JSON.parse(e):e;if(null!=e&&e.email&&null!=e&&e.password){if("password"===e.signInMethod)return this._fromEmailAndPassword(e.email,e.password);if("emailLink"===e.signInMethod)return this._fromEmailAndCode(e.email,e.password,e.tenantId)}return null}async _getIdTokenResponse(e){switch(this.signInMethod){case"password":return tt(e,{returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signInWithPassword",ct);case"emailLink":return async function(e,t){return ce(e,"POST","/v1/accounts:signInWithEmailLink",se(e,t))}(e,{email:this._email,oobCode:this._password});default:W(e,"internal-error")}}async _linkToIdToken(e,t){switch(this.signInMethod){case"password":return tt(e,{idToken:t,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",at);case"emailLink":return async function(e,t){return ce(e,"POST","/v1/accounts:signInWithEmailLink",se(e,t))}(e,{idToken:t,email:this._email,oobCode:this._password});default:W(e,"internal-error")}}_getReauthenticationResolver(e){return this._getIdTokenResponse(e)}}async function pt(e,t){return ce(e,"POST","/v1/accounts:signInWithIdp",se(e,t))}class ft extends st{constructor(){super(...arguments),this.pendingToken=null}static _fromParams(e){const t=new ft(e.providerId,e.signInMethod);return e.idToken||e.accessToken?(e.idToken&&(t.idToken=e.idToken),e.accessToken&&(t.accessToken=e.accessToken),e.nonce&&!e.pendingToken&&(t.nonce=e.nonce),e.pendingToken&&(t.pendingToken=e.pendingToken)):e.oauthToken&&e.oauthTokenSecret?(t.accessToken=e.oauthToken,t.secret=e.oauthTokenSecret):W("argument-error"),t}toJSON(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}}static fromJSON(e){var t="string"==typeof e?JSON.parse(e):e,{providerId:n,signInMethod:e}=t,t=O(t,["providerId","signInMethod"]);if(!n||!e)return null;const r=new ft(n,e);return r.idToken=t.idToken||void 0,r.accessToken=t.accessToken||void 0,r.secret=t.secret,r.nonce=t.nonce,r.pendingToken=t.pendingToken||null,r}_getIdTokenResponse(e){return pt(e,this.buildRequest())}_linkToIdToken(e,t){const n=this.buildRequest();return n.idToken=t,pt(e,n)}_getReauthenticationResolver(e){const t=this.buildRequest();return t.autoCreate=!1,pt(e,t)}buildRequest(){const e={requestUri:"http://localhost",returnSecureToken:!0};if(this.pendingToken)e.pendingToken=this.pendingToken;else{const t={};this.idToken&&(t.id_token=this.idToken),this.accessToken&&(t.access_token=this.accessToken),this.secret&&(t.oauth_token_secret=this.secret),t.providerId=this.providerId,this.nonce&&!this.pendingToken&&(t.nonce=this.nonce),e.postBody=I(t)}return e}}const mt={USER_NOT_FOUND:"user-not-found"};class vt extends st{constructor(e){super("phone","phone"),this.params=e}static _fromVerification(e,t){return new vt({verificationId:e,verificationCode:t})}static _fromTokenResponse(e,t){return new vt({phoneNumber:e,temporaryProof:t})}_getIdTokenResponse(e){return async function(e,t){return ce(e,"POST","/v1/accounts:signInWithPhoneNumber",se(e,t))}(e,this._makeVerificationRequest())}_linkToIdToken(e,t){return async function(e,t){if((t=await ce(e,"POST","/v1/accounts:signInWithPhoneNumber",se(e,t))).temporaryProof)throw de(e,"account-exists-with-different-credential",t);return t}(e,Object.assign({idToken:t},this._makeVerificationRequest()))}_getReauthenticationResolver(e){return async function(e,t){return ce(e,"POST","/v1/accounts:signInWithPhoneNumber",se(e,Object.assign(Object.assign({},t),{operation:"REAUTH"})),mt)}(e,this._makeVerificationRequest())}_makeVerificationRequest(){var{temporaryProof:e,phoneNumber:t,verificationId:n,verificationCode:r}=this.params;return e&&t?{temporaryProof:e,phoneNumber:t}:{sessionInfo:n,code:r}}toJSON(){const e={providerId:this.providerId};return this.params.phoneNumber&&(e.phoneNumber=this.params.phoneNumber),this.params.temporaryProof&&(e.temporaryProof=this.params.temporaryProof),this.params.verificationCode&&(e.verificationCode=this.params.verificationCode),this.params.verificationId&&(e.verificationId=this.params.verificationId),e}static fromJSON(e){var{verificationId:t,verificationCode:n,phoneNumber:r,temporaryProof:e}=e="string"==typeof e?JSON.parse(e):e;return n||t||r||e?new vt({verificationId:t,verificationCode:n,phoneNumber:r,temporaryProof:e}):null}}class gt{constructor(e){var t=w(b(e)),n=null!==(r=t.apiKey)&&void 0!==r?r:null,r=null!==(e=t.oobCode)&&void 0!==e?e:null,e=function(e){switch(e){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}}(null!==(e=t.mode)&&void 0!==e?e:null);$(n&&r&&e,"argument-error"),this.apiKey=n,this.operation=e,this.code=r,this.continueUrl=null!==(r=t.continueUrl)&&void 0!==r?r:null,this.languageCode=null!==(r=t.languageCode)&&void 0!==r?r:null,this.tenantId=null!==(t=t.tenantId)&&void 0!==t?t:null}static parseLink(e){var t,n,r,t=(n=w(b(t=e)).link,r=n?w(b(n)).deep_link_id:null,((e=w(b(t)).deep_link_id)?w(b(e)).link:null)||e||r||n||t);try{return new gt(t)}catch(e){return null}}}class yt{constructor(){this.providerId=yt.PROVIDER_ID}static credential(e,t){return ht._fromEmailAndPassword(e,t)}static credentialWithLink(e,t){t=gt.parseLink(t);return $(t,"argument-error"),ht._fromEmailAndCode(e,t.code,t.tenantId)}}yt.PROVIDER_ID="password",yt.EMAIL_PASSWORD_SIGN_IN_METHOD="password",yt.EMAIL_LINK_SIGN_IN_METHOD="emailLink";class _t{constructor(e){this.providerId=e,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(e){this.defaultLanguageCode=e}setCustomParameters(e){return this.customParameters=e,this}getCustomParameters(){return this.customParameters}}class It extends _t{constructor(){super(...arguments),this.scopes=[]}addScope(e){return this.scopes.includes(e)||this.scopes.push(e),this}getScopes(){return[...this.scopes]}}class wt extends It{static credentialFromJSON(e){e="string"==typeof e?JSON.parse(e):e;return $("providerId"in e&&"signInMethod"in e,"argument-error"),ft._fromParams(e)}credential(e){return this._credential(Object.assign(Object.assign({},e),{nonce:e.rawNonce}))}_credential(e){return $(e.idToken||e.accessToken,"argument-error"),ft._fromParams(Object.assign(Object.assign({},e),{providerId:this.providerId,signInMethod:this.providerId}))}static credentialFromResult(e){return wt.oauthCredentialFromTaggedObject(e)}static credentialFromError(e){return wt.oauthCredentialFromTaggedObject(e.customData||{})}static oauthCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:n,oauthTokenSecret:r,pendingToken:i,nonce:s,providerId:e}=e;if(!(n||r||t||i))return null;if(!e)return null;try{return new wt(e)._credential({idToken:t,accessToken:n,nonce:s,pendingToken:i})}catch(e){return null}}}class bt extends It{constructor(){super("facebook.com")}static credential(e){return ft._fromParams({providerId:bt.PROVIDER_ID,signInMethod:bt.FACEBOOK_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return bt.credentialFromTaggedObject(e)}static credentialFromError(e){return bt.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return bt.credential(e.oauthAccessToken)}catch(e){return null}}}bt.FACEBOOK_SIGN_IN_METHOD="facebook.com",bt.PROVIDER_ID="facebook.com";class Tt extends It{constructor(){super("google.com"),this.addScope("profile")}static credential(e,t){return ft._fromParams({providerId:Tt.PROVIDER_ID,signInMethod:Tt.GOOGLE_SIGN_IN_METHOD,idToken:e,accessToken:t})}static credentialFromResult(e){return Tt.credentialFromTaggedObject(e)}static credentialFromError(e){return Tt.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:e}=e;if(!t&&!e)return null;try{return Tt.credential(t,e)}catch(e){return null}}}Tt.GOOGLE_SIGN_IN_METHOD="google.com",Tt.PROVIDER_ID="google.com";class Et extends It{constructor(){super("github.com")}static credential(e){return ft._fromParams({providerId:Et.PROVIDER_ID,signInMethod:Et.GITHUB_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return Et.credentialFromTaggedObject(e)}static credentialFromError(e){return Et.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return Et.credential(e.oauthAccessToken)}catch(e){return null}}}Et.GITHUB_SIGN_IN_METHOD="github.com",Et.PROVIDER_ID="github.com";class kt extends st{constructor(e,t){super(e,e),this.pendingToken=t}_getIdTokenResponse(e){return pt(e,this.buildRequest())}_linkToIdToken(e,t){const n=this.buildRequest();return n.idToken=t,pt(e,n)}_getReauthenticationResolver(e){const t=this.buildRequest();return t.autoCreate=!1,pt(e,t)}toJSON(){return{signInMethod:this.signInMethod,providerId:this.providerId,pendingToken:this.pendingToken}}static fromJSON(e){var{providerId:t,signInMethod:n,pendingToken:e}="string"==typeof e?JSON.parse(e):e;return t&&n&&e&&t===n?new kt(t,e):null}static _create(e,t){return new kt(e,t)}buildRequest(){return{requestUri:"http://localhost",returnSecureToken:!0,pendingToken:this.pendingToken}}}class St extends _t{constructor(e){$(e.startsWith("saml."),"argument-error"),super(e)}static credentialFromResult(e){return St.samlCredentialFromTaggedObject(e)}static credentialFromError(e){return St.samlCredentialFromTaggedObject(e.customData||{})}static credentialFromJSON(e){e=kt.fromJSON(e);return $(e,"argument-error"),e}static samlCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{pendingToken:t,providerId:e}=e;if(!t||!e)return null;try{return kt._create(e,t)}catch(e){return null}}}class At extends It{constructor(){super("twitter.com")}static credential(e,t){return ft._fromParams({providerId:At.PROVIDER_ID,signInMethod:At.TWITTER_SIGN_IN_METHOD,oauthToken:e,oauthTokenSecret:t})}static credentialFromResult(e){return At.credentialFromTaggedObject(e)}static credentialFromError(e){return At.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthAccessToken:t,oauthTokenSecret:e}=e;if(!t||!e)return null;try{return At.credential(t,e)}catch(e){return null}}}async function Rt(e,t){return ce(e,"POST","/v1/accounts:signUp",se(e,t))}At.TWITTER_SIGN_IN_METHOD="twitter.com",At.PROVIDER_ID="twitter.com";class Pt{constructor(e){this.user=e.user,this.providerId=e.providerId,this._tokenResponse=e._tokenResponse,this.operationType=e.operationType}static async _fromIdTokenResponse(e,t,n,r=!1){e=await Ae._fromIdTokenResponse(e,n,r),r=Ot(n);return new Pt({user:e,providerId:r,_tokenResponse:n,operationType:t})}static async _forOperation(e,t,n){await e._updateTokensIfNecessary(n,!0);var r=Ot(n);return new Pt({user:e,providerId:r,_tokenResponse:n,operationType:t})}}function Ot(e){return e.providerId||("phoneNumber"in e?"phone":null)}class Ct extends g{constructor(e,t,n,r){super(t.code,t.message),this.operationType=n,this.user=r,Object.setPrototypeOf(this,Ct.prototype),this.customData={appName:e.name,tenantId:null!==(e=e.tenantId)&&void 0!==e?e:void 0,_serverResponse:t.customData._serverResponse,operationType:n}}static _fromErrorAndOperation(e,t,n,r){return new Ct(e,t,n,r)}}function Nt(t,n,e,r){const i="reauthenticate"===n?e._getReauthenticationResolver(t):e._getIdTokenResponse(t);return i.catch(e=>{if("auth/multi-factor-auth-required"===e.code)throw Ct._fromErrorAndOperation(t,e,n,r);throw e})}function Lt(e){return new Set(e.map(({providerId:e})=>e).filter(e=>!!e))}async function Dt(e,t){const n=k(e);await Mt(!0,n,t);var t=(e=n.auth,t={idToken:await n.getIdToken(),deleteProvider:[t]},await oe(e,"POST","/v1/accounts:update",t))["providerUserInfo"];const r=Lt(t||[]);return n.providerData=n.providerData.filter(e=>r.has(e.providerId)),r.has("phone")||(n.phoneNumber=null),await n.auth._persistUserIfCurrent(n),n}async function Ut(e,t,n=!1){n=await Ie(e,t._linkToIdToken(e.auth,await e.getIdToken()),n);return Pt._forOperation(e,"link",n)}async function Mt(e,t,n){await Te(t);const r=Lt(t.providerData);var i=!1===e?"provider-already-linked":"no-such-provider";$(r.has(n)===e,t.auth,i)}async function Ft(e,t,n=!1){var r=e["auth"];if(ji._isFirebaseServerApp(r.app))return Promise.reject(B(r));var i="reauthenticate";try{var s=await Ie(e,Nt(r,i,t,e),n);$(s.idToken,r,"internal-error");var o=ye(s.idToken);$(o,r,"internal-error");var a=o["sub"];return $(e.uid===a,r,"user-mismatch"),Pt._forOperation(e,i,s)}catch(e){throw"auth/user-not-found"===(null==e?void 0:e.code)&&W(r,"user-mismatch"),e}}async function Vt(e,t,n=!1){if(ji._isFirebaseServerApp(e.app))return Promise.reject(B(e));t=await Nt(e,"signIn",t),t=await Pt._fromIdTokenResponse(e,"signIn",t);return n||await e._updateCurrentUser(t.user),t}async function jt(e,t){return Vt($e(e),t)}async function xt(e,t){e=k(e);return await Mt(!1,e,t.providerId),Ut(e,t)}async function Ht(e,t){return Ft(k(e),t)}async function Wt(e,t){if(ji._isFirebaseServerApp(e.app))return Promise.reject(B(e));const n=$e(e);t=await ce(n,"POST","/v1/accounts:signInWithCustomToken",se(n,{token:t,returnSecureToken:!0})),t=await Pt._fromIdTokenResponse(n,"signIn",t);return await n._updateCurrentUser(t.user),t}class qt{constructor(e,t){this.factorId=e,this.uid=t.mfaEnrollmentId,this.enrollmentTime=new Date(t.enrolledAt).toUTCString(),this.displayName=t.displayName}static _fromServerResponse(e,t){return"phoneInfo"in t?zt._fromServerResponse(e,t):"totpInfo"in t?Bt._fromServerResponse(e,t):W(e,"internal-error")}}class zt extends qt{constructor(e){super("phone",e),this.phoneNumber=e.phoneInfo}static _fromServerResponse(e,t){return new zt(t)}}class Bt extends qt{constructor(e){super("totp",e)}static _fromServerResponse(e,t){return new Bt(t)}}function Gt(e,t,n){var r;$(0<(null===(r=n.url)||void 0===r?void 0:r.length),e,"invalid-continue-uri"),$(void 0===n.dynamicLinkDomain||0<n.dynamicLinkDomain.length,e,"invalid-dynamic-link-domain"),t.continueUrl=n.url,t.dynamicLinkDomain=n.dynamicLinkDomain,t.canHandleCodeInApp=n.handleCodeInApp,n.iOS&&($(0<n.iOS.bundleId.length,e,"missing-ios-bundle-id"),t.iOSBundleId=n.iOS.bundleId),n.android&&($(0<n.android.packageName.length,e,"missing-android-pkg-name"),t.androidInstallApp=n.android.installApp,t.androidMinimumVersionCode=n.android.minimumVersion,t.androidPackageName=n.android.packageName)}async function Kt(e){const t=$e(e);t._getPasswordPolicyInternal()&&await t._updatePasswordPolicy()}async function $t(e,t){await oe(e=k(e),"POST","/v1/accounts:update",se(e,{oobCode:t}))}async function Jt(e,t){var n=k(e),r=await ot(n,{oobCode:t}),t=r.requestType;switch($(t,n,"internal-error"),t){case"EMAIL_SIGNIN":break;case"VERIFY_AND_CHANGE_EMAIL":$(r.newEmail,n,"internal-error");break;case"REVERT_SECOND_FACTOR_ADDITION":$(r.mfaInfo,n,"internal-error");default:$(r.email,n,"internal-error")}let i=null;return r.mfaInfo&&(i=qt._fromServerResponse($e(n),r.mfaInfo)),{data:{email:("VERIFY_AND_CHANGE_EMAIL"===r.requestType?r.newEmail:r.email)||null,previousEmail:("VERIFY_AND_CHANGE_EMAIL"===r.requestType?r.email:r.newEmail)||null,multiFactorInfo:i},operation:t}}async function Yt(e,t){var n=Q()?X():"http://localhost",n=(await oe(e=k(e),"POST","/v1/accounts:createAuthUri",se(e,{identifier:t,continueUri:n})))["signinMethods"];return n||[]}async function Xt(e,t){var n=k(e),r={requestType:"VERIFY_EMAIL",idToken:await e.getIdToken()};t&&Gt(n.auth,r,t);var r=(await ut(n.auth,r))["email"];r!==e.email&&await e.reload()}async function Qt(e,t,n){var r=k(e),t={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:await e.getIdToken(),newEmail:t};n&&Gt(r.auth,t,n);var t=(await ut(r.auth,t))["email"];t!==e.email&&await e.reload()}async function Zt(e,{displayName:t,photoURL:n}){if(void 0!==t||void 0!==n){const r=k(e);e=await r.getIdToken(),n=await Ie(r,async function(e,t){return oe(e,"POST","/v1/accounts:update",t)}(r.auth,{idToken:e,displayName:t,photoUrl:n,returnSecureToken:!0}));r.displayName=n.displayName||null,r.photoURL=n.photoUrl||null;const i=r.providerData.find(({providerId:e})=>"password"===e);i&&(i.displayName=r.displayName,i.photoURL=r.photoURL),await r._updateTokensIfNecessary(n)}}async function en(e,t,n){var r=e["auth"];const i={idToken:await e.getIdToken(),returnSecureToken:!0};t&&(i.email=t),n&&(i.password=n);r=await Ie(e,async function(e,t){return oe(e,"POST","/v1/accounts:update",t)}(r,i));await e._updateTokensIfNecessary(r,!0)}class tn{constructor(e,t,n={}){this.isNewUser=e,this.providerId=t,this.profile=n}}class nn extends tn{constructor(e,t,n,r){super(e,t,n),this.username=r}}class rn extends tn{constructor(e,t){super(e,"facebook.com",t)}}class sn extends nn{constructor(e,t){super(e,"github.com",t,"string"==typeof(null==t?void 0:t.login)?null==t?void 0:t.login:null)}}class on extends tn{constructor(e,t){super(e,"google.com",t)}}class an extends nn{constructor(e,t,n){super(e,"twitter.com",t,n)}}function cn(e){var{user:t,_tokenResponse:e}=e;return t.isAnonymous&&!e?{providerId:null,isNewUser:!1,profile:null}:function(e){if(!e)return null;var t=e["providerId"],n=e.rawUserInfo?JSON.parse(e.rawUserInfo):{},r=e.isNewUser||"identitytoolkit#SignupNewUserResponse"===e.kind;if(!t&&null!=e&&e.idToken){var i=null===(i=null===(i=ye(e.idToken))||void 0===i?void 0:i.firebase)||void 0===i?void 0:i.sign_in_provider;if(i){i="anonymous"!==i&&"custom"!==i?i:null;return new tn(r,i)}}if(!t)return null;switch(t){case"facebook.com":return new rn(r,n);case"github.com":return new sn(r,n);case"google.com":return new on(r,n);case"twitter.com":return new an(r,n,e.screenName||null);case"custom":case"anonymous":return new tn(r,null);default:return new tn(r,t,n)}}(e)}class un{constructor(e,t,n){this.type=e,this.credential=t,this.user=n}static _fromIdtoken(e,t){return new un("enroll",e,t)}static _fromMfaPendingCredential(e){return new un("signin",e)}toJSON(){return{multiFactorSession:{["enroll"===this.type?"idToken":"pendingCredential"]:this.credential}}}static fromJSON(e){var t;if(null!=e&&e.multiFactorSession){if(null!==(t=e.multiFactorSession)&&void 0!==t&&t.pendingCredential)return un._fromMfaPendingCredential(e.multiFactorSession.pendingCredential);if(null!==(t=e.multiFactorSession)&&void 0!==t&&t.idToken)return un._fromIdtoken(e.multiFactorSession.idToken)}return null}}class ln{constructor(e,t,n){this.session=e,this.hints=t,this.signInResolver=n}static _fromError(e,r){const i=$e(e),s=r.customData._serverResponse;e=(s.mfaInfo||[]).map(e=>qt._fromServerResponse(i,e));$(s.mfaPendingCredential,i,"internal-error");const o=un._fromMfaPendingCredential(s.mfaPendingCredential);return new ln(o,e,async e=>{e=await e._process(i,o);delete s.mfaInfo,delete s.mfaPendingCredential;var t=Object.assign(Object.assign({},s),{idToken:e.idToken,refreshToken:e.refreshToken});switch(r.operationType){case"signIn":var n=await Pt._fromIdTokenResponse(i,r.operationType,t);return await i._updateCurrentUser(n.user),n;case"reauthenticate":return $(r.user,i,"internal-error"),Pt._forOperation(r.user,r.operationType,t);default:W(i,"internal-error")}})}async resolveSignIn(e){return this.signInResolver(e)}}class dn{constructor(t){this.user=t,this.enrolledFactors=[],t._onReload(e=>{e.mfaInfo&&(this.enrolledFactors=e.mfaInfo.map(e=>qt._fromServerResponse(t.auth,e)))})}static _fromUser(e){return new dn(e)}async getSession(){return un._fromIdtoken(await this.user.getIdToken(),this.user)}async enroll(e,t){const n=e;e=await this.getSession(),t=await Ie(this.user,n._process(this.user.auth,e,t));return await this.user._updateTokensIfNecessary(t),this.user.reload()}async unenroll(e){const t="string"==typeof e?e:e.uid;var n,r,e=await this.user.getIdToken();try{var i=await Ie(this.user,(n=this.user.auth,r={idToken:e,mfaEnrollmentId:t},oe(n,"POST","/v2/accounts/mfaEnrollment:withdraw",se(n,r))));this.enrolledFactors=this.enrolledFactors.filter(({uid:e})=>e!==t),await this.user._updateTokensIfNecessary(i),await this.user.reload()}catch(e){throw e}}}const hn=new WeakMap;const pn="__sak";class fn{constructor(e,t){this.storageRetriever=e,this.type=t}_isAvailable(){try{return this.storage?(this.storage.setItem(pn,"1"),this.storage.removeItem(pn),Promise.resolve(!0)):Promise.resolve(!1)}catch(e){return Promise.resolve(!1)}}_set(e,t){return this.storage.setItem(e,JSON.stringify(t)),Promise.resolve()}_get(e){e=this.storage.getItem(e);return Promise.resolve(e?JSON.parse(e):null)}_remove(e){return this.storage.removeItem(e),Promise.resolve()}get storage(){return this.storageRetriever()}}class mn extends fn{constructor(){super(()=>window.localStorage,"LOCAL"),this.boundEventHandler=(e,t)=>this.onStorageEvent(e,t),this.listeners={},this.localCache={},this.pollTimer=null,this.fallbackToPolling=qe(),this._shouldAllowMigration=!0}forAllChangedKeys(e){for(const r of Object.keys(this.listeners)){var t=this.storage.getItem(r),n=this.localCache[r];t!==n&&e(r,n,t)}}onStorageEvent(e,t=!1){if(e.key){const i=e.key;t?this.detachListener():this.stopPolling();var n=()=>{var e=this.storage.getItem(i);!t&&this.localCache[i]===e||this.notifyListeners(i,e)},r=this.storage.getItem(i);m()&&10===document.documentMode&&r!==e.newValue&&e.newValue!==e.oldValue?setTimeout(n,10):n()}else this.forAllChangedKeys((e,t,n)=>{this.notifyListeners(e,n)})}notifyListeners(e,t){this.localCache[e]=t;e=this.listeners[e];if(e)for(const n of Array.from(e))n(t&&JSON.parse(t))}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>{this.forAllChangedKeys((e,t,n)=>{this.onStorageEvent(new StorageEvent("storage",{key:e,oldValue:t,newValue:n}),!0)})},1e3)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}attachListener(){window.addEventListener("storage",this.boundEventHandler)}detachListener(){window.removeEventListener("storage",this.boundEventHandler)}_addListener(e,t){0===Object.keys(this.listeners).length&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[e]||(this.listeners[e]=new Set,this.localCache[e]=this.storage.getItem(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size&&delete this.listeners[e]),0===Object.keys(this.listeners).length&&(this.detachListener(),this.stopPolling())}async _set(e,t){await super._set(e,t),this.localCache[e]=JSON.stringify(t)}async _get(e){var t=await super._get(e);return this.localCache[e]=JSON.stringify(t),t}async _remove(e){await super._remove(e),delete this.localCache[e]}}mn.type="LOCAL";const vn=mn;class gn extends fn{constructor(){super(()=>window.sessionStorage,"SESSION")}_addListener(e,t){}_removeListener(e,t){}}gn.type="SESSION";const yn=gn;class _n{constructor(e){this.eventTarget=e,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}static _getInstance(t){var e=this.receivers.find(e=>e.isListeningto(t));if(e)return e;e=new _n(t);return this.receivers.push(e),e}isListeningto(e){return this.eventTarget===e}async handleEvent(e){const t=e,{eventId:n,eventType:r,data:i}=t.data;e=this.handlersMap[r];null!=e&&e.size&&(t.ports[0].postMessage({status:"ack",eventId:n,eventType:r}),e=Array.from(e).map(async e=>e(t.origin,i)),e=await Promise.all(e.map(async e=>{try{return{fulfilled:!0,value:await e}}catch(e){return{fulfilled:!1,reason:e}}})),t.ports[0].postMessage({status:"done",eventId:n,eventType:r,response:e}))}_subscribe(e,t){0===Object.keys(this.handlersMap).length&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[e]||(this.handlersMap[e]=new Set),this.handlersMap[e].add(t)}_unsubscribe(e,t){this.handlersMap[e]&&t&&this.handlersMap[e].delete(t),t&&0!==this.handlersMap[e].size||delete this.handlersMap[e],0===Object.keys(this.handlersMap).length&&this.eventTarget.removeEventListener("message",this.boundEventHandler)}}function In(e="",t=10){let n="";for(let e=0;e<t;e++)n+=Math.floor(10*Math.random());return e+n}_n.receivers=[];class wn{constructor(e){this.target=e,this.handlers=new Set}removeMessageHandler(e){e.messageChannel&&(e.messageChannel.port1.removeEventListener("message",e.onMessage),e.messageChannel.port1.close()),this.handlers.delete(e)}async _send(e,t,o=50){const a="undefined"!=typeof MessageChannel?new MessageChannel:null;if(!a)throw new Error("connection_unavailable");let c,u;return new Promise((n,r)=>{const i=In("",20);a.port1.start();const s=setTimeout(()=>{r(new Error("unsupported_event"))},o);u={messageChannel:a,onMessage(e){var t=e;if(t.data.eventId===i)switch(t.data.status){case"ack":clearTimeout(s),c=setTimeout(()=>{r(new Error("timeout"))},3e3);break;case"done":clearTimeout(c),n(t.data.response);break;default:clearTimeout(s),clearTimeout(c),r(new Error("invalid_response"))}}},this.handlers.add(u),a.port1.addEventListener("message",u.onMessage),this.target.postMessage({eventType:e,eventId:i,data:t},[a.port2])}).finally(()=>{u&&this.removeMessageHandler(u)})}}function bn(){return window}function Tn(){return void 0!==bn().WorkerGlobalScope&&"function"==typeof bn().importScripts}const En="firebaseLocalStorageDb",kn="firebaseLocalStorage",Sn="fbase_key";class An{constructor(e){this.request=e}toPromise(){return new Promise((e,t)=>{this.request.addEventListener("success",()=>{e(this.request.result)}),this.request.addEventListener("error",()=>{t(this.request.error)})})}}function Rn(e,t){return e.transaction([kn],t?"readwrite":"readonly").objectStore(kn)}function Pn(){const r=indexedDB.open(En,1);return new Promise((n,t)=>{r.addEventListener("error",()=>{t(r.error)}),r.addEventListener("upgradeneeded",()=>{const e=r.result;try{e.createObjectStore(kn,{keyPath:Sn})}catch(e){t(e)}}),r.addEventListener("success",async()=>{const e=r.result;var t;e.objectStoreNames.contains(kn)?n(e):(e.close(),t=indexedDB.deleteDatabase(En),await new An(t).toPromise(),n(await Pn()))})})}async function On(e,t,n){n=Rn(e,!0).put({fbase_key:t,value:n});return new An(n).toPromise()}function Cn(e,t){t=Rn(e,!0).delete(t);return new An(t).toPromise()}class Nn{constructor(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then(()=>{},()=>{})}async _openDb(){return this.db||(this.db=await Pn(),this.db)}async _withRetries(e){let t=0;for(;;)try{return e(await this._openDb())}catch(e){if(3<t++)throw e;this.db&&(this.db.close(),this.db=void 0)}}async initializeServiceWorkerMessaging(){return Tn()?this.initializeReceiver():this.initializeSender()}async initializeReceiver(){this.receiver=_n._getInstance(Tn()?self:null),this.receiver._subscribe("keyChanged",async(e,t)=>{const n=await this._poll();return{keyProcessed:n.includes(t.key)}}),this.receiver._subscribe("ping",async(e,t)=>["keyChanged"])}async initializeSender(){var e,t,n;this.activeServiceWorker=await async function(){if(null===navigator||void 0===navigator||!navigator.serviceWorker)return null;try{return(await navigator.serviceWorker.ready).active}catch(e){return null}}(),this.activeServiceWorker&&(this.sender=new wn(this.activeServiceWorker),(n=await this.sender._send("ping",{},800))&&null!==(e=n[0])&&void 0!==e&&e.fulfilled&&null!==(t=n[0])&&void 0!==t&&t.value.includes("keyChanged")&&(this.serviceWorkerReceiverAvailable=!0))}async notifyServiceWorker(e){var t;if(this.sender&&this.activeServiceWorker&&((null===(t=null===navigator||void 0===navigator?void 0:navigator.serviceWorker)||void 0===t?void 0:t.controller)||null)===this.activeServiceWorker)try{await this.sender._send("keyChanged",{key:e},this.serviceWorkerReceiverAvailable?800:50)}catch(e){}}async _isAvailable(){try{if(!indexedDB)return!1;var e=await Pn();return await On(e,pn,"1"),await Cn(e,pn),!0}catch(e){}return!1}async _withPendingWrite(e){this.pendingWrites++;try{await e()}finally{this.pendingWrites--}}async _set(t,n){return this._withPendingWrite(async()=>(await this._withRetries(e=>On(e,t,n)),this.localCache[t]=n,this.notifyServiceWorker(t)))}async _get(t){var e=await this._withRetries(e=>async function(e,t){return t=Rn(e,!1).get(t),void 0===(t=await new An(t).toPromise())?null:t.value}(e,t));return this.localCache[t]=e}async _remove(t){return this._withPendingWrite(async()=>(await this._withRetries(e=>Cn(e,t)),delete this.localCache[t],this.notifyServiceWorker(t)))}async _poll(){var e=await this._withRetries(e=>{e=Rn(e,!1).getAll();return new An(e).toPromise()});if(!e)return[];if(0!==this.pendingWrites)return[];const t=[],n=new Set;if(0!==e.length)for(var{fbase_key:r,value:i}of e)n.add(r),JSON.stringify(this.localCache[r])!==JSON.stringify(i)&&(this.notifyListeners(r,i),t.push(r));for(const s of Object.keys(this.localCache))this.localCache[s]&&!n.has(s)&&(this.notifyListeners(s,null),t.push(s));return t}notifyListeners(e,t){this.localCache[e]=t;e=this.listeners[e];if(e)for(const n of Array.from(e))n(t)}startPolling(){this.stopPolling(),this.pollTimer=setInterval(async()=>this._poll(),800)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}_addListener(e,t){0===Object.keys(this.listeners).length&&this.startPolling(),this.listeners[e]||(this.listeners[e]=new Set,this._get(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size&&delete this.listeners[e]),0===Object.keys(this.listeners).length&&this.stopPolling()}}Nn.type="LOCAL";const Ln=Nn;class Dn{constructor(e){this.auth=e,this.counter=1e12,this._widgets=new Map}render(e,t){var n=this.counter;return this._widgets.set(n,new Un(e,this.auth.name,t||{})),this.counter++,n}reset(e){var t=e||1e12;null===(e=this._widgets.get(t))||void 0===e||e.delete(),this._widgets.delete(t)}getResponse(e){return(null===(e=this._widgets.get(e||1e12))||void 0===e?void 0:e.getResponse())||""}async execute(e){return null===(e=this._widgets.get(e||1e12))||void 0===e||e.execute(),""}}class Un{constructor(e,t,n){this.params=n,this.timerId=null,this.deleted=!1,this.responseToken=null,this.clickHandler=()=>{this.execute()};e="string"==typeof e?document.getElementById(e):e;$(e,"argument-error",{appName:t}),this.container=e,this.isVisible="invisible"!==this.params.size,this.isVisible?this.execute():this.container.addEventListener("click",this.clickHandler)}getResponse(){return this.checkIfDeleted(),this.responseToken}delete(){this.checkIfDeleted(),this.deleted=!0,this.timerId&&(clearTimeout(this.timerId),this.timerId=null),this.container.removeEventListener("click",this.clickHandler)}execute(){this.checkIfDeleted(),this.timerId||(this.timerId=window.setTimeout(()=>{this.responseToken=function(t){const n=[],r="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let e=0;e<t;e++)n.push(r.charAt(Math.floor(Math.random()*r.length)));return n.join("")}(50);const{callback:e,"expired-callback":t}=this.params;if(e)try{e(this.responseToken)}catch(e){}this.timerId=window.setTimeout(()=>{if(this.timerId=null,this.responseToken=null,t)try{t()}catch(e){}this.isVisible&&this.execute()},6e4)},500))}checkIfDeleted(){if(this.deleted)throw new Error("reCAPTCHA mock was already deleted!")}}const Mn=Qe("rcb"),Fn=new ee(3e4,6e4);class Vn{constructor(){var e;this.hostLanguage="",this.counter=0,this.librarySeparatelyLoaded=!(null===(e=bn().grecaptcha)||void 0===e||!e.render)}load(s,o=""){var e;return $((e=o).length<=6&&/^\s*[a-zA-Z0-9\-]*\s*$/.test(e),s,"argument-error"),this.shouldResolveImmediately(o)&&he(bn().grecaptcha)?Promise.resolve(bn().grecaptcha):new Promise((t,r)=>{const i=bn().setTimeout(()=>{r(q(s,"network-request-failed"))},Fn.get());bn()[Mn]=()=>{bn().clearTimeout(i),delete bn()[Mn];const e=bn().grecaptcha;if(e&&he(e)){const n=e.render;e.render=(e,t)=>{t=n(e,t);return this.counter++,t},this.hostLanguage=o,t(e)}else r(q(s,"internal-error"))},Xe(`${Ye.recaptchaV2Script}?${I({onload:Mn,render:"explicit",hl:o})}`).catch(()=>{clearTimeout(i),r(q(s,"internal-error"))})})}clearedOneInstance(){this.counter--}shouldResolveImmediately(e){var t;return!(null===(t=bn().grecaptcha)||void 0===t||!t.render)&&(e===this.hostLanguage||0<this.counter||this.librarySeparatelyLoaded)}}class jn{async load(e){return new Dn(e)}clearedOneInstance(){}}const xn="recaptcha",Hn={theme:"light",type:"image"};class Wn{constructor(e,t,n=Object.assign({},Hn)){this.parameters=n,this.type=xn,this.destroyed=!1,this.widgetId=null,this.tokenChangeListeners=new Set,this.renderPromise=null,this.recaptcha=null,this.auth=$e(e),this.isInvisible="invisible"===this.parameters.size,$("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment");t="string"==typeof t?document.getElementById(t):t;$(t,this.auth,"argument-error"),this.container=t,this.parameters.callback=this.makeTokenCallback(this.parameters.callback),this._recaptchaLoader=new(this.auth.settings.appVerificationDisabledForTesting?jn:Vn),this.validateStartingState()}async verify(){this.assertNotDestroyed();const e=await this.render(),r=this.getAssertedRecaptcha();var t=r.getResponse(e);return t||new Promise(t=>{const n=e=>{e&&(this.tokenChangeListeners.delete(n),t(e))};this.tokenChangeListeners.add(n),this.isInvisible&&r.execute(e)})}render(){try{this.assertNotDestroyed()}catch(e){return Promise.reject(e)}return this.renderPromise||(this.renderPromise=this.makeRenderPromise().catch(e=>{throw this.renderPromise=null,e}),this.renderPromise)}_reset(){this.assertNotDestroyed(),null!==this.widgetId&&this.getAssertedRecaptcha().reset(this.widgetId)}clear(){this.assertNotDestroyed(),this.destroyed=!0,this._recaptchaLoader.clearedOneInstance(),this.isInvisible||this.container.childNodes.forEach(e=>{this.container.removeChild(e)})}validateStartingState(){$(!this.parameters.sitekey,this.auth,"argument-error"),$(this.isInvisible||!this.container.hasChildNodes(),this.auth,"argument-error"),$("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment")}makeTokenCallback(n){return t=>{if(this.tokenChangeListeners.forEach(e=>e(t)),"function"==typeof n)n(t);else if("string"==typeof n){const e=bn()[n];"function"==typeof e&&e(t)}}}assertNotDestroyed(){$(!this.destroyed,this.auth,"internal-error")}async makeRenderPromise(){if(await this.init(),!this.widgetId){let e=this.container;var t;this.isInvisible||(t=document.createElement("div"),e.appendChild(t),e=t),this.widgetId=this.getAssertedRecaptcha().render(e,this.parameters)}return this.widgetId}async init(){$(Q()&&!Tn(),this.auth,"internal-error"),await function(){let t=null;return new Promise(e=>{"complete"!==document.readyState?(t=()=>e(),window.addEventListener("load",t)):e()}).catch(e=>{throw t&&window.removeEventListener("load",t),e})}(),this.recaptcha=await this._recaptchaLoader.load(this.auth,this.auth.languageCode||void 0);var e=await((await oe(this.auth,"GET","/v1/recaptchaParams")).recaptchaSiteKey||"");$(e,this.auth,"internal-error"),this.parameters.sitekey=e}getAssertedRecaptcha(){return $(this.recaptcha,this.auth,"internal-error"),this.recaptcha}}class qn{constructor(e,t){this.verificationId=e,this.onConfirmation=t}confirm(e){e=vt._fromVerification(this.verificationId,e);return this.onConfirmation(e)}}async function zn(t,n,r){var i,s,o,a,c,u,l=await r.verify();try{$("string"==typeof l,t,"argument-error"),$(r.type===xn,t,"argument-error");let e;if(e="string"==typeof n?{phoneNumber:n}:n,"session"in e){var d=e.session;if("phoneNumber"in e)return $("enroll"===d.type,t,"internal-error"),(c=t,u={idToken:d.credential,phoneEnrollmentInfo:{phoneNumber:e.phoneNumber,recaptchaToken:l}},await oe(c,"POST","/v2/accounts/mfaEnrollment:start",se(c,u))).phoneSessionInfo.sessionInfo;$("signin"===d.type,t,"internal-error");var h=(null===(i=e.multiFactorHint)||void 0===i?void 0:i.uid)||e.multiFactorUid;return $(h,t,"missing-multi-factor-info"),(a={mfaPendingCredential:d.credential,mfaEnrollmentId:h,phoneSignInInfo:{recaptchaToken:l}},await oe(t,"POST","/v2/accounts/mfaSignIn:start",se(t,a))).phoneResponseInfo.sessionInfo}var p=(s=t,o={phoneNumber:e.phoneNumber,recaptchaToken:l},await oe(s,"POST","/v1/accounts:sendVerificationCode",se(s,o)))["sessionInfo"];return p}finally{r._reset()}}class Bn{constructor(e){this.providerId=Bn.PROVIDER_ID,this.auth=$e(e)}verifyPhoneNumber(e,t){return zn(this.auth,e,k(t))}static credential(e,t){return vt._fromVerification(e,t)}static credentialFromResult(e){return Bn.credentialFromTaggedObject(e)}static credentialFromError(e){return Bn.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{phoneNumber:t,temporaryProof:e}=e;return t&&e?vt._fromTokenResponse(t,e):null}}function Gn(e,t){return t?Pe(t):($(e._popupRedirectResolver,e,"argument-error"),e._popupRedirectResolver)}Bn.PROVIDER_ID="phone",Bn.PHONE_SIGN_IN_METHOD="phone";class Kn extends st{constructor(e){super("custom","custom"),this.params=e}_getIdTokenResponse(e){return pt(e,this._buildIdpRequest())}_linkToIdToken(e,t){return pt(e,this._buildIdpRequest(t))}_getReauthenticationResolver(e){return pt(e,this._buildIdpRequest())}_buildIdpRequest(e){const t={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return e&&(t.idToken=e),t}}function $n(e){return Vt(e.auth,new Kn(e),e.bypassAuthState)}function Jn(e){var{auth:t,user:n}=e;return $(n,t,"internal-error"),Ft(n,new Kn(e),e.bypassAuthState)}async function Yn(e){var{auth:t,user:n}=e;return $(n,t,"internal-error"),Ut(n,new Kn(e),e.bypassAuthState)}class Xn{constructor(e,t,n,r,i=!1){this.auth=e,this.resolver=n,this.user=r,this.bypassAuthState=i,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(t)?t:[t]}execute(){return new Promise(async(e,t)=>{this.pendingPromise={resolve:e,reject:t};try{this.eventManager=await this.resolver._initialize(this.auth),await this.onExecution(),this.eventManager.registerConsumer(this)}catch(e){this.reject(e)}})}async onAuthEvent(e){var{urlResponse:t,sessionId:n,postBody:r,tenantId:i,error:s,type:e}=e;if(s)this.reject(s);else{r={auth:this.auth,requestUri:t,sessionId:n,tenantId:i||void 0,postBody:r||void 0,user:this.user,bypassAuthState:this.bypassAuthState};try{this.resolve(await this.getIdpTask(e)(r))}catch(e){this.reject(e)}}}onError(e){this.reject(e)}getIdpTask(e){switch(e){case"signInViaPopup":case"signInViaRedirect":return $n;case"linkViaPopup":case"linkViaRedirect":return Yn;case"reauthViaPopup":case"reauthViaRedirect":return Jn;default:W(this.auth,"internal-error")}}resolve(e){Y(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(e),this.unregisterAndCleanUp()}reject(e){Y(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(e),this.unregisterAndCleanUp()}unregisterAndCleanUp(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()}}const Qn=new ee(2e3,1e4);class Zn extends Xn{constructor(e,t,n,r,i){super(e,t,r,i),this.provider=n,this.authWindow=null,this.pollId=null,Zn.currentPopupAction&&Zn.currentPopupAction.cancel(),Zn.currentPopupAction=this}async executeNotNull(){var e=await this.execute();return $(e,this.auth,"internal-error"),e}async onExecution(){Y(1===this.filter.length,"Popup operations only handle one event");var e=In();this.authWindow=await this.resolver._openPopup(this.auth,this.provider,this.filter[0],e),this.authWindow.associatedEvent=e,this.resolver._originValidation(this.auth).catch(e=>{this.reject(e)}),this.resolver._isIframeWebStorageSupported(this.auth,e=>{e||this.reject(q(this.auth,"web-storage-unsupported"))}),this.pollUserCancellation()}get eventId(){var e;return(null===(e=this.authWindow)||void 0===e?void 0:e.associatedEvent)||null}cancel(){this.reject(q(this.auth,"cancelled-popup-request"))}cleanUp(){this.authWindow&&this.authWindow.close(),this.pollId&&window.clearTimeout(this.pollId),this.authWindow=null,this.pollId=null,Zn.currentPopupAction=null}pollUserCancellation(){const t=()=>{var e;null!==(e=null===(e=this.authWindow)||void 0===e?void 0:e.window)&&void 0!==e&&e.closed?this.pollId=window.setTimeout(()=>{this.pollId=null,this.reject(q(this.auth,"popup-closed-by-user"))},8e3):this.pollId=window.setTimeout(t,Qn.get())};t()}}Zn.currentPopupAction=null;const er="pendingRedirect",tr=new Map;class nr extends Xn{constructor(e,t,n=!1){super(e,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],t,void 0,n),this.eventId=null}async execute(){let t=tr.get(this.auth._key());if(!t){try{const e=await async function(e,t){const n=or(t),r=sr(e);if(!await r._isAvailable())return!1;e="true"===await r._get(n);return await r._remove(n),e}(this.resolver,this.auth)?await super.execute():null;t=()=>Promise.resolve(e)}catch(e){t=()=>Promise.reject(e)}tr.set(this.auth._key(),t)}return this.bypassAuthState||tr.set(this.auth._key(),()=>Promise.resolve(null)),t()}async onAuthEvent(e){if("signInViaRedirect"===e.type)return super.onAuthEvent(e);if("unknown"!==e.type){if(e.eventId){var t=await this.auth._redirectUserForId(e.eventId);if(t)return this.user=t,super.onAuthEvent(e);this.resolve(null)}}else this.resolve(null)}async onExecution(){}cleanUp(){}}async function rr(e,t){return sr(e)._set(or(t),"true")}function ir(e,t){tr.set(e._key(),t)}function sr(e){return Pe(e._redirectPersistence)}function or(e){return Ne(er,e.config.apiKey,e.name)}function ar(e,t,n){return async function(e,t,n){if(ji._isFirebaseServerApp(e.app))return Promise.reject(B(e));var r=$e(e);G(e,t,_t),await r._initializationPromise;const i=Gn(r,n);return await rr(i,r),i._openRedirect(r,t,"signInViaRedirect")}(e,t,n)}function cr(e,t,n){return async function(e,t,n){e=k(e);if(G(e.auth,t,_t),ji._isFirebaseServerApp(e.auth.app))return Promise.reject(B(e.auth));await e.auth._initializationPromise;const r=Gn(e.auth,n);await rr(r,e.auth);n=await dr(e);return r._openRedirect(e.auth,t,"reauthViaRedirect",n)}(e,t,n)}function ur(e,t,n){return async function(e,t,n){e=k(e);G(e.auth,t,_t),await e.auth._initializationPromise;const r=Gn(e.auth,n);await Mt(!1,e,t.providerId),await rr(r,e.auth);n=await dr(e);return r._openRedirect(e.auth,t,"linkViaRedirect",n)}(e,t,n)}async function lr(e,t,n=!1){if(ji._isFirebaseServerApp(e.app))return Promise.reject(B(e));const r=$e(e);e=Gn(r,t);const i=new nr(r,e,n),s=await i.execute();return s&&!n&&(delete s.user._redirectEventId,await r._persistUserIfCurrent(s.user),await r._setRedirectUser(null,t)),s}async function dr(e){var t=In(`${e.uid}:::`);return e._redirectEventId=t,await e.auth._setRedirectUser(e),await e.auth._persistUserIfCurrent(e),t}class hr{constructor(e){this.auth=e,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}registerConsumer(e){this.consumers.add(e),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,e)&&(this.sendToConsumer(this.queuedRedirectEvent,e),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)}unregisterConsumer(e){this.consumers.delete(e)}onEvent(t){if(this.hasEventBeenHandled(t))return!1;let n=!1;return this.consumers.forEach(e=>{this.isEventForConsumer(t,e)&&(n=!0,this.sendToConsumer(t,e),this.saveEventToCache(t))}),this.hasHandledPotentialRedirect||!function(e){switch(e.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return!0;case"unknown":return fr(e);default:return!1}}(t)||(this.hasHandledPotentialRedirect=!0,n||(this.queuedRedirectEvent=t,n=!0)),n}sendToConsumer(e,t){var n;e.error&&!fr(e)?(n=(null===(n=e.error.code)||void 0===n?void 0:n.split("auth/")[1])||"internal-error",t.onError(q(this.auth,n))):t.onAuthEvent(e)}isEventForConsumer(e,t){var n=null===t.eventId||!!e.eventId&&e.eventId===t.eventId;return t.filter.includes(e.type)&&n}hasEventBeenHandled(e){return 6e5<=Date.now()-this.lastProcessedEventTime&&this.cachedEventUids.clear(),this.cachedEventUids.has(pr(e))}saveEventToCache(e){this.cachedEventUids.add(pr(e)),this.lastProcessedEventTime=Date.now()}}function pr(e){return[e.type,e.eventId,e.sessionId,e.tenantId].filter(e=>e).join("-")}function fr({type:e,error:t}){return"unknown"===e&&"auth/no-auth-event"===(null==t?void 0:t.code)}async function mr(e,t={}){return oe(e,"GET","/v1/projects",t)}const vr=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,gr=/^https?/;async function yr(e){if(!e.config.emulator){var t=(await mr(e))["authorizedDomains"];for(const n of t)try{if(function(e){const t=X(),{protocol:n,hostname:r}=new URL(t);if(e.startsWith("chrome-extension://")){var i=new URL(e);return""===i.hostname&&""===r?"chrome-extension:"===n&&e.replace("chrome-extension://","")===t.replace("chrome-extension://",""):"chrome-extension:"===n&&i.hostname===r}if(!gr.test(n))return!1;if(vr.test(e))return r===e;const s=e.replace(/\./g,"\\."),o=new RegExp("^(.+\\."+s+"|"+s+")$","i");return o.test(r)}(n))return}catch(e){}W(e,"unauthorized-domain")}}const _r=new ee(3e4,6e4);function Ir(){const t=bn().___jsl;if(null!==t&&void 0!==t&&t.H)for(const e of Object.keys(t.H))if(t.H[e].r=t.H[e].r||[],t.H[e].L=t.H[e].L||[],t.H[e].r=[...t.H[e].L],t.CP)for(let e=0;e<t.CP.length;e++)t.CP[e]=null}function wr(i){return new Promise((e,t)=>{function n(){Ir(),gapi.load("gapi.iframes",{callback:()=>{e(gapi.iframes.getContext())},ontimeout:()=>{Ir(),t(q(i,"network-request-failed"))},timeout:_r.get()})}if(null!==(r=null===(r=bn().gapi)||void 0===r?void 0:r.iframes)&&void 0!==r&&r.Iframe)e(gapi.iframes.getContext());else{if(null===(r=bn().gapi)||void 0===r||!r.load){var r=Qe("iframefcb");return bn()[r]=()=>{gapi.load?n():t(q(i,"network-request-failed"))},Xe(`${Ye.gapiScript}?onload=${r}`).catch(e=>t(e))}n()}}).catch(e=>{throw br=null,e})}let br=null;const Tr=new ee(5e3,15e3),Er="__/auth/iframe",kr="emulator/auth/iframe",Sr={style:{position:"absolute",top:"-100px",width:"1px",height:"1px"},"aria-hidden":"true",tabindex:"-1"},Ar=new Map([["identitytoolkit.googleapis.com","p"],["staging-identitytoolkit.sandbox.googleapis.com","s"],["test-identitytoolkit.sandbox.googleapis.com","t"]]);async function Rr(o){const e=(t=o,br=br||wr(t),await br);var t=bn().gapi;return $(t,o,"internal-error"),e.open({where:document.body,url:function(e){var t=e.config;$(t.authDomain,e,"auth-domain-config-required");var n=t.emulator?te(t,kr):`https://${e.config.authDomain}/${Er}`;const r={apiKey:t.apiKey,appName:e.name,v:ji.SDK_VERSION};(t=Ar.get(e.config.apiHost))&&(r.eid=t);const i=e._getFrameworks();return i.length&&(r.fw=i.join(",")),`${n}?${I(r).slice(1)}`}(o),messageHandlersFilter:t.iframes.CROSS_ORIGIN_IFRAMES_FILTER,attributes:Sr,dontclear:!0},s=>new Promise(async(e,t)=>{await s.restyle({setHideOnLeave:!1});const n=q(o,"network-request-failed"),r=bn().setTimeout(()=>{t(n)},Tr.get());function i(){bn().clearTimeout(r),e(s)}s.ping(i).then(i,()=>{t(n)})}))}const Pr={location:"yes",resizable:"yes",statusbar:"yes",toolbar:"no"};class Or{constructor(e){this.window=e,this.associatedEvent=null}close(){if(this.window)try{this.window.close()}catch(e){}}}function Cr(e,t,n,r=500,i=600){var s=Math.max((window.screen.availHeight-i)/2,0).toString(),o=Math.max((window.screen.availWidth-r)/2,0).toString();let a="";const c=Object.assign(Object.assign({},Pr),{width:r.toString(),height:i.toString(),top:s,left:o});o=d().toLowerCase();n&&(a=Fe(o)?"_blank":n),Ue(o)&&(t=t||"http://localhost",c.scrollbars="yes");var u,n=Object.entries(c).reduce((e,[t,n])=>`${e}${t}=${n},`,"");if([o=d()]=[o],We(o)&&null!==(u=window.navigator)&&void 0!==u&&u.standalone&&"_self"!==a)return function(e,t){const n=document.createElement("a");n.href=e,n.target=t;const r=document.createEvent("MouseEvent");r.initMouseEvent("click",!0,!0,window,1,0,0,0,0,!1,!1,!1,!1,1,null),n.dispatchEvent(r)}(t||"",a),new Or(null);const l=window.open(t||"",a,n);$(l,e,"popup-blocked");try{l.focus()}catch(e){}return new Or(l)}const Nr="__/auth/handler",Lr="emulator/auth/handler",Dr=encodeURIComponent("fac");async function Ur(e,t,n,r,i,s){$(e.config.authDomain,e,"auth-domain-config-required"),$(e.config.apiKey,e,"invalid-api-key");const o={apiKey:e.config.apiKey,appName:e.name,authType:n,redirectUrl:r,v:ji.SDK_VERSION,eventId:i};if(t instanceof _t){t.setDefaultLanguage(e.languageCode),o.providerId=t.providerId||"",function(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t))return;return 1}(t.getCustomParameters())||(o.customParameters=JSON.stringify(t.getCustomParameters()));for(var[a,c]of Object.entries(s||{}))o[a]=c}if(t instanceof It){const l=t.getScopes().filter(e=>""!==e);0<l.length&&(o.scopes=l.join(","))}e.tenantId&&(o.tid=e.tenantId);const u=o;for(const d of Object.keys(u))void 0===u[d]&&delete u[d];t=await e._getAppCheckToken(),t=t?`#${Dr}=${encodeURIComponent(t)}`:"";return`${e=[e["config"]][0],e.emulator?te(e,Lr):`https://${e.authDomain}/${Nr}`}?${I(u).slice(1)}${t}`}const Mr="webStorageSupport";const Fr=class{constructor(){this.eventManagers={},this.iframes={},this.originValidationPromises={},this._redirectPersistence=yn,this._completeRedirectFn=lr,this._overrideRedirectResult=ir}async _openPopup(e,t,n,r){var i;return Y(null===(i=this.eventManagers[e._key()])||void 0===i?void 0:i.manager,"_initialize() not called before _openPopup()"),Cr(e,await Ur(e,t,n,X(),r),In())}async _openRedirect(e,t,n,r){await this._originValidation(e);r=await Ur(e,t,n,X(),r);return bn().location.href=r,new Promise(()=>{})}_initialize(e){const t=e._key();if(this.eventManagers[t]){const{manager:r,promise:n}=this.eventManagers[t];return r?Promise.resolve(r):(Y(n,"If manager is not set, promise should be"),n)}const n=this.initAndGetManager(e);return this.eventManagers[t]={promise:n},n.catch(()=>{delete this.eventManagers[t]}),n}async initAndGetManager(t){const e=await Rr(t),n=new hr(t);return e.register("authEvent",e=>{return $(null==e?void 0:e.authEvent,t,"invalid-auth-event"),{status:n.onEvent(e.authEvent)?"ACK":"ERROR"}},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER),this.eventManagers[t._key()]={manager:n},this.iframes[t._key()]=e,n}_isIframeWebStorageSupported(t,n){const e=this.iframes[t._key()];e.send(Mr,{type:Mr},e=>{e=null===(e=null==e?void 0:e[0])||void 0===e?void 0:e[Mr];void 0!==e&&n(!!e),W(t,"internal-error")},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=yr(e)),this.originValidationPromises[t]}get _shouldInitProactively(){return qe()||Me()||We()}};class Vr extends class{constructor(e){this.factorId=e}_process(e,t,n){switch(t.type){case"enroll":return this._finalizeEnroll(e,t.credential,n);case"signin":return this._finalizeSignIn(e,t.credential);default:return J("unexpected MultiFactorSessionType")}}}{constructor(e){super("phone"),this.credential=e}static _fromCredential(e){return new Vr(e)}_finalizeEnroll(e,t,n){return e=e,n={idToken:t,displayName:n,phoneVerificationInfo:this.credential._makeVerificationRequest()},oe(e,"POST","/v2/accounts/mfaEnrollment:finalize",se(e,n))}_finalizeSignIn(e,t){return e=e,t={mfaPendingCredential:t,phoneVerificationInfo:this.credential._makeVerificationRequest()},oe(e,"POST","/v2/accounts/mfaSignIn:finalize",se(e,t))}}class jr{constructor(){}static assertion(e){return Vr._fromCredential(e)}}jr.FACTOR_ID="phone";var xr="@firebase/auth";class Hr{constructor(e){this.auth=e,this.internalListeners=new Map}getUid(){var e;return this.assertAuthConfigured(),(null===(e=this.auth.currentUser)||void 0===e?void 0:e.uid)||null}async getToken(e){return this.assertAuthConfigured(),await this.auth._initializationPromise,this.auth.currentUser?{accessToken:await this.auth.currentUser.getIdToken(e)}:null}addAuthTokenListener(t){var e;this.assertAuthConfigured(),this.internalListeners.has(t)||(e=this.auth.onIdTokenChanged(e=>{t((null==e?void 0:e.stsTokenManager.accessToken)||null)}),this.internalListeners.set(t,e),this.updateProactiveRefresh())}removeAuthTokenListener(e){this.assertAuthConfigured();const t=this.internalListeners.get(e);t&&(this.internalListeners.delete(e),t(),this.updateProactiveRefresh())}assertAuthConfigured(){$(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){0<this.internalListeners.size?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}}var Wr,qr,zr;function Br(){return window}Wr="authIdTokenMaxAge",null===(qr=a())||void 0===qr||qr[`_${Wr}`],Ye={loadJS(r){return new Promise((e,n)=>{const t=document.createElement("script");t.setAttribute("src",r),t.onload=e,t.onerror=e=>{const t=q("internal-error");t.customData=e,n(t)},t.type="text/javascript",t.charset="UTF-8",(null!==(e=null===(e=document.getElementsByTagName("head"))||void 0===e?void 0:e[0])&&void 0!==e?e:document).appendChild(t)})},gapiScript:"https://apis.google.com/js/api.js",recaptchaV2Script:"https://www.google.com/recaptcha/api.js",recaptchaEnterpriseScript:"https://www.google.com/recaptcha/enterprise.js?render="},zr="Browser",ji._registerComponent(new L("auth",(e,{options:t})=>{var n=e.getProvider("app").getImmediate(),r=e.getProvider("heartbeat"),i=e.getProvider("app-check-internal");const{apiKey:s,authDomain:o}=n.options;$(s&&!s.includes(":"),"invalid-api-key",{appName:n.name});e={apiKey:s,authDomain:o,clientPlatform:zr,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:ze(zr)},e=new Ke(n,r,i,e);return function(e,t){const n=(null==t?void 0:t.persistence)||[];var r=(Array.isArray(n)?n:[n]).map(Pe);null!=t&&t.errorMap&&e._updateErrorMap(t.errorMap),e._initializeWithPersistence(r,null==t?void 0:t.popupRedirectResolver)}(e,t),e},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((e,t,n)=>{const r=e.getProvider("auth-internal");r.initialize()})),ji._registerComponent(new L("auth-internal",e=>{e=$e(e.getProvider("auth").getImmediate());return e=e,new Hr(e)},"PRIVATE").setInstantiationMode("EXPLICIT")),ji.registerVersion(xr,"1.7.9",function(e){switch(e){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}}(zr)),ji.registerVersion(xr,"1.7.9","esm2017");async function Gr(e,t,n){var r=Br()["BuildInfo"];Y(t.sessionId,"AuthEvent did not contain a session ID");var i=await async function(e){const t=function(t){if(Y(/[0-9a-zA-Z]+/.test(t),"Can only convert alpha-numeric strings"),"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(t);const e=new ArrayBuffer(t.length),n=new Uint8Array(e);for(let e=0;e<t.length;e++)n[e]=t.charCodeAt(e);return n}(e),n=await crypto.subtle.digest("SHA-256",t),r=Array.from(new Uint8Array(n));return r.map(e=>e.toString(16).padStart(2,"0")).join("")}(t.sessionId);const s={};return We()?s.ibi=r.packageName:je()?s.apn=r.packageName:W(e,"operation-not-supported-in-this-environment"),r.displayName&&(s.appDisplayName=r.displayName),s.sessionId=i,Ur(e,n,t.type,void 0,null!==(t=t.eventId)&&void 0!==t?t:void 0,s)}function Kr(r){const i=Br()["cordova"];return new Promise(n=>{i.plugins.browsertab.isAvailable(e=>{let t=null;e?i.plugins.browsertab.openUrl(r):t=i.InAppBrowser.open(r,(e=d(),/(iPad|iPhone|iPod).*OS 7_\d/i.test(e)||/(iPad|iPhone|iPod).*OS 8_\d/i.test(e)?"_blank":"_system"),"location=yes"),n(t)})})}const $r=20;class Jr extends hr{constructor(){super(...arguments),this.passiveListeners=new Set,this.initPromise=new Promise(e=>{this.resolveInitialized=e})}addPassiveListener(e){this.passiveListeners.add(e)}removePassiveListener(e){this.passiveListeners.delete(e)}resetRedirect(){this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1}onEvent(t){return this.resolveInitialized(),this.passiveListeners.forEach(e=>e(t)),super.onEvent(t)}async initialized(){await this.initPromise}}function Yr(e,t,n=null){return{type:t,eventId:n,urlResponse:null,sessionId:function(){const t=[],n="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let e=0;e<$r;e++){var r=Math.floor(Math.random()*n.length);t.push(n.charAt(r))}return t.join("")}(),postBody:null,tenantId:e.tenantId,error:q(e,"no-auth-event")}}async function Xr(e){var t=await Zr()._get(ei(e));return t&&await Zr()._remove(ei(e)),t}function Qr(e,t){var n,r,i;const s=(n=ti(o=t),r=n.link?decodeURIComponent(n.link):void 0,i=ti(r).link,t=n.deep_link_id?decodeURIComponent(n.deep_link_id):void 0,(n=ti(t).link)||t||i||r||o);if(s.includes("/__/auth/callback")){var o=ti(s),o=o.firebaseError?function(e){try{return JSON.parse(e)}catch(e){return null}}(decodeURIComponent(o.firebaseError)):null,o=null===(o=null===(o=null==o?void 0:o.code)||void 0===o?void 0:o.split("auth/"))||void 0===o?void 0:o[1],o=o?q(o):null;return o?{type:e.type,eventId:e.eventId,tenantId:e.tenantId,error:o,urlResponse:null,sessionId:null,postBody:null}:{type:e.type,eventId:e.eventId,tenantId:e.tenantId,sessionId:e.sessionId,urlResponse:s,postBody:null}}return null}function Zr(){return Pe(vn)}function ei(e){return Ne("authEvent",e.config.apiKey,e.name)}function ti(e){if(null==e||!e.includes("?"))return{};const[,...t]=e.split("?");return w(t.join("?"))}const ni=class{constructor(){this._redirectPersistence=yn,this._shouldInitProactively=!0,this.eventManagers=new Map,this.originValidationPromises={},this._completeRedirectFn=lr,this._overrideRedirectResult=ir}async _initialize(e){var t=e._key();let n=this.eventManagers.get(t);return n||(n=new Jr(e),this.eventManagers.set(t,n),this.attachCallbackListeners(e,n)),n}_openPopup(e){W(e,"operation-not-supported-in-this-environment")}async _openRedirect(e,t,n,r){var i,s;a=e,s=Br(),$("function"==typeof(null===(i=null==s?void 0:s.universalLinks)||void 0===i?void 0:i.subscribe),a,"invalid-cordova-configuration",{missingPlugin:"cordova-universal-links-plugin-fix"}),$(void 0!==(null===(i=null==s?void 0:s.BuildInfo)||void 0===i?void 0:i.packageName),a,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-buildInfo"}),$("function"==typeof(null===(i=null===(i=null===(i=null==s?void 0:s.cordova)||void 0===i?void 0:i.plugins)||void 0===i?void 0:i.browsertab)||void 0===i?void 0:i.openUrl),a,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),$("function"==typeof(null===(i=null===(i=null===(i=null==s?void 0:s.cordova)||void 0===i?void 0:i.plugins)||void 0===i?void 0:i.browsertab)||void 0===i?void 0:i.isAvailable),a,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),$("function"==typeof(null===(s=null===(s=null==s?void 0:s.cordova)||void 0===s?void 0:s.InAppBrowser)||void 0===s?void 0:s.open),a,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-inappbrowser"});const o=await this._initialize(e);await o.initialized(),o.resetRedirect(),tr.clear(),await this._originValidation(e);var a=Yr(e,n,r);n=e,r=a,await Zr()._set(ei(n),r);t=await Kr(await Gr(e,a,t));return async function(o,a,c){const u=Br()["cordova"];let l=()=>{};try{await new Promise((n,e)=>{let t=null;function r(){var e;n();const t=null===(e=u.plugins.browsertab)||void 0===e?void 0:e.close;"function"==typeof t&&t(),"function"==typeof(null==c?void 0:c.close)&&c.close()}function i(){t=t||window.setTimeout(()=>{e(q(o,"redirect-cancelled-by-user"))},2e3)}function s(){"visible"===(null===document||void 0===document?void 0:document.visibilityState)&&i()}a.addPassiveListener(r),document.addEventListener("resume",i,!1),je()&&document.addEventListener("visibilitychange",s,!1),l=()=>{a.removePassiveListener(r),document.removeEventListener("resume",i,!1),document.removeEventListener("visibilitychange",s,!1),t&&window.clearTimeout(t)}})}finally{l()}}(e,o,t)}_isIframeWebStorageSupported(e,t){throw new Error("Method not implemented.")}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=async function(e){var t=Br()["BuildInfo"];const n={};We()?n.iosBundleId=t.packageName:je()?n.androidPackageName=t.packageName:W(e,"operation-not-supported-in-this-environment"),await mr(e,n)}(e)),this.originValidationPromises[t]}attachCallbackListeners(r,i){const{universalLinks:e,handleOpenURL:t,BuildInfo:n}=Br(),s=setTimeout(async()=>{await Xr(r),i.onEvent(ri())},500),o=async e=>{clearTimeout(s);var t=await Xr(r);let n=null;t&&null!=e&&e.url&&(n=Qr(t,e.url)),i.onEvent(n||ri())};void 0!==e&&"function"==typeof e.subscribe&&e.subscribe(null,o);const a=t,c=`${n.packageName.toLowerCase()}://`;Br().handleOpenURL=async e=>{if(e.toLowerCase().startsWith(c)&&o({url:e}),"function"==typeof a)try{a(e)}catch(e){console.error(e)}}}};function ri(){return{type:"unknown",eventId:null,sessionId:null,urlResponse:null,postBody:null,tenantId:null,error:q("no-auth-event")}}function ii(){var e;return(null===(e=null===self||void 0===self?void 0:self.location)||void 0===e?void 0:e.protocol)||null}function si(e){return void 0===e&&(e=d()),!("file:"!==ii()&&"ionic:"!==ii()&&"capacitor:"!==ii()||!e.toLowerCase().match(/iphone|ipad|ipod|android/))}function oi(e){return void 0===e&&(e=d()),m()&&11===(null===document||void 0===document?void 0:document.documentMode)||(void 0===(e=e)&&(e=d()),/Edge\/\d+/.test(e))}function ai(){try{var e=self.localStorage,t=In();if(e)return e.setItem(t,"1"),e.removeItem(t),!oi()||v()}catch(e){return ci()&&v()}return!1}function ci(){return"undefined"!=typeof global&&"WorkerGlobalScope"in global&&"importScripts"in global}function ui(){return("http:"===ii()||"https:"===ii()||p()||si())&&!(f()||h())&&ai()&&!ci()}function li(){return si()&&"undefined"!=typeof document}var di={LOCAL:"local",NONE:"none",SESSION:"session"},hi=$,pi="persistence";function fi(r){return C(this,void 0,void 0,function(){var t,n;return N(this,function(e){switch(e.label){case 0:return[4,r._initializationPromise];case 1:return e.sent(),t=mi(),n=Ne(pi,r.config.apiKey,r.name),t&&t.setItem(n,r._getPersistence()),[2]}})})}function mi(){var e;try{return(null===(e="undefined"!=typeof window?window:null)?void 0:e.sessionStorage)||null}catch(e){return null}}var vi=$,gi=(yi.prototype._initialize=function(t){return C(this,void 0,void 0,function(){return N(this,function(e){switch(e.label){case 0:return[4,this.selectUnderlyingResolver()];case 1:return e.sent(),[2,this.assertedUnderlyingResolver._initialize(t)]}})})},yi.prototype._openPopup=function(t,n,r,i){return C(this,void 0,void 0,function(){return N(this,function(e){switch(e.label){case 0:return[4,this.selectUnderlyingResolver()];case 1:return e.sent(),[2,this.assertedUnderlyingResolver._openPopup(t,n,r,i)]}})})},yi.prototype._openRedirect=function(t,n,r,i){return C(this,void 0,void 0,function(){return N(this,function(e){switch(e.label){case 0:return[4,this.selectUnderlyingResolver()];case 1:return e.sent(),[2,this.assertedUnderlyingResolver._openRedirect(t,n,r,i)]}})})},yi.prototype._isIframeWebStorageSupported=function(e,t){this.assertedUnderlyingResolver._isIframeWebStorageSupported(e,t)},yi.prototype._originValidation=function(e){return this.assertedUnderlyingResolver._originValidation(e)},Object.defineProperty(yi.prototype,"_shouldInitProactively",{get:function(){return li()||this.browserResolver._shouldInitProactively},enumerable:!1,configurable:!0}),Object.defineProperty(yi.prototype,"assertedUnderlyingResolver",{get:function(){return vi(this.underlyingResolver,"internal-error"),this.underlyingResolver},enumerable:!1,configurable:!0}),yi.prototype.selectUnderlyingResolver=function(){return C(this,void 0,void 0,function(){var t;return N(this,function(e){switch(e.label){case 0:return this.underlyingResolver?[2]:[4,function(){return C(this,void 0,void 0,function(){return N(this,function(e){return li()?[2,new Promise(function(e){var t=setTimeout(function(){e(!1)},1e3);document.addEventListener("deviceready",function(){clearTimeout(t),e(!0)})})]:[2,!1]})})}()];case 1:return t=e.sent(),this.underlyingResolver=t?this.cordovaResolver:this.browserResolver,[2]}})})},yi);function yi(){this.browserResolver=Pe(Fr),this.cordovaResolver=Pe(ni),this.underlyingResolver=null,this._redirectPersistence=yn,this._completeRedirectFn=lr,this._overrideRedirectResult=ir}function _i(e){return e.unwrap()}function Ii(e,t){var n,r,i=null===(r=t.customData)||void 0===r?void 0:r._tokenResponse;"auth/multi-factor-auth-required"===(null==t?void 0:t.code)?t.resolver=new Ei(e,(r=t,e=k(n=e),$((n=r).customData.operationType,e,"argument-error"),$(null===(r=n.customData._serverResponse)||void 0===r?void 0:r.mfaPendingCredential,e,"argument-error"),ln._fromError(e,n))):!i||(t=wi(n=t))&&(n.credential=t,n.tenantId=i.tenantId||void 0,n.email=i.email||void 0,n.phoneNumber=i.phoneNumber||void 0)}function wi(e){var t=(e instanceof g?e.customData:e)._tokenResponse;if(!t)return null;if(!(e instanceof g)&&"temporaryProof"in t&&"phoneNumber"in t)return Bn.credentialFromResult(e);var n,r=t.providerId;if(!r||r===D.PASSWORD)return null;switch(r){case D.GOOGLE:n=Tt;break;case D.FACEBOOK:n=bt;break;case D.GITHUB:n=Et;break;case D.TWITTER:n=At;break;default:var i=t.oauthIdToken,s=t.oauthAccessToken,o=t.oauthTokenSecret,a=t.pendingToken,c=t.nonce;return s||o||i||a?a?r.startsWith("saml.")?kt._create(r,a):ft._fromParams({providerId:r,signInMethod:r,pendingToken:a,idToken:i,accessToken:s}):new wt(r).credential({idToken:i,accessToken:s,rawNonce:c}):null}return e instanceof g?n.credentialFromError(e):n.credentialFromResult(e)}function bi(t,e){return e.catch(function(e){throw e instanceof g&&Ii(t,e),e}).then(function(e){var t=e.operationType,n=e.user;return{operationType:t,credential:wi(e),additionalUserInfo:cn(e),user:Si.getOrCreate(n)}})}function Ti(n,r){return C(this,void 0,void 0,function(){var t;return N(this,function(e){switch(e.label){case 0:return[4,r];case 1:return[2,{verificationId:(t=e.sent()).verificationId,confirm:function(e){return bi(n,t.confirm(e))}}]}})})}var Ei=(Object.defineProperty(ki.prototype,"session",{get:function(){return this.resolver.session},enumerable:!1,configurable:!0}),Object.defineProperty(ki.prototype,"hints",{get:function(){return this.resolver.hints},enumerable:!1,configurable:!0}),ki.prototype.resolveSignIn=function(e){return bi(_i(this.auth),this.resolver.resolveSignIn(e))},ki);function ki(e,t){this.resolver=t,this.auth=e.wrapped()}var Si=(Ai.getOrCreate=function(e){return Ai.USER_MAP.has(e)||Ai.USER_MAP.set(e,new Ai(e)),Ai.USER_MAP.get(e)},Ai.prototype.delete=function(){return this._delegate.delete()},Ai.prototype.reload=function(){return this._delegate.reload()},Ai.prototype.toJSON=function(){return this._delegate.toJSON()},Ai.prototype.getIdTokenResult=function(e){return this._delegate.getIdTokenResult(e)},Ai.prototype.getIdToken=function(e){return this._delegate.getIdToken(e)},Ai.prototype.linkAndRetrieveDataWithCredential=function(e){return this.linkWithCredential(e)},Ai.prototype.linkWithCredential=function(t){return C(this,void 0,void 0,function(){return N(this,function(e){return[2,bi(this.auth,xt(this._delegate,t))]})})},Ai.prototype.linkWithPhoneNumber=function(t,n){return C(this,void 0,void 0,function(){return N(this,function(e){return[2,Ti(this.auth,async function(e,t,n){const r=k(e);return await Mt(!1,r,"phone"),n=await zn(r.auth,t,k(n)),new qn(n,e=>xt(r,e))}(this._delegate,t,n))]})})},Ai.prototype.linkWithPopup=function(t){return C(this,void 0,void 0,function(){return N(this,function(e){return[2,bi(this.auth,async function(e,t,n){G((e=k(e)).auth,t,_t),n=Gn(e.auth,n);const r=new Zn(e.auth,"linkViaPopup",t,n,e);return r.executeNotNull()}(this._delegate,t,gi))]})})},Ai.prototype.linkWithRedirect=function(t){return C(this,void 0,void 0,function(){return N(this,function(e){switch(e.label){case 0:return[4,fi($e(this.auth))];case 1:return e.sent(),[2,ur(this._delegate,t,gi)]}})})},Ai.prototype.reauthenticateAndRetrieveDataWithCredential=function(e){return this.reauthenticateWithCredential(e)},Ai.prototype.reauthenticateWithCredential=function(t){return C(this,void 0,void 0,function(){return N(this,function(e){return[2,bi(this.auth,Ht(this._delegate,t))]})})},Ai.prototype.reauthenticateWithPhoneNumber=function(e,t){return Ti(this.auth,async function(e,t,n){const r=k(e);return ji._isFirebaseServerApp(r.auth.app)?Promise.reject(B(r.auth)):(n=await zn(r.auth,t,k(n)),new qn(n,e=>Ht(r,e)))}(this._delegate,e,t))},Ai.prototype.reauthenticateWithPopup=function(e){return bi(this.auth,async function(e,t,n){if(e=k(e),ji._isFirebaseServerApp(e.auth.app))return Promise.reject(q(e.auth,"operation-not-supported-in-this-environment"));G(e.auth,t,_t),n=Gn(e.auth,n);const r=new Zn(e.auth,"reauthViaPopup",t,n,e);return r.executeNotNull()}(this._delegate,e,gi))},Ai.prototype.reauthenticateWithRedirect=function(t){return C(this,void 0,void 0,function(){return N(this,function(e){switch(e.label){case 0:return[4,fi($e(this.auth))];case 1:return e.sent(),[2,cr(this._delegate,t,gi)]}})})},Ai.prototype.sendEmailVerification=function(e){return Xt(this._delegate,e)},Ai.prototype.unlink=function(t){return C(this,void 0,void 0,function(){return N(this,function(e){switch(e.label){case 0:return[4,Dt(this._delegate,t)];case 1:return e.sent(),[2,this]}})})},Ai.prototype.updateEmail=function(e){return t=this._delegate,e=e,t=k(t),ji._isFirebaseServerApp(t.auth.app)?Promise.reject(B(t.auth)):en(t,e,null);var t},Ai.prototype.updatePassword=function(e){return en(k(this._delegate),null,e)},Ai.prototype.updatePhoneNumber=function(e){return async function(e,t){if(e=k(e),ji._isFirebaseServerApp(e.auth.app))return Promise.reject(B(e.auth));await Ut(e,t)}(this._delegate,e)},Ai.prototype.updateProfile=function(e){return Zt(this._delegate,e)},Ai.prototype.verifyBeforeUpdateEmail=function(e,t){return Qt(this._delegate,e,t)},Object.defineProperty(Ai.prototype,"emailVerified",{get:function(){return this._delegate.emailVerified},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"isAnonymous",{get:function(){return this._delegate.isAnonymous},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"metadata",{get:function(){return this._delegate.metadata},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"phoneNumber",{get:function(){return this._delegate.phoneNumber},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"providerData",{get:function(){return this._delegate.providerData},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"refreshToken",{get:function(){return this._delegate.refreshToken},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"tenantId",{get:function(){return this._delegate.tenantId},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"displayName",{get:function(){return this._delegate.displayName},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"email",{get:function(){return this._delegate.email},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"photoURL",{get:function(){return this._delegate.photoURL},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"providerId",{get:function(){return this._delegate.providerId},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"uid",{get:function(){return this._delegate.uid},enumerable:!1,configurable:!0}),Object.defineProperty(Ai.prototype,"auth",{get:function(){return this._delegate.auth},enumerable:!1,configurable:!0}),Ai.USER_MAP=new WeakMap,Ai);function Ai(e){this._delegate=e,this.multiFactor=(e=k(e=e),hn.has(e)||hn.set(e,dn._fromUser(e)),hn.get(e))}var Ri=$,Pi=(Object.defineProperty(Oi.prototype,"emulatorConfig",{get:function(){return this._delegate.emulatorConfig},enumerable:!1,configurable:!0}),Object.defineProperty(Oi.prototype,"currentUser",{get:function(){return this._delegate.currentUser?Si.getOrCreate(this._delegate.currentUser):null},enumerable:!1,configurable:!0}),Object.defineProperty(Oi.prototype,"languageCode",{get:function(){return this._delegate.languageCode},set:function(e){this._delegate.languageCode=e},enumerable:!1,configurable:!0}),Object.defineProperty(Oi.prototype,"settings",{get:function(){return this._delegate.settings},enumerable:!1,configurable:!0}),Object.defineProperty(Oi.prototype,"tenantId",{get:function(){return this._delegate.tenantId},set:function(e){this._delegate.tenantId=e},enumerable:!1,configurable:!0}),Oi.prototype.useDeviceLanguage=function(){this._delegate.useDeviceLanguage()},Oi.prototype.signOut=function(){return this._delegate.signOut()},Oi.prototype.useEmulator=function(e,t){nt(this._delegate,e,t)},Oi.prototype.applyActionCode=function(e){return $t(this._delegate,e)},Oi.prototype.checkActionCode=function(e){return Jt(this._delegate,e)},Oi.prototype.confirmPasswordReset=function(e,t){return async function(t,e,n){await ot(k(t),{oobCode:e,newPassword:n}).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&Kt(t),e})}(this._delegate,e,t)},Oi.prototype.createUserWithEmailAndPassword=function(t,n){return C(this,void 0,void 0,function(){return N(this,function(e){return[2,bi(this._delegate,async function(t,e,n){if(ji._isFirebaseServerApp(t.app))return Promise.reject(B(t));const r=$e(t),i=tt(r,{returnSecureToken:!0,email:e,password:n,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",Rt);return n=await i.catch(e=>{throw"auth/password-does-not-meet-requirements"===e.code&&Kt(t),e}),n=await Pt._fromIdTokenResponse(r,"signIn",n),await r._updateCurrentUser(n.user),n}(this._delegate,t,n))]})})},Oi.prototype.fetchProvidersForEmail=function(e){return this.fetchSignInMethodsForEmail(e)},Oi.prototype.fetchSignInMethodsForEmail=function(e){return Yt(this._delegate,e)},Oi.prototype.isSignInWithEmailLink=function(e){return this._delegate,e=e,"EMAIL_SIGNIN"===(null==(e=gt.parseLink(e))?void 0:e.operation)},Oi.prototype.getRedirectResult=function(){return C(this,void 0,void 0,function(){var t;return N(this,function(e){switch(e.label){case 0:return Ri(ui(),this._delegate,"operation-not-supported-in-this-environment"),[4,async function(e,t){return await $e(e)._initializationPromise,lr(e,t,!1)}(this._delegate,gi)];case 1:return(t=e.sent())?[2,bi(this._delegate,Promise.resolve(t))]:[2,{credential:null,user:null}]}})})},Oi.prototype.addFrameworkForLogging=function(e){$e(this._delegate)._logFramework(e)},Oi.prototype.onAuthStateChanged=function(e,t,n){e=Ci(e,t,n),t=e.next,n=e.error,e=e.complete;return this._delegate.onAuthStateChanged(t,n,e)},Oi.prototype.onIdTokenChanged=function(e,t,n){e=Ci(e,t,n),t=e.next,n=e.error,e=e.complete;return this._delegate.onIdTokenChanged(t,n,e)},Oi.prototype.sendSignInLinkToEmail=function(e,t){return async function(e,t,n){const r=$e(e);e=t={requestType:"EMAIL_SIGNIN",email:t,clientType:"CLIENT_TYPE_WEB"},$((n=n).handleCodeInApp,r,"argument-error"),n&&Gt(r,e,n),await tt(r,t,"getOobCode",dt)}(this._delegate,e,t)},Oi.prototype.sendPasswordResetEmail=function(e,t){return async function(e,t,n){e=$e(e),t={requestType:"PASSWORD_RESET",email:t,clientType:"CLIENT_TYPE_WEB"},n&&Gt(e,t,n),await tt(e,t,"getOobCode",lt)}(this._delegate,e,t||void 0)},Oi.prototype.setPersistence=function(s){return C(this,void 0,void 0,function(){var r,i;return N(this,function(e){switch(e.label){case 0:switch(t=this._delegate,n=s,hi(Object.values(di).includes(n),t,"invalid-persistence-type"),f()?hi(n!==di.SESSION,t,"unsupported-persistence-type"):h()?hi(n===di.NONE,t,"unsupported-persistence-type"):ci()?hi(n===di.NONE||n===di.LOCAL&&v(),t,"unsupported-persistence-type"):hi(n===di.NONE||ai(),t,"unsupported-persistence-type"),s){case di.SESSION:return[3,1];case di.LOCAL:return[3,2];case di.NONE:return[3,4]}return[3,5];case 1:return r=yn,[3,6];case 2:return[4,Pe(Ln)._isAvailable()];case 3:return i=e.sent(),r=i?Ln:vn,[3,6];case 4:return r=Ce,[3,6];case 5:return[2,W("argument-error",{appName:this._delegate.name})];case 6:return[2,this._delegate.setPersistence(r)]}var t,n})})},Oi.prototype.signInAndRetrieveDataWithCredential=function(e){return this.signInWithCredential(e)},Oi.prototype.signInAnonymously=function(){return bi(this._delegate,async function(e){if(ji._isFirebaseServerApp(e.app))return Promise.reject(B(e));const t=$e(e);return await t._initializationPromise,null!==(e=t.currentUser)&&void 0!==e&&e.isAnonymous?new Pt({user:t.currentUser,providerId:null,operationType:"signIn"}):(e=await Rt(t,{returnSecureToken:!0}),e=await Pt._fromIdTokenResponse(t,"signIn",e,!0),await t._updateCurrentUser(e.user),e)}(this._delegate))},Oi.prototype.signInWithCredential=function(e){return bi(this._delegate,jt(this._delegate,e))},Oi.prototype.signInWithCustomToken=function(e){return bi(this._delegate,Wt(this._delegate,e))},Oi.prototype.signInWithEmailAndPassword=function(e,t){return bi(this._delegate,(n=this._delegate,e=e,t=t,ji._isFirebaseServerApp(n.app)?Promise.reject(B(n)):jt(k(n),yt.credential(e,t)).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&Kt(n),e})));var n},Oi.prototype.signInWithEmailLink=function(e,t){return bi(this._delegate,async function(e,t,n){return ji._isFirebaseServerApp(e.app)?Promise.reject(B(e)):(e=k(e),$((n=yt.credentialWithLink(t,n||X()))._tenantId===(e.tenantId||null),e,"tenant-id-mismatch"),jt(e,n))}(this._delegate,e,t))},Oi.prototype.signInWithPhoneNumber=function(e,t){return Ti(this._delegate,async function(e,t,n){if(ji._isFirebaseServerApp(e.app))return Promise.reject(B(e));const r=$e(e);return n=await zn(r,t,k(n)),new qn(n,e=>jt(r,e))}(this._delegate,e,t))},Oi.prototype.signInWithPopup=function(t){return C(this,void 0,void 0,function(){return N(this,function(e){return Ri(ui(),this._delegate,"operation-not-supported-in-this-environment"),[2,bi(this._delegate,async function(e,t,n){if(ji._isFirebaseServerApp(e.app))return Promise.reject(q(e,"operation-not-supported-in-this-environment"));var r=$e(e);G(e,t,_t),n=Gn(r,n);const i=new Zn(r,"signInViaPopup",t,n);return i.executeNotNull()}(this._delegate,t,gi))]})})},Oi.prototype.signInWithRedirect=function(t){return C(this,void 0,void 0,function(){return N(this,function(e){switch(e.label){case 0:return Ri(ui(),this._delegate,"operation-not-supported-in-this-environment"),[4,fi(this._delegate)];case 1:return e.sent(),[2,ar(this._delegate,t,gi)]}})})},Oi.prototype.updateCurrentUser=function(e){return this._delegate.updateCurrentUser(e)},Oi.prototype.verifyPasswordResetCode=function(e){return async function(e,t){return(t=(await Jt(k(e),t))["data"]).email}(this._delegate,e)},Oi.prototype.unwrap=function(){return this._delegate},Oi.prototype._delete=function(){return this._delegate._delete()},Oi.prototype.linkUnderlyingAuth=function(){var e=this;this._delegate.wrapped=function(){return e}},Oi.Persistence=di,Oi);function Oi(e,t){if(this.app=e,t.isInitialized())return this._delegate=t.getImmediate(),void this.linkUnderlyingAuth();var n=e.options.apiKey;Ri(n,"invalid-api-key",{appName:e.name}),Ri(n,"invalid-api-key",{appName:e.name});var r="undefined"!=typeof window?gi:void 0;this._delegate=t.initialize({options:{persistence:function(e,t){var n=function(e,t){var n=mi();if(!n)return[];switch(t=Ne(pi,e,t),n.getItem(t)){case di.NONE:return[Ce];case di.LOCAL:return[Ln,yn];case di.SESSION:return[yn];default:return[]}}(e,t);"undefined"==typeof self||n.includes(Ln)||n.push(Ln);if("undefined"!=typeof window)for(var r=0,i=[vn,yn];r<i.length;r++){var s=i[r];n.includes(s)||n.push(s)}n.includes(Ce)||n.push(Ce);return n}(n,e.name),popupRedirectResolver:r}}),this._delegate._updateErrorMap(F),this.linkUnderlyingAuth()}function Ci(e,t,n){var r=e;"function"!=typeof e&&(r=e.next,t=e.error,n=e.complete);var i=r;return{next:function(e){return i(e&&Si.getOrCreate(e))},error:t,complete:n}}var Ni=(Li.credential=function(e,t){return Bn.credential(e,t)},Li.prototype.verifyPhoneNumber=function(e,t){return this._delegate.verifyPhoneNumber(e,t)},Li.prototype.unwrap=function(){return this._delegate},Li.PHONE_SIGN_IN_METHOD=Bn.PHONE_SIGN_IN_METHOD,Li.PROVIDER_ID=Bn.PROVIDER_ID,Li);function Li(){this.providerId="phone",this._delegate=new Bn(_i(i.default.auth()))}var Di=$,Ui=(Mi.prototype.clear=function(){this._delegate.clear()},Mi.prototype.render=function(){return this._delegate.render()},Mi.prototype.verify=function(){return this._delegate.verify()},Mi);function Mi(e,t,n){var r;void 0===n&&(n=i.default.app()),Di(null===(r=n.options)||void 0===r?void 0:r.apiKey,"invalid-api-key",{appName:n.name}),this._delegate=new Wn(n.auth(),e,t),this.type=this._delegate.type}var Fi;(Fi=i.default).INTERNAL.registerComponent(new L("auth-compat",function(e){var t=e.getProvider("app-compat").getImmediate(),e=e.getProvider("auth");return new Pi(t,e)},"PUBLIC").setServiceProps({ActionCodeInfo:{Operation:{EMAIL_SIGNIN:U.EMAIL_SIGNIN,PASSWORD_RESET:U.PASSWORD_RESET,RECOVER_EMAIL:U.RECOVER_EMAIL,REVERT_SECOND_FACTOR_ADDITION:U.REVERT_SECOND_FACTOR_ADDITION,VERIFY_AND_CHANGE_EMAIL:U.VERIFY_AND_CHANGE_EMAIL,VERIFY_EMAIL:U.VERIFY_EMAIL}},EmailAuthProvider:yt,FacebookAuthProvider:bt,GithubAuthProvider:Et,GoogleAuthProvider:Tt,OAuthProvider:wt,SAMLAuthProvider:St,PhoneAuthProvider:Ni,PhoneMultiFactorGenerator:jr,RecaptchaVerifier:Ui,TwitterAuthProvider:At,Auth:Pi,AuthCredential:st,Error:g}).setInstantiationMode("LAZY").setMultipleInstances(!1)),Fi.registerVersion("@firebase/auth-compat","0.5.14")}.apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-auth.js - be sure to load firebase-app.js first.")}});//# sourceMappingURL=firebase-auth.js.map
