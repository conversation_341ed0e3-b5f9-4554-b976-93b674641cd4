(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{1469:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return o},getImageProps:function(){return l}});let r=t(8229),a=t(8883),i=t(3063),n=r._(t(1193));function l(e){let{props:s}=(0,a.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(s))void 0===t&&delete s[e];return{props:s}}let o=i.Image},2590:(e,s,t)=>{Promise.resolve().then(t.bind(t,9690))},6766:(e,s,t)=>{"use strict";t.d(s,{default:()=>a.a});var r=t(1469),a=t.n(r)},9690:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(5155),a=t(2115),i=t(6874),n=t.n(i),l=t(6766),o=t(3004),c=t(6104),d=t(6681),m=t(12),u=t(4752),h=t.n(u);function f(){let{user:e,loading:s}=(0,d.hD)(),[t,i]=(0,a.useState)(""),[u,f]=(0,a.useState)(""),[x,g]=(0,a.useState)(!1),[b,p]=(0,a.useState)(!1),[w,j]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if("true"===new URLSearchParams(window.location.search).get("restore")&&(j(!0),setTimeout(()=>{h().fire({icon:"info",title:"Session Expired",text:"Your session expired. Please log in again to restore your progress.",background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)",timer:5e3,showConfirmButton:!0})},500)),e&&!s){let e=w?"/work":"/dashboard";window.location.href=e}},[e,s,w]);let v=async e=>{if(e.preventDefault(),!t||!u)return void h().fire({icon:"error",title:"Error",text:"Please fill in all fields",background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"});g(!0);try{let e=await (0,o.x9)(c.j2,t,u);w||(0,m.Dl)(!0),(0,m.nS)(e.user.uid),console.log("\uD83D\uDD12 User session isolated for:",e.user.uid)}catch(s){console.error("Login error:",s);let e="An error occurred during login";switch(s.code){case"auth/user-not-found":e="No account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=s.message||"Login failed"}h().fire({icon:"error",title:"Login Failed",text:e,background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"}),f("")}finally{g(!1)}};return s?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(l.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,r.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:w?"Session Expired":"Welcome Back"}),(0,r.jsx)("p",{className:"text-white/80",children:w?"Log in to restore your progress":"Sign in to continue earning"}),w&&(0,r.jsx)("div",{className:"mt-4 bg-orange-500/20 border border-orange-400/30 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle text-orange-400 mr-2"}),(0,r.jsx)("span",{className:"text-orange-300 text-sm",children:"Your work progress will be restored after login"})]})})]}),(0,r.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address"}),(0,r.jsx)("input",{type:"email",id:"email",value:t,onChange:e=>i(e.target.value),className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:b?"text":"password",id:"password",value:u,onChange:e=>f(e.target.value),className:"form-input pr-12",placeholder:"Enter your password",required:!0}),(0,r.jsx)("button",{type:"button",onClick:()=>p(!b),className:"password-toggle-btn","aria-label":b?"Hide password":"Show password",children:(0,r.jsx)("i",{className:"fas ".concat(b?"fa-eye-slash":"fa-eye")})})]})]}),(0,r.jsx)("button",{type:"submit",disabled:x,className:"w-full btn-primary flex items-center justify-center",children:x?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Logging in..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Login"]})})]}),(0,r.jsxs)("div",{className:"mt-6 text-center space-y-3",children:[(0,r.jsx)(n(),{href:"/forgot-password",className:"text-white/80 hover:text-white transition-colors",children:"Forgot your password?"}),(0,r.jsxs)("div",{className:"text-white/60",children:["Don't have an account?"," ",(0,r.jsx)(n(),{href:"/register",className:"text-white font-semibold hover:underline",children:"Sign up here"})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsxs)(n(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3063,6681,8441,1684,7358],()=>s(2590)),_N_E=e.O()}]);