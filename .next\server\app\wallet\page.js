(()=>{var e={};e.id=1730,e.ids=[1730],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30614:(e,t,s)=>{Promise.resolve().then(s.bind(s,70280))},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34025:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["wallet",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,49386)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\wallet\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\wallet\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/wallet/page",pathname:"/wallet",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},49386:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\wallet\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\wallet\\page.tsx","default")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67566:(e,t,s)=>{Promise.resolve().then(s.bind(s,49386))},70280:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(60687),r=s(43210),i=s(85814),l=s.n(i),n=s(87979),o=s(744),d=s(55986),c=s(3582),u=s(92617),m=s(98873),x=s(77567);function p(){let{user:e,loading:t}=(0,n.Nu)(),{hasBlockingNotifications:i,isChecking:p,markAllAsRead:h}=(0,o.J)(e?.uid||null),{isBlocked:b,leaveStatus:f}=(0,d.l)({userId:e?.uid||null,checkInterval:3e4,enabled:!!e}),[w,j]=(0,r.useState)(null),[N,g]=(0,r.useState)([]),[v,y]=(0,r.useState)(!0),[k,q]=(0,r.useState)(""),[C,S]=(0,r.useState)(!1),[P,A]=(0,r.useState)(null),[D,M]=(0,r.useState)(!1),[E,O]=(0,r.useState)({accountHolderName:"",accountNumber:"",ifscCode:"",bankName:""}),[T,_]=(0,r.useState)(!1),[U,W]=(0,r.useState)({allowed:!0}),[B,R]=(0,r.useState)(!1),[F,Y]=(0,r.useState)(null),I=async()=>{if(e)try{R(!0);let t=await (0,c.QD)(e.uid);W(t)}catch(e){console.error("Error checking withdrawal eligibility:",e),W({allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."})}finally{R(!1)}},J=async()=>{try{y(!0);try{console.log("\uD83D\uDE80 Loading wallet data with optimized functions...");let[t,s]=await Promise.all([(0,c.getWalletData)(e.uid),u.x8.getUserTransactions(e.uid,20,"withdrawal")]);j(t);let a=s.map(e=>({id:e.id,type:e.type,amount:"withdrawal"===e.type?-Math.abs(e.amount):e.amount,description:e.description,date:e.date,status:e.status||"completed"}));g(a),console.log("✅ Wallet data loaded via optimized functions")}catch(r){console.warn("⚠️ Optimized functions failed, using fallback:",r);let[t,s]=await Promise.all([(0,c.getWalletData)(e.uid),(0,c.i8)(e.uid,20)]);j(t);let a=s.map(e=>({id:e.id,type:"withdrawal",amount:-e.amount,description:`Withdrawal request - ₹${e.amount}`,date:e.date,status:e.status}));g(a)}}catch(e){console.error("Error loading wallet data:",e),x.A.fire({icon:"error",title:"Error",text:"Failed to load wallet data. Please try again."})}finally{y(!1)}},$=async t=>{if(t.preventDefault(),!T)try{_(!0),await (0,c.mm)(e.uid,E),A(E),M(!1),x.A.fire({icon:"success",title:"Bank Details Saved",text:"Your bank details have been saved successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error saving bank details:",e),x.A.fire({icon:"error",title:"Error",text:e.message||"Failed to save bank details. Please try again."})}finally{_(!1)}},z=e=>{let{name:t,value:s}=e.target;O(e=>({...e,[t]:s}))},H=async()=>{if(C)return void console.log("⚠️ Withdrawal already in progress, ignoring duplicate request");try{let{isUserPlanExpired:t}=await Promise.resolve().then(s.bind(s,3582)),a=await t(e.uid);if(a.expired)return void x.A.fire({icon:"error",title:"Plan Expired",html:`
            <div class="text-center">
              <p class="mb-2">Your plan has expired and you cannot make withdrawals.</p>
              <p class="text-sm text-gray-600 mb-2">${a.reason}</p>
              <p class="text-sm text-blue-600">Please upgrade your plan to continue using withdrawal services.</p>
            </div>
          `,confirmButtonText:"Go to Plans",showCancelButton:!0,cancelButtonText:"OK"}).then(e=>{e.isConfirmed&&(window.location.href="/plans")})}catch(e){console.error("Error checking plan expiry:",e)}if(b)return void x.A.fire({icon:"warning",title:"Withdrawal Not Available",text:f.reason||"Withdrawals are not available due to leave.",confirmButtonText:"OK"});let t=parseFloat(k);if(!t||t<=0)return void x.A.fire({icon:"error",title:"Invalid Amount",text:"Please enter a valid amount to withdraw"});if(t<50)return void x.A.fire({icon:"error",title:"Minimum Withdrawal",text:"Minimum withdrawal amount is ₹50"});if(t>(w?.wallet||0))return void x.A.fire({icon:"error",title:"Insufficient Balance",text:"You do not have enough balance in your wallet"});if(!P)return void x.A.fire({icon:"warning",title:"Bank Details Required",text:"Please add your bank details before making a withdrawal"});try{S(!0),console.log(`🔄 Processing withdrawal request for ₹${t}`),await (0,c.xj)(e.uid,t,P),await J(),x.A.fire({icon:"success",title:"Withdrawal Request Submitted",text:`Your withdrawal request for ₹${t} has been submitted and will be processed within 24-48 hours.`}),q(""),console.log(`✅ Withdrawal request completed successfully`)}catch(e){console.error("❌ Error processing withdrawal:",e),x.A.fire({icon:"error",title:"Withdrawal Failed",text:e.message||"Failed to process withdrawal request. Please try again."})}finally{S(!1)}},L=e=>null==e||isNaN(e)?"₹0.00":`₹${e.toFixed(2)}`;return t||v||p?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:t?"Loading...":p?"Checking notifications...":"Loading wallet..."})]})}):i&&e?(0,a.jsx)(m.A,{userId:e.uid,onAllRead:h}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(l(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"My Wallet"}),(0,a.jsxs)("button",{onClick:J,className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold text-white",children:"My Wallet"}),(0,a.jsx)("i",{className:"fas fa-wallet text-green-400 text-3xl"})]}),(0,a.jsx)("p",{className:"text-4xl font-bold text-green-400 mb-2",children:L(w?.wallet||0)}),(0,a.jsx)("p",{className:"text-white/60",children:"Total available balance"})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-university mr-2"}),"Bank Details"]}),P&&!D&&(0,a.jsxs)("button",{onClick:()=>M(!0),className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-edit mr-2"}),"Edit"]})]}),P||D?D?(0,a.jsxs)("form",{onSubmit:$,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Account Holder Name *"}),(0,a.jsx)("input",{type:"text",name:"accountHolderName",value:E.accountHolderName,onChange:z,className:"form-input",placeholder:"Enter account holder name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Bank Name *"}),(0,a.jsx)("input",{type:"text",name:"bankName",value:E.bankName,onChange:z,className:"form-input",placeholder:"Enter bank name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Account Number *"}),(0,a.jsx)("input",{type:"text",name:"accountNumber",value:E.accountNumber,onChange:z,className:"form-input",placeholder:"Enter account number",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"IFSC Code *"}),(0,a.jsx)("input",{type:"text",name:"ifscCode",value:E.ifscCode,onChange:z,className:"form-input",placeholder:"Enter IFSC code (e.g., SBIN0001234)",required:!0})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("button",{type:"submit",disabled:T,className:`${T?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:bg-blue-600"}`,children:T?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Saving Bank Details..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-save mr-2"}),"Save Bank Details"]})}),(0,a.jsx)("button",{type:"button",onClick:()=>M(!1),className:"btn-secondary",children:"Cancel"})]})]}):(0,a.jsx)("div",{className:"bg-white/10 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Account Holder"}),(0,a.jsx)("p",{className:"text-white font-medium",children:P?.accountHolderName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Bank Name"}),(0,a.jsx)("p",{className:"text-white font-medium",children:P?.bankName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Account Number"}),(0,a.jsxs)("p",{className:"text-white font-medium",children:["****",P?.accountNumber.slice(-4)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"IFSC Code"}),(0,a.jsx)("p",{className:"text-white font-medium",children:P?.ifscCode})]})]})}):(0,a.jsxs)("div",{className:"text-center py-6",children:[(0,a.jsx)("i",{className:"fas fa-university text-white/30 text-4xl mb-4"}),(0,a.jsx)("p",{className:"text-white/60 mb-4",children:"No bank details added yet"}),(0,a.jsxs)("button",{onClick:()=>M(!0),className:"btn-primary",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),"Add Bank Details"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Withdraw Funds"]}),F?.plan==="Trial"&&(0,a.jsxs)("div",{className:"mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,a.jsx)("span",{className:"text-red-400 font-medium",children:"Trial Plan Restriction"})]}),(0,a.jsx)("p",{className:"text-white/80 text-sm mb-3",children:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawal functionality."}),(0,a.jsx)("div",{className:"flex gap-3",children:(0,a.jsxs)(l(),{href:"/plans",className:"btn-primary inline-block",children:[(0,a.jsx)("i",{className:"fas fa-arrow-up mr-2"}),"Upgrade Plan"]})})]}),(0,a.jsxs)("div",{className:"mb-4 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("i",{className:"fas fa-clock text-blue-400 mr-2"}),(0,a.jsx)("span",{className:"text-blue-400 font-medium",children:"Withdrawal Timings"})]}),(0,a.jsxs)("p",{className:"text-white/80 text-sm mb-2",children:["Withdrawals are only allowed between ",(0,a.jsx)("strong",{children:"10:00 AM to 6:00 PM"})," on non-leave days."]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-white/60 text-xs",children:["Current time: ",new Date().toLocaleTimeString()," | Status: ",U.allowed?(0,a.jsx)("span",{className:"text-green-400 font-medium",children:"✓ Available"}):(0,a.jsx)("span",{className:"text-red-400 font-medium",children:"✗ Not Available"})]}),(0,a.jsx)("button",{onClick:I,disabled:B,className:"text-blue-400 hover:text-blue-300 text-xs",children:B?(0,a.jsx)("div",{className:"spinner w-3 h-3"}):(0,a.jsx)("i",{className:"fas fa-sync-alt"})})]}),!U.allowed&&U.reason&&(0,a.jsxs)("p",{className:"text-red-400 text-sm mt-2",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),U.reason]})]}),P?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("input",{type:"number",value:k,onChange:e=>q(e.target.value),placeholder:"Enter amount to withdraw (Min: ₹50)",className:"form-input flex-1",min:"50",max:w?.wallet||0}),(0,a.jsx)("button",{onClick:H,disabled:C||!k||!U.allowed||0>=parseFloat(k),className:`whitespace-nowrap ${C?"btn-disabled cursor-not-allowed opacity-50 pointer-events-none":U.allowed&&k&&parseFloat(k)>0?"btn-primary hover:bg-blue-600":"btn-disabled cursor-not-allowed opacity-50"}`,style:{pointerEvents:C?"none":"auto"},children:C?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Processing Withdrawal..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Withdraw ₹",k||"0"]})})]}),(0,a.jsxs)("p",{className:"text-white/60 text-sm mt-2",children:["Available: ",L(w?.wallet||0)," | Minimum: ₹50"]})]}):(0,a.jsxs)("div",{className:"text-center py-6",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4"}),(0,a.jsx)("p",{className:"text-white/60 mb-4",children:"Please add your bank details before making a withdrawal"}),(0,a.jsxs)("button",{onClick:()=>M(!0),className:"btn-primary",children:[(0,a.jsx)("i",{className:"fas fa-university mr-2"}),"Add Bank Details"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Withdrawal History"]}),(0,a.jsxs)("button",{onClick:J,className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]}),0===N.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave text-white/30 text-4xl mb-4"}),(0,a.jsx)("p",{className:"text-white/60 mb-2",children:"No withdrawal requests yet"}),(0,a.jsx)("p",{className:"text-white/40 text-sm",children:"Your withdrawal requests will appear here"})]}):(0,a.jsx)("div",{className:"space-y-3",children:N.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/10 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave text-red-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.description}),(0,a.jsxs)("p",{className:"text-white/60 text-sm",children:[e.date.toLocaleDateString()," at ",e.date.toLocaleTimeString()]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-bold text-red-400",children:L(Math.abs(e.amount))}),(0,a.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${"pending"===e.status?"bg-yellow-500/20 text-yellow-400":"approved"===e.status?"bg-green-500/20 text-green-400":"rejected"===e.status?"bg-red-500/20 text-red-400":"completed"===e.status?"bg-blue-500/20 text-blue-400":"bg-gray-500/20 text-gray-400"}`,children:"pending"===e.status?"⏳ Pending":"approved"===e.status?"✅ Approved":"rejected"===e.status?"❌ Rejected":"completed"===e.status?"✅ Completed":e.status})]})]},e.id))})]})]})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[6204,6958,7567,8441,3582,7979,4887],()=>s(34025));module.exports=a})();