(()=>{var e={};e.id=3179,e.ids=[3179],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},782:(e,s,r)=>{Promise.resolve().then(r.bind(r,68163))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5969:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\daily-active-days\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\daily-active-days\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},40102:(e,s,r)=>{Promise.resolve().then(r.bind(r,5969))},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58043:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(65239),i=r(48088),a=r(88170),l=r.n(a),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c={children:["",{children:["admin",{children:["daily-active-days",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5969)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\daily-active-days\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\daily-active-days\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/daily-active-days/page",pathname:"/admin/daily-active-days",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68163:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(60687),i=r(43210),a=r(85814),l=r.n(a),n=r(87979),d=r(3582),c=r(77567);function o(){let{user:e,loading:s,isAdmin:r}=(0,n.wC)(),[a,o]=(0,i.useState)(!1),[x,m]=(0,i.useState)(null),[u,p]=(0,i.useState)(!1),[h,g]=(0,i.useState)(null),[y,j]=(0,i.useState)(!1),[v,b]=(0,i.useState)(null),[f,N]=(0,i.useState)(!1),[w,C]=(0,i.useState)(null),[k,S]=(0,i.useState)(!1),[A,q]=(0,i.useState)(null);if(s)return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})});if(!r)return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})});let D=async()=>{try{o(!0);let e=await (0,d.Oe)();m(e),c.A.fire({icon:"success",title:"Daily Active Days Increment Completed!",html:`
          <div class="text-left">
            <p><strong>Incremented:</strong> ${e.incrementedCount} users</p>
            <p><strong>Skipped:</strong> ${e.skippedCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
            ${e.reason?`<p><strong>Reason:</strong> ${e.reason}</p>`:""}
          </div>
        `,timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running daily active days increment:",e),c.A.fire({icon:"error",title:"Error",text:"Failed to run daily active days increment. Please try again."})}finally{o(!1)}},E=async()=>{try{p(!0);let e=await (0,d.GA)();g(e),c.A.fire({icon:"success",title:"Quick Video Migration Completed!",html:`
          <div class="text-left">
            <p><strong>Migrated:</strong> ${e.migratedCount} users</p>
            <p><strong>Skipped:</strong> ${e.skippedCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
          </div>
        `,timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running quick video migration:",e),c.A.fire({icon:"error",title:"Error",text:"Failed to run quick video migration. Please try again."})}finally{p(!1)}},P=async()=>{try{j(!0);let e=await (0,d.Kc)();b(e),c.A.fire({icon:"success",title:"Active Days Recalculation Completed!",html:`
          <div class="text-left">
            <p><strong>Recalculated:</strong> ${e.recalculatedCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
            <p class="text-sm text-gray-600 mt-2">All users now have correct active days based on registration date.</p>
          </div>
        `,timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running active days recalculation:",e),c.A.fire({icon:"error",title:"Error",text:"Failed to recalculate active days. Please try again."})}finally{j(!1)}},U=async()=>{try{N(!0);let e=await (0,d.gx)();C(e),c.A.fire({icon:"success",title:"Force Daily Process Catchup Completed!",html:`
          <div class="text-left">
            <p><strong>Incremented:</strong> ${e.incrementedCount} users</p>
            <p><strong>Skipped:</strong> ${e.skippedCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
            <p class="text-sm text-green-600 mt-2">All users now have updated active days regardless of last visit.</p>
          </div>
        `,timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running force catchup:",e),c.A.fire({icon:"error",title:"Error",text:"Failed to run force catchup. Please try again."})}finally{N(!1)}},R=async()=>{try{S(!0);let e=await (0,d.wD)();q(e),c.A.fire({icon:"success",title:"Reset Last Update Completed!",html:`
          <div class="text-left">
            <p><strong>Reset:</strong> ${e.resetCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
            <p class="text-sm text-blue-600 mt-2">All users can now receive fresh daily increments.</p>
          </div>
        `,timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error resetting last update:",e),c.A.fire({icon:"error",title:"Error",text:"Failed to reset last update. Please try again."})}finally{S(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Daily Active Days Management"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"Manually trigger daily active days increment for all users"})]}),(0,t.jsxs)(l(),{href:"/admin",className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin"]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,t.jsx)("i",{className:"fas fa-calendar-plus mr-2 text-blue-500"}),"Daily Active Days Increment"]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"How it works:"}),(0,t.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Increments active days by 1 for all users (regardless of login status)"}),(0,t.jsx)("li",{children:"• Skips users who are on approved leave today"}),(0,t.jsx)("li",{children:"• Skips increment if today is an admin leave day"}),(0,t.jsx)("li",{children:"• Only processes each user once per day"}),(0,t.jsx)("li",{children:"• Preserves manually set active days by admin"})]})]}),(0,t.jsx)("button",{onClick:D,disabled:a,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Processing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-play mr-2"}),"Run Daily Active Days Increment"]})})]}),x&&(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Last Execution Result"}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:x.incrementedCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Incremented"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:x.skippedCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:x.errorCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]}),x.reason&&(0,t.jsx)("div",{className:"mt-4 text-center",children:(0,t.jsx)("span",{className:"inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm",children:x.reason})})]})]}),(0,t.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Important Notes"}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsxs)("ul",{className:"text-yellow-800 text-sm space-y-2",children:[(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Automatic Execution:"})," This process also runs automatically when users interact with the platform (once per day)"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Manual Trigger:"})," Use this page to manually trigger the process if needed"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Safety:"})," The process is safe to run multiple times per day - it will skip users already processed"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Leave Days:"})," Active days will not increment on admin leave days or user leave days"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Manual Override:"})," Users with manually set active days will still get daily increments"]})]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,t.jsx)("i",{className:"fas fa-video mr-2 text-purple-500"}),"Quick Video Advantage Migration"]}),(0,t.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4",children:[(0,t.jsx)("h3",{className:"font-medium text-purple-900 mb-2",children:"Migration Purpose:"}),(0,t.jsxs)("ul",{className:"text-purple-800 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Converts old expiry date system to new remaining days system"}),(0,t.jsx)("li",{children:"• Calculates remaining days from current expiry dates"}),(0,t.jsx)("li",{children:"• Disables expired quick video advantages"}),(0,t.jsx)("li",{children:"• Ensures proper daily decrement functionality"}),(0,t.jsx)("li",{children:"• Safe to run multiple times (skips already migrated users)"})]})]}),(0,t.jsx)("button",{onClick:E,disabled:u,className:"bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Migrating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Migrate Quick Video System"]})})]}),h&&(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Migration Result"}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:h.migratedCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Migrated"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:h.skippedCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:h.errorCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,t.jsx)("i",{className:"fas fa-calculator mr-2 text-orange-500"}),"Active Days Recalculation"]}),(0,t.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4",children:[(0,t.jsx)("h3",{className:"font-medium text-orange-900 mb-2",children:"Fix Active Days Issues:"}),(0,t.jsxs)("ul",{className:"text-orange-800 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Recalculates active days for all users based on correct formula"}),(0,t.jsx)("li",{children:'• Fixes the "showing 3 days instead of 2" issue'}),(0,t.jsx)("li",{children:"• Ensures Trial plans expire at exactly 3+ active days"}),(0,t.jsx)("li",{children:"• Ensures other plans expire at exactly 31+ active days"}),(0,t.jsx)("li",{children:"• Safe to run multiple times (only updates incorrect values)"})]})]}),(0,t.jsx)("button",{onClick:P,disabled:y,className:"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:y?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Recalculating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-calculator mr-2"}),"Fix Active Days Calculation"]})})]}),v&&(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recalculation Result"}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:v.recalculatedCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Fixed"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:v.errorCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,t.jsx)("i",{className:"fas fa-rocket mr-2 text-red-500"}),"Force Daily Process Catchup"]}),(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:[(0,t.jsx)("h3",{className:"font-medium text-red-900 mb-2",children:"Emergency Active Days Update:"}),(0,t.jsxs)("ul",{className:"text-red-800 text-sm space-y-1",children:[(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Forces daily process to run"})," regardless of last run date"]}),(0,t.jsx)("li",{children:"• Updates ALL users' active days by +1 (except on leave days)"}),(0,t.jsx)("li",{children:"• Use this when many users haven't received daily increments"}),(0,t.jsx)("li",{children:"• Safe to run - respects leave days and prevents double increments"}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Recommended:"})," Run this to catch up all missed days"]})]})]}),(0,t.jsx)("button",{onClick:U,disabled:f,className:"bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:f?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Force Processing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-rocket mr-2"}),"Force Daily Process Catchup"]})})]}),w&&(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Force Catchup Result"}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:w.incrementedCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Updated"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:w.skippedCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:w.errorCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,t.jsx)("i",{className:"fas fa-undo mr-2 text-indigo-500"}),"Reset Last Update Tracking"]}),(0,t.jsxs)("div",{className:"bg-indigo-50 border border-indigo-200 rounded-lg p-4 mb-4",children:[(0,t.jsx)("h3",{className:"font-medium text-indigo-900 mb-2",children:"Reset Update Tracking:"}),(0,t.jsxs)("ul",{className:"text-indigo-800 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Clears the lastActiveDaysUpdate field for all users"}),(0,t.jsx)("li",{children:"• Allows fresh daily increments for all users"}),(0,t.jsx)("li",{children:"• Use this if users are stuck with old update timestamps"}),(0,t.jsx)("li",{children:"• Run this BEFORE running the force catchup for best results"}),(0,t.jsx)("li",{children:"• Safe to run multiple times"})]})]}),(0,t.jsx)("button",{onClick:R,disabled:k,className:"bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Resetting..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-undo mr-2"}),"Reset All Users' Last Update"]})})]}),A&&(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Reset Result"}),(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:A.resetCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Reset"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:A.errorCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,t.jsx)("i",{className:"fas fa-lightbulb mr-2 text-yellow-500"}),"Recommended Process for Fixing Active Days"]}),(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-medium text-yellow-900 mb-3",children:"Step-by-Step Fix:"}),(0,t.jsxs)("ol",{className:"text-yellow-800 text-sm space-y-2 list-decimal list-inside",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Step 1:"}),' Click "Reset All Users\' Last Update" to clear tracking']}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Step 2:"}),' Click "Force Daily Process Catchup" to update all users']}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Step 3:"})," Verify results - all users should now have correct active days"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Step 4:"})," The daily process will now run automatically going forward"]})]}),(0,t.jsx)("div",{className:"mt-4 p-3 bg-yellow-100 rounded-lg",children:(0,t.jsxs)("p",{className:"text-yellow-900 text-sm font-medium",children:["\uD83D\uDCA1 ",(0,t.jsx)("strong",{children:"Important:"})," After running these steps, active days will automatically increment daily for ALL users, regardless of whether they visit the platform or not."]})})]})]})]})})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[6204,2756,7567,8441,3582,7979],()=>r(58043));module.exports=t})();