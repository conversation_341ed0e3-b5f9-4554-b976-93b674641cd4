"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2992],{7015:(e,t,n)=>{n.d(t,{AB:()=>lr,BN:()=>lT,Dc:()=>U,GG:()=>lI,GV:()=>lN,H9:()=>op,HM:()=>ls,My:()=>lt,P:()=>o8,_M:()=>o9,aU:()=>ov,d_:()=>lx,gS:()=>lb,kd:()=>lE,mZ:()=>l_,rJ:()=>og,x7:()=>lw});var r,i,s,a,o=n(2612),l=n(6391),u=n(796),h=n(7222),c=n(2107),d=n(927),f=n(9509),m=n(7131).hp;let g="@firebase/firestore";class p{constructor(e){this.uid=e}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}}p.UNAUTHENTICATED=new p(null),p.GOOGLE_CREDENTIALS=new p("google-credentials-uid"),p.FIRST_PARTY=new p("first-party-uid"),p.MOCK_USER=new p("mock-user");let y="10.14.0",w=new u.Vy("@firebase/firestore");function v(){return w.logLevel}function I(e,...t){if(w.logLevel<=u.$b.DEBUG){let n=t.map(E);w.debug(`Firestore (${y}): ${e}`,...n)}}function T(e,...t){if(w.logLevel<=u.$b.ERROR){let n=t.map(E);w.error(`Firestore (${y}): ${e}`,...n)}}function _(e,...t){if(w.logLevel<=u.$b.WARN){let n=t.map(E);w.warn(`Firestore (${y}): ${e}`,...n)}}function E(e){if("string"==typeof e)return e;try{return JSON.stringify(e)}catch(t){return e}}function b(e="Unexpected state"){let t=`FIRESTORE (${y}) INTERNAL ASSERTION FAILED: `+e;throw T(t),Error(t)}let S={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class x extends h.g{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}}class D{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}class C{constructor(e,t){this.user=t,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization",`Bearer ${e}`)}}class N{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,t){e.enqueueRetryable(()=>t(p.UNAUTHENTICATED))}shutdown(){}}class A{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,t){this.changeListener=t,e.enqueueRetryable(()=>t(this.token.user))}shutdown(){this.changeListener=null}}class k{constructor(e){this.t=e,this.currentUser=p.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(e,t){void 0===this.o||b();let n=this.i,r=e=>this.i!==n?(n=this.i,t(e)):Promise.resolve(),i=new D;this.o=()=>{this.i++,this.currentUser=this.u(),i.resolve(),i=new D,e.enqueueRetryable(()=>r(this.currentUser))};let s=()=>{let t=i;e.enqueueRetryable(async()=>{await t.promise,await r(this.currentUser)})},a=e=>{I("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=e,this.o&&(this.auth.addAuthTokenListener(this.o),s())};this.t.onInit(e=>a(e)),setTimeout(()=>{if(!this.auth){let e=this.t.getImmediate({optional:!0});e?a(e):(I("FirebaseAuthCredentialsProvider","Auth not yet detected"),i.resolve(),i=new D)}},0),s()}getToken(){let e=this.i,t=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(t).then(t=>this.i!==e?(I("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):t?("string"==typeof t.accessToken||b(),new C(t.accessToken,this.currentUser)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){let e=this.auth&&this.auth.getUid();return null===e||"string"==typeof e||b(),new p(e)}}class V{constructor(e,t,n){this.l=e,this.h=t,this.P=n,this.type="FirstParty",this.user=p.FIRST_PARTY,this.I=new Map}T(){return this.P?this.P():null}get headers(){this.I.set("X-Goog-AuthUser",this.l);let e=this.T();return e&&this.I.set("Authorization",e),this.h&&this.I.set("X-Goog-Iam-Authorization-Token",this.h),this.I}}class R{constructor(e,t,n){this.l=e,this.h=t,this.P=n}getToken(){return Promise.resolve(new V(this.l,this.h,this.P))}start(e,t){e.enqueueRetryable(()=>t(p.FIRST_PARTY))}shutdown(){}invalidateToken(){}}class O{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&e.length>0&&this.headers.set("x-firebase-appcheck",this.value)}}class M{constructor(e){this.A=e,this.forceRefresh=!1,this.appCheck=null,this.R=null}start(e,t){void 0===this.o||b();let n=e=>{null!=e.error&&I("FirebaseAppCheckTokenProvider",`Error getting App Check token; using placeholder token instead. Error: ${e.error.message}`);let n=e.token!==this.R;return this.R=e.token,I("FirebaseAppCheckTokenProvider",`Received ${n?"new":"existing"} token.`),n?t(e.token):Promise.resolve()};this.o=t=>{e.enqueueRetryable(()=>n(t))};let r=e=>{I("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=e,this.o&&this.appCheck.addTokenListener(this.o)};this.A.onInit(e=>r(e)),setTimeout(()=>{if(!this.appCheck){let e=this.A.getImmediate({optional:!0});e?r(e):I("FirebaseAppCheckTokenProvider","AppCheck not yet detected")}},0)}getToken(){let e=this.forceRefresh;return this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(e).then(e=>e?("string"==typeof e.token||b(),this.R=e.token,new O(e.token)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}}class F{static newId(){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t=Math.floor(256/e.length)*e.length,n="";for(;n.length<20;){let r=function(e){let t="undefined"!=typeof self&&(self.crypto||self.msCrypto),n=new Uint8Array(40);if(t&&"function"==typeof t.getRandomValues)t.getRandomValues(n);else for(let e=0;e<40;e++)n[e]=Math.floor(256*Math.random());return n}(40);for(let i=0;i<r.length;++i)n.length<20&&r[i]<t&&(n+=e.charAt(r[i]%e.length))}return n}}function L(e,t){return e<t?-1:+(e>t)}function P(e,t,n){return e.length===t.length&&e.every((e,r)=>n(e,t[r]))}class U{constructor(e,t){if(this.seconds=e,this.nanoseconds=t,t<0||t>=1e9)throw new x(S.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(e<-0xe7791f700||e>=0x3afff44180)throw new x(S.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e)}static now(){return U.fromMillis(Date.now())}static fromDate(e){return U.fromMillis(e.getTime())}static fromMillis(e){let t=Math.floor(e/1e3),n=Math.floor(1e6*(e-1e3*t));return new U(t,n)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/1e6}_compareTo(e){return this.seconds===e.seconds?L(this.nanoseconds,e.nanoseconds):L(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{seconds:this.seconds,nanoseconds:this.nanoseconds}}valueOf(){return String(this.seconds- -0xe7791f700).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}class q{constructor(e){this.timestamp=e}static fromTimestamp(e){return new q(e)}static min(){return new q(new U(0,0))}static max(){return new q(new U(0x3afff4417f,0x3b9ac9ff))}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}class B{constructor(e,t,n){void 0===t?t=0:t>e.length&&b(),void 0===n?n=e.length-t:n>e.length-t&&b(),this.segments=e,this.offset=t,this.len=n}get length(){return this.len}isEqual(e){return 0===B.comparator(this,e)}child(e){let t=this.segments.slice(this.offset,this.limit());return e instanceof B?e.forEach(e=>{t.push(e)}):t.push(e),this.construct(t)}limit(){return this.offset+this.length}popFirst(e){return e=void 0===e?1:e,this.construct(this.segments,this.offset+e,this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return 0===this.length}isPrefixOf(e){if(e.length<this.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}forEach(e){for(let t=this.offset,n=this.limit();t<n;t++)e(this.segments[t])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(e,t){let n=Math.min(e.length,t.length);for(let r=0;r<n;r++){let n=e.get(r),i=t.get(r);if(n<i)return -1;if(n>i)return 1}return e.length<t.length?-1:+(e.length>t.length)}}class K extends B{construct(e,t,n){return new K(e,t,n)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){let t=[];for(let n of e){if(n.indexOf("//")>=0)throw new x(S.INVALID_ARGUMENT,`Invalid segment (${n}). Paths must not contain // in them.`);t.push(...n.split("/").filter(e=>e.length>0))}return new K(t)}static emptyPath(){return new K([])}}let z=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class G extends B{construct(e,t,n){return new G(e,t,n)}static isValidIdentifier(e){return z.test(e)}canonicalString(){return this.toArray().map(e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),G.isValidIdentifier(e)||(e="`"+e+"`"),e)).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&"__name__"===this.get(0)}static keyField(){return new G(["__name__"])}static fromServerFormat(e){let t=[],n="",r=0,i=()=>{if(0===n.length)throw new x(S.INVALID_ARGUMENT,`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);t.push(n),n=""},s=!1;for(;r<e.length;){let t=e[r];if("\\"===t){if(r+1===e.length)throw new x(S.INVALID_ARGUMENT,"Path has trailing escape character: "+e);let t=e[r+1];if("\\"!==t&&"."!==t&&"`"!==t)throw new x(S.INVALID_ARGUMENT,"Path has invalid escape sequence: "+e);n+=t,r+=2}else"`"===t?s=!s:"."!==t||s?n+=t:i(),r++}if(i(),s)throw new x(S.INVALID_ARGUMENT,"Unterminated ` in path: "+e);return new G(t)}static emptyPath(){return new G([])}}class ${constructor(e){this.path=e}static fromPath(e){return new $(K.fromString(e))}static fromName(e){return new $(K.fromString(e).popFirst(5))}static empty(){return new $(K.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return this.path.length>=2&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return null!==e&&0===K.comparator(this.path,e.path)}toString(){return this.path.toString()}static comparator(e,t){return K.comparator(e.path,t.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new $(new K(e.slice()))}}class Q{constructor(e,t,n,r){this.indexId=e,this.collectionGroup=t,this.fields=n,this.indexState=r}}function j(e){return e.fields.find(e=>2===e.kind)}function W(e){return e.fields.filter(e=>2!==e.kind)}Q.UNKNOWN_ID=-1;class H{constructor(e,t){this.fieldPath=e,this.kind=t}}class J{constructor(e,t){this.sequenceNumber=e,this.offset=t}static empty(){return new J(0,Z.min())}}function Y(e,t){let n=e.toTimestamp().seconds,r=e.toTimestamp().nanoseconds+1;return new Z(q.fromTimestamp(1e9===r?new U(n+1,0):new U(n,r)),$.empty(),t)}function X(e){return new Z(e.readTime,e.key,-1)}class Z{constructor(e,t,n){this.readTime=e,this.documentKey=t,this.largestBatchId=n}static min(){return new Z(q.min(),$.empty(),-1)}static max(){return new Z(q.max(),$.empty(),-1)}}function ee(e,t){let n=e.readTime.compareTo(t.readTime);return 0!==n||0!==(n=$.comparator(e.documentKey,t.documentKey))?n:L(e.largestBatchId,t.largestBatchId)}let et="The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab.";class en{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(e){this.onCommittedListeners.push(e)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach(e=>e())}}async function er(e){if(e.code!==S.FAILED_PRECONDITION||e.message!==et)throw e;I("LocalStore","Unexpectedly lost primary lease")}class ei{constructor(e){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,e(e=>{this.isDone=!0,this.result=e,this.nextCallback&&this.nextCallback(e)},e=>{this.isDone=!0,this.error=e,this.catchCallback&&this.catchCallback(e)})}catch(e){return this.next(void 0,e)}next(e,t){return this.callbackAttached&&b(),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(t,this.error):this.wrapSuccess(e,this.result):new ei((n,r)=>{this.nextCallback=t=>{this.wrapSuccess(e,t).next(n,r)},this.catchCallback=e=>{this.wrapFailure(t,e).next(n,r)}})}toPromise(){return new Promise((e,t)=>{this.next(e,t)})}wrapUserFunction(e){try{let t=e();return t instanceof ei?t:ei.resolve(t)}catch(e){return ei.reject(e)}}wrapSuccess(e,t){return e?this.wrapUserFunction(()=>e(t)):ei.resolve(t)}wrapFailure(e,t){return e?this.wrapUserFunction(()=>e(t)):ei.reject(t)}static resolve(e){return new ei((t,n)=>{t(e)})}static reject(e){return new ei((t,n)=>{n(e)})}static waitFor(e){return new ei((t,n)=>{let r=0,i=0,s=!1;e.forEach(e=>{++r,e.next(()=>{++i,s&&i===r&&t()},e=>n(e))}),s=!0,i===r&&t()})}static or(e){let t=ei.resolve(!1);for(let n of e)t=t.next(e=>e?ei.resolve(e):n());return t}static forEach(e,t){let n=[];return e.forEach((e,r)=>{n.push(t.call(this,e,r))}),this.waitFor(n)}static mapArray(e,t){return new ei((n,r)=>{let i=e.length,s=Array(i),a=0;for(let o=0;o<i;o++){let l=o;t(e[l]).next(e=>{s[l]=e,++a===i&&n(s)},e=>r(e))}})}static doWhile(e,t){return new ei((n,r)=>{let i=()=>{!0===e()?t().next(()=>{i()},r):n()};i()})}}class es{constructor(e,t){this.action=e,this.transaction=t,this.aborted=!1,this.V=new D,this.transaction.oncomplete=()=>{this.V.resolve()},this.transaction.onabort=()=>{t.error?this.V.reject(new eu(e,t.error)):this.V.resolve()},this.transaction.onerror=t=>{let n=em(t.target.error);this.V.reject(new eu(e,n))}}static open(e,t,n,r){try{return new es(t,e.transaction(r,n))}catch(e){throw new eu(t,e)}}get m(){return this.V.promise}abort(e){e&&this.V.reject(e),this.aborted||(I("SimpleDb","Aborting transaction:",e?e.message:"Client-initiated abort"),this.aborted=!0,this.transaction.abort())}g(){let e=this.transaction;this.aborted||"function"!=typeof e.commit||e.commit()}store(e){return new ec(this.transaction.objectStore(e))}}class ea{constructor(e,t,n){this.name=e,this.version=t,this.p=n,12.2===ea.S((0,h.ZQ)())&&T("Firestore persistence suffers from a bug in iOS 12.2 Safari that may cause your app to stop working. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.")}static delete(e){return I("SimpleDb","Removing database:",e),ed(window.indexedDB.deleteDatabase(e)).toPromise()}static D(){if(!(0,h.zW)())return!1;if(ea.v())return!0;let e=(0,h.ZQ)(),t=ea.S(e),n=eo(e);return!(e.indexOf("MSIE ")>0||e.indexOf("Trident/")>0||e.indexOf("Edge/")>0||0<t&&t<10||0<n&&n<4.5)}static v(){var e;return void 0!==f&&"YES"===(null==(e=f.__PRIVATE_env)?void 0:e.C)}static F(e,t){return e.store(t)}static S(e){let t=e.match(/i(?:phone|pad|pod) os ([\d_]+)/i);return Number(t?t[1].split("_").slice(0,2).join("."):"-1")}async M(e){return this.db||(I("SimpleDb","Opening database:",this.name),this.db=await new Promise((t,n)=>{let r=indexedDB.open(this.name,this.version);r.onsuccess=e=>{t(e.target.result)},r.onblocked=()=>{n(new eu(e,"Cannot upgrade IndexedDB schema while another tab is open. Close all tabs that access Firestore and reload this page to proceed."))},r.onerror=t=>{let r=t.target.error;"VersionError"===r.name?n(new x(S.FAILED_PRECONDITION,"A newer version of the Firestore SDK was previously used and so the persisted data is not compatible with the version of the SDK you are now using. The SDK will operate with persistence disabled. If you need persistence, please re-upgrade to a newer version of the SDK or else clear the persisted IndexedDB data for your app to start fresh.")):"InvalidStateError"===r.name?n(new x(S.FAILED_PRECONDITION,"Unable to open an IndexedDB connection. This could be due to running in a private browsing session on a browser whose private browsing sessions do not support IndexedDB: "+r)):n(new eu(e,r))},r.onupgradeneeded=e=>{I("SimpleDb",'Database "'+this.name+'" requires upgrade from version:',e.oldVersion);let t=e.target.result;this.p.O(t,r.transaction,e.oldVersion,this.version).next(()=>{I("SimpleDb","Database upgrade to version "+this.version+" complete")})}})),this.N&&(this.db.onversionchange=e=>this.N(e)),this.db}L(e){this.N=e,this.db&&(this.db.onversionchange=t=>e(t))}async runTransaction(e,t,n,r){let i="readonly"===t,s=0;for(;;){++s;try{this.db=await this.M(e);let t=es.open(this.db,e,i?"readonly":"readwrite",n),s=r(t).next(e=>(t.g(),e)).catch(e=>(t.abort(e),ei.reject(e))).toPromise();return s.catch(()=>{}),await t.m,s}catch(t){let e="FirebaseError"!==t.name&&s<3;if(I("SimpleDb","Transaction failed with error:",t.message,"Retrying:",e),this.close(),!e)return Promise.reject(t)}}}close(){this.db&&this.db.close(),this.db=void 0}}function eo(e){let t=e.match(/Android ([\d.]+)/i);return Number(t?t[1].split(".").slice(0,2).join("."):"-1")}class el{constructor(e){this.B=e,this.k=!1,this.q=null}get isDone(){return this.k}get K(){return this.q}set cursor(e){this.B=e}done(){this.k=!0}$(e){this.q=e}delete(){return ed(this.B.delete())}}class eu extends x{constructor(e,t){super(S.UNAVAILABLE,`IndexedDB transaction '${e}' failed: ${t}`),this.name="IndexedDbTransactionError"}}function eh(e){return"IndexedDbTransactionError"===e.name}class ec{constructor(e){this.store=e}put(e,t){let n;return void 0!==t?(I("SimpleDb","PUT",this.store.name,e,t),n=this.store.put(t,e)):(I("SimpleDb","PUT",this.store.name,"<auto-key>",e),n=this.store.put(e)),ed(n)}add(e){return I("SimpleDb","ADD",this.store.name,e,e),ed(this.store.add(e))}get(e){return ed(this.store.get(e)).next(t=>(void 0===t&&(t=null),I("SimpleDb","GET",this.store.name,e,t),t))}delete(e){return I("SimpleDb","DELETE",this.store.name,e),ed(this.store.delete(e))}count(){return I("SimpleDb","COUNT",this.store.name),ed(this.store.count())}U(e,t){let n=this.options(e,t),r=n.index?this.store.index(n.index):this.store;if("function"==typeof r.getAll){let e=r.getAll(n.range);return new ei((t,n)=>{e.onerror=e=>{n(e.target.error)},e.onsuccess=e=>{t(e.target.result)}})}{let e=this.cursor(n),t=[];return this.W(e,(e,n)=>{t.push(n)}).next(()=>t)}}G(e,t){let n=this.store.getAll(e,null===t?void 0:t);return new ei((e,t)=>{n.onerror=e=>{t(e.target.error)},n.onsuccess=t=>{e(t.target.result)}})}j(e,t){I("SimpleDb","DELETE ALL",this.store.name);let n=this.options(e,t);n.H=!1;let r=this.cursor(n);return this.W(r,(e,t,n)=>n.delete())}J(e,t){let n;t?n=e:(n={},t=e);let r=this.cursor(n);return this.W(r,t)}Y(e){let t=this.cursor({});return new ei((n,r)=>{t.onerror=e=>{r(em(e.target.error))},t.onsuccess=t=>{let r=t.target.result;r?e(r.primaryKey,r.value).next(e=>{e?r.continue():n()}):n()}})}W(e,t){let n=[];return new ei((r,i)=>{e.onerror=e=>{i(e.target.error)},e.onsuccess=e=>{let i=e.target.result;if(!i)return void r();let s=new el(i),a=t(i.primaryKey,i.value,s);if(a instanceof ei){let e=a.catch(e=>(s.done(),ei.reject(e)));n.push(e)}s.isDone?r():null===s.K?i.continue():i.continue(s.K)}}).next(()=>ei.waitFor(n))}options(e,t){let n;return void 0!==e&&("string"==typeof e?n=e:t=e),{index:n,range:t}}cursor(e){let t="next";if(e.reverse&&(t="prev"),e.index){let n=this.store.index(e.index);return e.H?n.openKeyCursor(e.range,t):n.openCursor(e.range,t)}return this.store.openCursor(e.range,t)}}function ed(e){return new ei((t,n)=>{e.onsuccess=e=>{t(e.target.result)},e.onerror=e=>{n(em(e.target.error))}})}let ef=!1;function em(e){let t=ea.S((0,h.ZQ)());if(t>=12.2&&t<13){let t="An internal error was encountered in the Indexed Database server";if(e.message.indexOf(t)>=0){let e=new x("internal",`IOS_INDEXEDDB_BUG1: IndexedDb has thrown '${t}'. This is likely due to an unavoidable bug in iOS. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.`);return ef||(ef=!0,setTimeout(()=>{throw e},0)),e}}return e}class eg{constructor(e,t){this.asyncQueue=e,this.Z=t,this.task=null}start(){this.X(15e3)}stop(){this.task&&(this.task.cancel(),this.task=null)}get started(){return null!==this.task}X(e){I("IndexBackfiller",`Scheduled in ${e}ms`),this.task=this.asyncQueue.enqueueAfterDelay("index_backfill",e,async()=>{this.task=null;try{I("IndexBackfiller",`Documents written: ${await this.Z.ee()}`)}catch(e){eh(e)?I("IndexBackfiller","Ignoring IndexedDB error during index backfill: ",e):await er(e)}await this.X(6e4)})}}class ep{constructor(e,t){this.localStore=e,this.persistence=t}async ee(e=50){return this.persistence.runTransaction("Backfill Indexes","readwrite-primary",t=>this.te(t,e))}te(e,t){let n=new Set,r=t,i=!0;return ei.doWhile(()=>!0===i&&r>0,()=>this.localStore.indexManager.getNextCollectionGroupToUpdate(e).next(t=>{if(null!==t&&!n.has(t))return I("IndexBackfiller",`Processing collection: ${t}`),this.ne(e,t,r).next(e=>{r-=e,n.add(t)});i=!1})).next(()=>t-r)}ne(e,t,n){return this.localStore.indexManager.getMinOffsetFromCollectionGroup(e,t).next(r=>this.localStore.localDocuments.getNextDocuments(e,t,r,n).next(n=>{let i=n.changes;return this.localStore.indexManager.updateIndexEntries(e,i).next(()=>this.re(r,n)).next(n=>(I("IndexBackfiller",`Updating offset: ${n}`),this.localStore.indexManager.updateCollectionGroup(e,t,n))).next(()=>i.size)}))}re(e,t){let n=e;return t.changes.forEach((e,t)=>{let r=X(t);ee(r,n)>0&&(n=r)}),new Z(n.readTime,n.documentKey,Math.max(t.batchId,e.largestBatchId))}}class ey{constructor(e,t){this.previousValue=e,t&&(t.sequenceNumberHandler=e=>this.ie(e),this.se=e=>t.writeSequenceNumber(e))}ie(e){return this.previousValue=Math.max(e,this.previousValue),this.previousValue}next(){let e=++this.previousValue;return this.se&&this.se(e),e}}function ew(e){return null==e}function ev(e){return 0===e&&1/e==-1/0}function eI(e){return"number"==typeof e&&Number.isInteger(e)&&!ev(e)&&e<=Number.MAX_SAFE_INTEGER&&e>=Number.MIN_SAFE_INTEGER}function eT(e){let t="";for(let n=0;n<e.length;n++)t.length>0&&(t+="\x01\x01"),t=function(e,t){let n=t,r=e.length;for(let t=0;t<r;t++){let r=e.charAt(t);switch(r){case"\0":n+="\x01\x10";break;case"\x01":n+="\x01\x11";break;default:n+=r}}return n}(e.get(n),t);return t+"\x01\x01"}ey.oe=-1;function e_(e){let t=e.length;if(t>=2||b(),2===t)return"\x01"===e.charAt(0)&&"\x01"===e.charAt(1)||b(),K.emptyPath();let n=t-2,r=[],i="";for(let s=0;s<t;){let t=e.indexOf("\x01",s);switch((t<0||t>n)&&b(),e.charAt(t+1)){case"\x01":let a,o=e.substring(s,t);0===i.length?a=o:(i+=o,a=i,i=""),r.push(a);break;case"\x10":i+=e.substring(s,t),i+="\0";break;case"\x11":i+=e.substring(s,t+1);break;default:b()}s=t+2}return new K(r)}let eE=["userId","batchId"],eb={},eS=["prefixPath","collectionGroup","readTime","documentId"],ex=["prefixPath","collectionGroup","documentId"],eD=["collectionGroup","readTime","prefixPath","documentId"],eC=["canonicalId","targetId"],eN=["targetId","path"],eA=["path","targetId"],ek=["collectionId","parent"],eV=["indexId","uid"],eR=["uid","sequenceNumber"],eO=["indexId","uid","arrayValue","directionalValue","orderedDocumentKey","documentKey"],eM=["indexId","uid","orderedDocumentKey"],eF=["userId","collectionPath","documentId"],eL=["userId","collectionPath","largestBatchId"],eP=["userId","collectionGroup","largestBatchId"],eU=["mutationQueues","mutations","documentMutations","remoteDocuments","targets","owner","targetGlobal","targetDocuments","clientMetadata","remoteDocumentGlobal","collectionParents","bundles","namedQueries"],eq=[...eU,"documentOverlays"],eB=["mutationQueues","mutations","documentMutations","remoteDocumentsV14","targets","owner","targetGlobal","targetDocuments","clientMetadata","remoteDocumentGlobal","collectionParents","bundles","namedQueries","documentOverlays"],eK=[...eB,"indexConfiguration","indexState","indexEntries"],ez=[...eK,"globals"];class eG extends en{constructor(e,t){super(),this._e=e,this.currentSequenceNumber=t}}function e$(e,t){return ea.F(e._e,t)}function eQ(e){let t=0;for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t++;return t}function ej(e,t){for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t(n,e[n])}function eW(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}class eH{constructor(e,t){this.comparator=e,this.root=t||eY.EMPTY}insert(e,t){return new eH(this.comparator,this.root.insert(e,t,this.comparator).copy(null,null,eY.BLACK,null,null))}remove(e){return new eH(this.comparator,this.root.remove(e,this.comparator).copy(null,null,eY.BLACK,null,null))}get(e){let t=this.root;for(;!t.isEmpty();){let n=this.comparator(e,t.key);if(0===n)return t.value;n<0?t=t.left:n>0&&(t=t.right)}return null}indexOf(e){let t=0,n=this.root;for(;!n.isEmpty();){let r=this.comparator(e,n.key);if(0===r)return t+n.left.size;r<0?n=n.left:(t+=n.left.size+1,n=n.right)}return -1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(e){this.inorderTraversal((t,n)=>(e(t,n),!1))}toString(){let e=[];return this.inorderTraversal((t,n)=>(e.push(`${t}:${n}`),!1)),`{${e.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new eJ(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new eJ(this.root,e,this.comparator,!1)}getReverseIterator(){return new eJ(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new eJ(this.root,e,this.comparator,!0)}}class eJ{constructor(e,t,n,r){this.isReverse=r,this.nodeStack=[];let i=1;for(;!e.isEmpty();)if(i=t?n(e.key,t):1,t&&r&&(i*=-1),i<0)e=this.isReverse?e.left:e.right;else{if(0===i){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop(),t={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return t}hasNext(){return this.nodeStack.length>0}peek(){if(0===this.nodeStack.length)return null;let e=this.nodeStack[this.nodeStack.length-1];return{key:e.key,value:e.value}}}class eY{constructor(e,t,n,r,i){this.key=e,this.value=t,this.color=null!=n?n:eY.RED,this.left=null!=r?r:eY.EMPTY,this.right=null!=i?i:eY.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,t,n,r,i){return new eY(null!=e?e:this.key,null!=t?t:this.value,null!=n?n:this.color,null!=r?r:this.left,null!=i?i:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,n){let r=this,i=n(e,r.key);return(r=i<0?r.copy(null,null,null,r.left.insert(e,t,n),null):0===i?r.copy(null,t,null,null,null):r.copy(null,null,null,null,r.right.insert(e,t,n))).fixUp()}removeMin(){if(this.left.isEmpty())return eY.EMPTY;let e=this;return e.left.isRed()||e.left.left.isRed()||(e=e.moveRedLeft()),(e=e.copy(null,null,null,e.left.removeMin(),null)).fixUp()}remove(e,t){let n,r=this;if(0>t(e,r.key))r.left.isEmpty()||r.left.isRed()||r.left.left.isRed()||(r=r.moveRedLeft()),r=r.copy(null,null,null,r.left.remove(e,t),null);else{if(r.left.isRed()&&(r=r.rotateRight()),r.right.isEmpty()||r.right.isRed()||r.right.left.isRed()||(r=r.moveRedRight()),0===t(e,r.key)){if(r.right.isEmpty())return eY.EMPTY;n=r.right.min(),r=r.copy(n.key,n.value,null,null,r.right.removeMin())}r=r.copy(null,null,null,null,r.right.remove(e,t))}return r.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e.right.isRed()&&!e.left.isRed()&&(e=e.rotateLeft()),e.left.isRed()&&e.left.left.isRed()&&(e=e.rotateRight()),e.left.isRed()&&e.right.isRed()&&(e=e.colorFlip()),e}moveRedLeft(){let e=this.colorFlip();return e.right.left.isRed()&&(e=(e=(e=e.copy(null,null,null,null,e.right.rotateRight())).rotateLeft()).colorFlip()),e}moveRedRight(){let e=this.colorFlip();return e.left.left.isRed()&&(e=(e=e.rotateRight()).colorFlip()),e}rotateLeft(){let e=this.copy(null,null,eY.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){let e=this.copy(null,null,eY.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){let e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth(){return Math.pow(2,this.check())<=this.size+1}check(){if(this.isRed()&&this.left.isRed()||this.right.isRed())throw b();let e=this.left.check();if(e!==this.right.check())throw b();return e+ +!this.isRed()}}eY.EMPTY=null,eY.RED=!0,eY.BLACK=!1,eY.EMPTY=new class{constructor(){this.size=0}get key(){throw b()}get value(){throw b()}get color(){throw b()}get left(){throw b()}get right(){throw b()}copy(e,t,n,r,i){return this}insert(e,t,n){return new eY(e,t)}remove(e,t){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};class eX{constructor(e){this.comparator=e,this.data=new eH(this.comparator)}has(e){return null!==this.data.get(e)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(e){this.data.inorderTraversal((t,n)=>(e(t),!1))}forEachInRange(e,t){let n=this.data.getIteratorFrom(e[0]);for(;n.hasNext();){let r=n.getNext();if(this.comparator(r.key,e[1])>=0)return;t(r.key)}}forEachWhile(e,t){let n;for(n=void 0!==t?this.data.getIteratorFrom(t):this.data.getIterator();n.hasNext();)if(!e(n.getNext().key))return}firstAfterOrEqual(e){let t=this.data.getIteratorFrom(e);return t.hasNext()?t.getNext().key:null}getIterator(){return new eZ(this.data.getIterator())}getIteratorFrom(e){return new eZ(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let t=this;return t.size<e.size&&(t=e,e=this),e.forEach(e=>{t=t.add(e)}),t}isEqual(e){if(!(e instanceof eX)||this.size!==e.size)return!1;let t=this.data.getIterator(),n=e.data.getIterator();for(;t.hasNext();){let e=t.getNext().key,r=n.getNext().key;if(0!==this.comparator(e,r))return!1}return!0}toArray(){let e=[];return this.forEach(t=>{e.push(t)}),e}toString(){let e=[];return this.forEach(t=>e.push(t)),"SortedSet("+e.toString()+")"}copy(e){let t=new eX(this.comparator);return t.data=e,t}}class eZ{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}function e0(e){return e.hasNext()?e.getNext():void 0}class e1{constructor(e){this.fields=e,e.sort(G.comparator)}static empty(){return new e1([])}unionWith(e){let t=new eX(G.comparator);for(let e of this.fields)t=t.add(e);for(let n of e)t=t.add(n);return new e1(t.toArray())}covers(e){for(let t of this.fields)if(t.isPrefixOf(e))return!0;return!1}isEqual(e){return P(this.fields,e.fields,(e,t)=>e.isEqual(t))}}class e2 extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}class e5{constructor(e){this.binaryString=e}static fromBase64String(e){return new e5(function(e){try{return atob(e)}catch(e){throw"undefined"!=typeof DOMException&&e instanceof DOMException?new e2("Invalid base64 string: "+e):e}}(e))}static fromUint8Array(e){return new e5(function(e){let t="";for(let n=0;n<e.length;++n)t+=String.fromCharCode(e[n]);return t}(e))}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return btoa(this.binaryString)}toUint8Array(){var e=this.binaryString;let t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return L(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}}e5.EMPTY_BYTE_STRING=new e5("");let e3=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function e4(e){if(e||b(),"string"==typeof e){let t=0,n=e3.exec(e);if(n||b(),n[1]){let e=n[1];t=Number(e=(e+"000000000").substr(0,9))}return{seconds:Math.floor(new Date(e).getTime()/1e3),nanos:t}}return{seconds:e8(e.seconds),nanos:e8(e.nanos)}}function e8(e){return"number"==typeof e?e:"string"==typeof e?Number(e):0}function e6(e){return"string"==typeof e?e5.fromBase64String(e):e5.fromUint8Array(e)}function e9(e){var t,n;return"server_timestamp"===(null==(n=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{}).__type__)?void 0:n.stringValue)}function e7(e){let t=e.mapValue.fields.__previous_value__;return e9(t)?e7(t):t}function te(e){let t=e4(e.mapValue.fields.__local_write_time__.timestampValue);return new U(t.seconds,t.nanos)}class tt{constructor(e,t,n,r,i,s,a,o,l){this.databaseId=e,this.appId=t,this.persistenceKey=n,this.host=r,this.ssl=i,this.forceLongPolling=s,this.autoDetectLongPolling=a,this.longPollingOptions=o,this.useFetchStreams=l}}class tn{constructor(e,t){this.projectId=e,this.database=t||"(default)"}static empty(){return new tn("","")}get isDefaultDatabase(){return"(default)"===this.database}isEqual(e){return e instanceof tn&&e.projectId===this.projectId&&e.database===this.database}}let tr={mapValue:{fields:{__type__:{stringValue:"__max__"}}}},ti={nullValue:"NULL_VALUE"};function ts(e){return"nullValue"in e?0:"booleanValue"in e?1:"integerValue"in e||"doubleValue"in e?2:"timestampValue"in e?3:"stringValue"in e?5:"bytesValue"in e?6:"referenceValue"in e?7:"geoPointValue"in e?8:"arrayValue"in e?9:"mapValue"in e?e9(e)?4:tI(e)?0x1fffffffffffff:tw(e)?10:11:b()}function ta(e,t){if(e===t)return!0;let n=ts(e);if(n!==ts(t))return!1;switch(n){case 0:case 0x1fffffffffffff:return!0;case 1:return e.booleanValue===t.booleanValue;case 4:return te(e).isEqual(te(t));case 3:if("string"==typeof e.timestampValue&&"string"==typeof t.timestampValue&&e.timestampValue.length===t.timestampValue.length)return e.timestampValue===t.timestampValue;let r=e4(e.timestampValue),i=e4(t.timestampValue);return r.seconds===i.seconds&&r.nanos===i.nanos;case 5:return e.stringValue===t.stringValue;case 6:return e6(e.bytesValue).isEqual(e6(t.bytesValue));case 7:return e.referenceValue===t.referenceValue;case 8:return e8(e.geoPointValue.latitude)===e8(t.geoPointValue.latitude)&&e8(e.geoPointValue.longitude)===e8(t.geoPointValue.longitude);case 2:if("integerValue"in e&&"integerValue"in t)return e8(e.integerValue)===e8(t.integerValue);if("doubleValue"in e&&"doubleValue"in t){let n=e8(e.doubleValue),r=e8(t.doubleValue);return n===r?ev(n)===ev(r):isNaN(n)&&isNaN(r)}return!1;case 9:return P(e.arrayValue.values||[],t.arrayValue.values||[],ta);case 10:case 11:let s=e.mapValue.fields||{},a=t.mapValue.fields||{};if(eQ(s)!==eQ(a))return!1;for(let e in s)if(s.hasOwnProperty(e)&&(void 0===a[e]||!ta(s[e],a[e])))return!1;return!0;default:return b()}}function to(e,t){return void 0!==(e.values||[]).find(e=>ta(e,t))}function tl(e,t){if(e===t)return 0;let n=ts(e),r=ts(t);if(n!==r)return L(n,r);switch(n){case 0:case 0x1fffffffffffff:return 0;case 1:return L(e.booleanValue,t.booleanValue);case 2:let i=e8(e.integerValue||e.doubleValue),s=e8(t.integerValue||t.doubleValue);return i<s?-1:i>s?1:i===s?0:isNaN(i)?isNaN(s)?0:-1:1;case 3:return tu(e.timestampValue,t.timestampValue);case 4:return tu(te(e),te(t));case 5:return L(e.stringValue,t.stringValue);case 6:return function(e,t){let n=e6(e),r=e6(t);return n.compareTo(r)}(e.bytesValue,t.bytesValue);case 7:return function(e,t){let n=e.split("/"),r=t.split("/");for(let e=0;e<n.length&&e<r.length;e++){let t=L(n[e],r[e]);if(0!==t)return t}return L(n.length,r.length)}(e.referenceValue,t.referenceValue);case 8:return function(e,t){let n=L(e8(e.latitude),e8(t.latitude));return 0!==n?n:L(e8(e.longitude),e8(t.longitude))}(e.geoPointValue,t.geoPointValue);case 9:return th(e.arrayValue,t.arrayValue);case 10:return function(e,t){var n,r,i,s;let a=e.fields||{},o=t.fields||{},l=null==(n=a.value)?void 0:n.arrayValue,u=null==(r=o.value)?void 0:r.arrayValue,h=L((null==(i=null==l?void 0:l.values)?void 0:i.length)||0,(null==(s=null==u?void 0:u.values)?void 0:s.length)||0);return 0!==h?h:th(l,u)}(e.mapValue,t.mapValue);case 11:return function(e,t){if(e===tr.mapValue&&t===tr.mapValue)return 0;if(e===tr.mapValue)return 1;if(t===tr.mapValue)return -1;let n=e.fields||{},r=Object.keys(n),i=t.fields||{},s=Object.keys(i);r.sort(),s.sort();for(let e=0;e<r.length&&e<s.length;++e){let t=L(r[e],s[e]);if(0!==t)return t;let a=tl(n[r[e]],i[s[e]]);if(0!==a)return a}return L(r.length,s.length)}(e.mapValue,t.mapValue);default:throw b()}}function tu(e,t){if("string"==typeof e&&"string"==typeof t&&e.length===t.length)return L(e,t);let n=e4(e),r=e4(t),i=L(n.seconds,r.seconds);return 0!==i?i:L(n.nanos,r.nanos)}function th(e,t){let n=e.values||[],r=t.values||[];for(let e=0;e<n.length&&e<r.length;++e){let t=tl(n[e],r[e]);if(t)return t}return L(n.length,r.length)}function tc(e){var t,n;return"nullValue"in e?"null":"booleanValue"in e?""+e.booleanValue:"integerValue"in e?""+e.integerValue:"doubleValue"in e?""+e.doubleValue:"timestampValue"in e?function(e){let t=e4(e);return`time(${t.seconds},${t.nanos})`}(e.timestampValue):"stringValue"in e?e.stringValue:"bytesValue"in e?e6(e.bytesValue).toBase64():"referenceValue"in e?(t=e.referenceValue,$.fromName(t).toString()):"geoPointValue"in e?(n=e.geoPointValue,`geo(${n.latitude},${n.longitude})`):"arrayValue"in e?function(e){let t="[",n=!0;for(let r of e.values||[])n?n=!1:t+=",",t+=tc(r);return t+"]"}(e.arrayValue):"mapValue"in e?function(e){let t=Object.keys(e.fields||{}).sort(),n="{",r=!0;for(let i of t)r?r=!1:n+=",",n+=`${i}:${tc(e.fields[i])}`;return n+"}"}(e.mapValue):b()}function td(e,t){return{referenceValue:`projects/${e.projectId}/databases/${e.database}/documents/${t.path.canonicalString()}`}}function tf(e){return!!e&&"integerValue"in e}function tm(e){return!!e&&"arrayValue"in e}function tg(e){return!!e&&"nullValue"in e}function tp(e){return!!e&&"doubleValue"in e&&isNaN(Number(e.doubleValue))}function ty(e){return!!e&&"mapValue"in e}function tw(e){var t,n;return"__vector__"===(null==(n=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{}).__type__)?void 0:n.stringValue)}function tv(e){if(e.geoPointValue)return{geoPointValue:Object.assign({},e.geoPointValue)};if(e.timestampValue&&"object"==typeof e.timestampValue)return{timestampValue:Object.assign({},e.timestampValue)};if(e.mapValue){let t={mapValue:{fields:{}}};return ej(e.mapValue.fields,(e,n)=>t.mapValue.fields[e]=tv(n)),t}if(e.arrayValue){let t={arrayValue:{values:[]}};for(let n=0;n<(e.arrayValue.values||[]).length;++n)t.arrayValue.values[n]=tv(e.arrayValue.values[n]);return t}return Object.assign({},e)}function tI(e){return"__max__"===(((e.mapValue||{}).fields||{}).__type__||{}).stringValue}let tT={mapValue:{fields:{__type__:{stringValue:"__vector__"},value:{arrayValue:{}}}}};function t_(e,t){let n=tl(e.value,t.value);return 0!==n?n:e.inclusive&&!t.inclusive?-1:!e.inclusive&&t.inclusive?1:0}function tE(e,t){let n=tl(e.value,t.value);return 0!==n?n:e.inclusive&&!t.inclusive?1:!e.inclusive&&t.inclusive?-1:0}class tb{constructor(e){this.value=e}static empty(){return new tb({mapValue:{}})}field(e){if(e.isEmpty())return this.value;{let t=this.value;for(let n=0;n<e.length-1;++n)if(!ty(t=(t.mapValue.fields||{})[e.get(n)]))return null;return(t=(t.mapValue.fields||{})[e.lastSegment()])||null}}set(e,t){this.getFieldsMap(e.popLast())[e.lastSegment()]=tv(t)}setAll(e){let t=G.emptyPath(),n={},r=[];e.forEach((e,i)=>{if(!t.isImmediateParentOf(i)){let e=this.getFieldsMap(t);this.applyChanges(e,n,r),n={},r=[],t=i.popLast()}e?n[i.lastSegment()]=tv(e):r.push(i.lastSegment())});let i=this.getFieldsMap(t);this.applyChanges(i,n,r)}delete(e){let t=this.field(e.popLast());ty(t)&&t.mapValue.fields&&delete t.mapValue.fields[e.lastSegment()]}isEqual(e){return ta(this.value,e.value)}getFieldsMap(e){let t=this.value;t.mapValue.fields||(t.mapValue={fields:{}});for(let n=0;n<e.length;++n){let r=t.mapValue.fields[e.get(n)];ty(r)&&r.mapValue.fields||(r={mapValue:{fields:{}}},t.mapValue.fields[e.get(n)]=r),t=r}return t.mapValue.fields}applyChanges(e,t,n){for(let r of(ej(t,(t,n)=>e[t]=n),n))delete e[r]}clone(){return new tb(tv(this.value))}}class tS{constructor(e,t,n,r,i,s,a){this.key=e,this.documentType=t,this.version=n,this.readTime=r,this.createTime=i,this.data=s,this.documentState=a}static newInvalidDocument(e){return new tS(e,0,q.min(),q.min(),q.min(),tb.empty(),0)}static newFoundDocument(e,t,n,r){return new tS(e,1,t,q.min(),n,r,0)}static newNoDocument(e,t){return new tS(e,2,t,q.min(),q.min(),tb.empty(),0)}static newUnknownDocument(e,t){return new tS(e,3,t,q.min(),q.min(),tb.empty(),2)}convertToFoundDocument(e,t){return this.createTime.isEqual(q.min())&&(2===this.documentType||0===this.documentType)&&(this.createTime=e),this.version=e,this.documentType=1,this.data=t,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=tb.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=tb.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=q.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(e){return e instanceof tS&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new tS(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}class tx{constructor(e,t){this.position=e,this.inclusive=t}}function tD(e,t,n){let r=0;for(let i=0;i<e.position.length;i++){let s=t[i],a=e.position[i];if(r=s.field.isKeyField()?$.comparator($.fromName(a.referenceValue),n.key):tl(a,n.data.field(s.field)),"desc"===s.dir&&(r*=-1),0!==r)break}return r}function tC(e,t){if(null===e)return null===t;if(null===t||e.inclusive!==t.inclusive||e.position.length!==t.position.length)return!1;for(let n=0;n<e.position.length;n++)if(!ta(e.position[n],t.position[n]))return!1;return!0}class tN{constructor(e,t="asc"){this.field=e,this.dir=t}}class tA{}class tk extends tA{constructor(e,t,n){super(),this.field=e,this.op=t,this.value=n}static create(e,t,n){return e.isKeyField()?"in"===t||"not-in"===t?this.createKeyFieldInFilter(e,t,n):new tP(e,t,n):"array-contains"===t?new tK(e,n):"in"===t?new tz(e,n):"not-in"===t?new tG(e,n):"array-contains-any"===t?new t$(e,n):new tk(e,t,n)}static createKeyFieldInFilter(e,t,n){return"in"===t?new tU(e,n):new tq(e,n)}matches(e){let t=e.data.field(this.field);return"!="===this.op?null!==t&&this.matchesComparison(tl(t,this.value)):null!==t&&ts(this.value)===ts(t)&&this.matchesComparison(tl(t,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return 0===e;case"!=":return 0!==e;case">":return e>0;case">=":return e>=0;default:return b()}}isInequality(){return["<","<=",">",">=","!=","not-in"].indexOf(this.op)>=0}getFlattenedFilters(){return[this]}getFilters(){return[this]}}class tV extends tA{constructor(e,t){super(),this.filters=e,this.op=t,this.ae=null}static create(e,t){return new tV(e,t)}matches(e){return tR(this)?void 0===this.filters.find(t=>!t.matches(e)):void 0!==this.filters.find(t=>t.matches(e))}getFlattenedFilters(){return null!==this.ae||(this.ae=this.filters.reduce((e,t)=>e.concat(t.getFlattenedFilters()),[])),this.ae}getFilters(){return Object.assign([],this.filters)}}function tR(e){return"and"===e.op}function tO(e){return"or"===e.op}function tM(e){return tF(e)&&tR(e)}function tF(e){for(let t of e.filters)if(t instanceof tV)return!1;return!0}function tL(e,t){let n=e.filters.concat(t);return tV.create(n,e.op)}class tP extends tk{constructor(e,t,n){super(e,t,n),this.key=$.fromName(n.referenceValue)}matches(e){let t=$.comparator(e.key,this.key);return this.matchesComparison(t)}}class tU extends tk{constructor(e,t){super(e,"in",t),this.keys=tB("in",t)}matches(e){return this.keys.some(t=>t.isEqual(e.key))}}class tq extends tk{constructor(e,t){super(e,"not-in",t),this.keys=tB("not-in",t)}matches(e){return!this.keys.some(t=>t.isEqual(e.key))}}function tB(e,t){var n;return((null==(n=t.arrayValue)?void 0:n.values)||[]).map(e=>$.fromName(e.referenceValue))}class tK extends tk{constructor(e,t){super(e,"array-contains",t)}matches(e){let t=e.data.field(this.field);return tm(t)&&to(t.arrayValue,this.value)}}class tz extends tk{constructor(e,t){super(e,"in",t)}matches(e){let t=e.data.field(this.field);return null!==t&&to(this.value.arrayValue,t)}}class tG extends tk{constructor(e,t){super(e,"not-in",t)}matches(e){if(to(this.value.arrayValue,{nullValue:"NULL_VALUE"}))return!1;let t=e.data.field(this.field);return null!==t&&!to(this.value.arrayValue,t)}}class t$ extends tk{constructor(e,t){super(e,"array-contains-any",t)}matches(e){let t=e.data.field(this.field);return!(!tm(t)||!t.arrayValue.values)&&t.arrayValue.values.some(e=>to(this.value.arrayValue,e))}}class tQ{constructor(e,t=null,n=[],r=[],i=null,s=null,a=null){this.path=e,this.collectionGroup=t,this.orderBy=n,this.filters=r,this.limit=i,this.startAt=s,this.endAt=a,this.ue=null}}function tj(e,t=null,n=[],r=[],i=null,s=null,a=null){return new tQ(e,t,n,r,i,s,a)}function tW(e){if(null===e.ue){let t=e.path.canonicalString();null!==e.collectionGroup&&(t+="|cg:"+e.collectionGroup),t+="|f:",t+=e.filters.map(e=>(function e(t){if(t instanceof tk)return t.field.canonicalString()+t.op.toString()+tc(t.value);if(tM(t))return t.filters.map(t=>e(t)).join(",");{let n=t.filters.map(t=>e(t)).join(",");return`${t.op}(${n})`}})(e)).join(","),t+="|ob:",t+=e.orderBy.map(e=>e.field.canonicalString()+e.dir).join(","),ew(e.limit)||(t+="|l:",t+=e.limit),e.startAt&&(t+="|lb:",t+=e.startAt.inclusive?"b:":"a:",t+=e.startAt.position.map(e=>tc(e)).join(",")),e.endAt&&(t+="|ub:",t+=e.endAt.inclusive?"a:":"b:",t+=e.endAt.position.map(e=>tc(e)).join(",")),e.ue=t}return e.ue}function tH(e,t){if(e.limit!==t.limit||e.orderBy.length!==t.orderBy.length)return!1;for(let i=0;i<e.orderBy.length;i++){var n,r;if(n=e.orderBy[i],r=t.orderBy[i],!(n.dir===r.dir&&n.field.isEqual(r.field)))return!1}if(e.filters.length!==t.filters.length)return!1;for(let n=0;n<e.filters.length;n++)if(!function e(t,n){return t instanceof tk?n instanceof tk&&t.op===n.op&&t.field.isEqual(n.field)&&ta(t.value,n.value):t instanceof tV?n instanceof tV&&t.op===n.op&&t.filters.length===n.filters.length&&t.filters.reduce((t,r,i)=>t&&e(r,n.filters[i]),!0):void b()}(e.filters[n],t.filters[n]))return!1;return e.collectionGroup===t.collectionGroup&&!!e.path.isEqual(t.path)&&!!tC(e.startAt,t.startAt)&&tC(e.endAt,t.endAt)}function tJ(e){return $.isDocumentKey(e.path)&&null===e.collectionGroup&&0===e.filters.length}function tY(e,t){return e.filters.filter(e=>e instanceof tk&&e.field.isEqual(t))}function tX(e,t,n){let r=ti,i=!0;for(let n of tY(e,t)){let e=ti,t=!0;switch(n.op){case"<":case"<=":var s;e="nullValue"in(s=n.value)?ti:"booleanValue"in s?{booleanValue:!1}:"integerValue"in s||"doubleValue"in s?{doubleValue:NaN}:"timestampValue"in s?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"stringValue"in s?{stringValue:""}:"bytesValue"in s?{bytesValue:""}:"referenceValue"in s?td(tn.empty(),$.empty()):"geoPointValue"in s?{geoPointValue:{latitude:-90,longitude:-180}}:"arrayValue"in s?{arrayValue:{}}:"mapValue"in s?tw(s)?tT:{mapValue:{}}:b();break;case"==":case"in":case">=":e=n.value;break;case">":e=n.value,t=!1;break;case"!=":case"not-in":e=ti}0>t_({value:r,inclusive:i},{value:e,inclusive:t})&&(r=e,i=t)}if(null!==n){for(let s=0;s<e.orderBy.length;++s)if(e.orderBy[s].field.isEqual(t)){let e=n.position[s];0>t_({value:r,inclusive:i},{value:e,inclusive:n.inclusive})&&(r=e,i=n.inclusive);break}}return{value:r,inclusive:i}}function tZ(e,t,n){let r=tr,i=!0;for(let n of tY(e,t)){let e=tr,t=!0;switch(n.op){case">=":case">":var s;e="nullValue"in(s=n.value)?{booleanValue:!1}:"booleanValue"in s?{doubleValue:NaN}:"integerValue"in s||"doubleValue"in s?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"timestampValue"in s?{stringValue:""}:"stringValue"in s?{bytesValue:""}:"bytesValue"in s?td(tn.empty(),$.empty()):"referenceValue"in s?{geoPointValue:{latitude:-90,longitude:-180}}:"geoPointValue"in s?{arrayValue:{}}:"arrayValue"in s?tT:"mapValue"in s?tw(s)?{mapValue:{}}:tr:b(),t=!1;break;case"==":case"in":case"<=":e=n.value;break;case"<":e=n.value,t=!1;break;case"!=":case"not-in":e=tr}tE({value:r,inclusive:i},{value:e,inclusive:t})>0&&(r=e,i=t)}if(null!==n){for(let s=0;s<e.orderBy.length;++s)if(e.orderBy[s].field.isEqual(t)){let e=n.position[s];tE({value:r,inclusive:i},{value:e,inclusive:n.inclusive})>0&&(r=e,i=n.inclusive);break}}return{value:r,inclusive:i}}class t0{constructor(e,t=null,n=[],r=[],i=null,s="F",a=null,o=null){this.path=e,this.collectionGroup=t,this.explicitOrderBy=n,this.filters=r,this.limit=i,this.limitType=s,this.startAt=a,this.endAt=o,this.ce=null,this.le=null,this.he=null,this.startAt,this.endAt}}function t1(e){return new t0(e)}function t2(e){return 0===e.filters.length&&null===e.limit&&null==e.startAt&&null==e.endAt&&(0===e.explicitOrderBy.length||1===e.explicitOrderBy.length&&e.explicitOrderBy[0].field.isKeyField())}function t5(e){return null!==e.collectionGroup}function t3(e){if(null===e.ce){let t;e.ce=[];let n=new Set;for(let t of e.explicitOrderBy)e.ce.push(t),n.add(t.field.canonicalString());let r=e.explicitOrderBy.length>0?e.explicitOrderBy[e.explicitOrderBy.length-1].dir:"asc";(t=new eX(G.comparator),e.filters.forEach(e=>{e.getFlattenedFilters().forEach(e=>{e.isInequality()&&(t=t.add(e.field))})}),t).forEach(t=>{n.has(t.canonicalString())||t.isKeyField()||e.ce.push(new tN(t,r))}),n.has(G.keyField().canonicalString())||e.ce.push(new tN(G.keyField(),r))}return e.ce}function t4(e){return e.le||(e.le=t8(e,t3(e))),e.le}function t8(e,t){if("F"===e.limitType)return tj(e.path,e.collectionGroup,t,e.filters,e.limit,e.startAt,e.endAt);{t=t.map(e=>{let t="desc"===e.dir?"asc":"desc";return new tN(e.field,t)});let n=e.endAt?new tx(e.endAt.position,e.endAt.inclusive):null,r=e.startAt?new tx(e.startAt.position,e.startAt.inclusive):null;return tj(e.path,e.collectionGroup,t,e.filters,e.limit,n,r)}}function t6(e,t){let n=e.filters.concat([t]);return new t0(e.path,e.collectionGroup,e.explicitOrderBy.slice(),n,e.limit,e.limitType,e.startAt,e.endAt)}function t9(e,t,n){return new t0(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),t,n,e.startAt,e.endAt)}function t7(e,t){return tH(t4(e),t4(t))&&e.limitType===t.limitType}function ne(e){return`${tW(t4(e))}|lt:${e.limitType}`}function nt(e){var t;let n;return`Query(target=${n=(t=t4(e)).path.canonicalString(),null!==t.collectionGroup&&(n+=" collectionGroup="+t.collectionGroup),t.filters.length>0&&(n+=`, filters: [${t.filters.map(e=>(function e(t){return t instanceof tk?`${t.field.canonicalString()} ${t.op} ${tc(t.value)}`:t instanceof tV?t.op.toString()+" {"+t.getFilters().map(e).join(" ,")+"}":"Filter"})(e)).join(", ")}]`),ew(t.limit)||(n+=", limit: "+t.limit),t.orderBy.length>0&&(n+=`, orderBy: [${t.orderBy.map(e=>`${e.field.canonicalString()} (${e.dir})`).join(", ")}]`),t.startAt&&(n+=", startAt: ",n+=t.startAt.inclusive?"b:":"a:",n+=t.startAt.position.map(e=>tc(e)).join(",")),t.endAt&&(n+=", endAt: ",n+=t.endAt.inclusive?"a:":"b:",n+=t.endAt.position.map(e=>tc(e)).join(",")),`Target(${n})`}; limitType=${e.limitType})`}function nn(e,t){return t.isFoundDocument()&&function(e,t){let n=t.key.path;return null!==e.collectionGroup?t.key.hasCollectionId(e.collectionGroup)&&e.path.isPrefixOf(n):$.isDocumentKey(e.path)?e.path.isEqual(n):e.path.isImmediateParentOf(n)}(e,t)&&function(e,t){for(let n of t3(e))if(!n.field.isKeyField()&&null===t.data.field(n.field))return!1;return!0}(e,t)&&function(e,t){for(let n of e.filters)if(!n.matches(t))return!1;return!0}(e,t)&&(!e.startAt||!!function(e,t,n){let r=tD(e,t,n);return e.inclusive?r<=0:r<0}(e.startAt,t3(e),t))&&(!e.endAt||!!function(e,t,n){let r=tD(e,t,n);return e.inclusive?r>=0:r>0}(e.endAt,t3(e),t))}function nr(e){return e.collectionGroup||(e.path.length%2==1?e.path.lastSegment():e.path.get(e.path.length-2))}function ni(e){return(t,n)=>{let r=!1;for(let i of t3(e)){let e=function(e,t,n){let r=e.field.isKeyField()?$.comparator(t.key,n.key):function(e,t,n){let r=t.data.field(e),i=n.data.field(e);return null!==r&&null!==i?tl(r,i):b()}(e.field,t,n);switch(e.dir){case"asc":return r;case"desc":return -1*r;default:return b()}}(i,t,n);if(0!==e)return e;r=r||i.field.isKeyField()}return 0}}class ns{constructor(e,t){this.mapKeyFn=e,this.equalsFn=t,this.inner={},this.innerSize=0}get(e){let t=this.mapKeyFn(e),n=this.inner[t];if(void 0!==n){for(let[t,r]of n)if(this.equalsFn(t,e))return r}}has(e){return void 0!==this.get(e)}set(e,t){let n=this.mapKeyFn(e),r=this.inner[n];if(void 0===r)return this.inner[n]=[[e,t]],void this.innerSize++;for(let n=0;n<r.length;n++)if(this.equalsFn(r[n][0],e))return void(r[n]=[e,t]);r.push([e,t]),this.innerSize++}delete(e){let t=this.mapKeyFn(e),n=this.inner[t];if(void 0===n)return!1;for(let r=0;r<n.length;r++)if(this.equalsFn(n[r][0],e))return 1===n.length?delete this.inner[t]:n.splice(r,1),this.innerSize--,!0;return!1}forEach(e){ej(this.inner,(t,n)=>{for(let[t,r]of n)e(t,r)})}isEmpty(){return eW(this.inner)}size(){return this.innerSize}}let na=new eH($.comparator),no=new eH($.comparator);function nl(...e){let t=no;for(let n of e)t=t.insert(n.key,n);return t}function nu(e){let t=no;return e.forEach((e,n)=>t=t.insert(e,n.overlayedDocument)),t}function nh(){return new ns(e=>e.toString(),(e,t)=>e.isEqual(t))}let nc=new eH($.comparator),nd=new eX($.comparator);function nf(...e){let t=nd;for(let n of e)t=t.add(n);return t}let nm=new eX(L);function ng(e,t){if(e.useProto3Json){if(isNaN(t))return{doubleValue:"NaN"};if(t===1/0)return{doubleValue:"Infinity"};if(t===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:ev(t)?"-0":t}}function np(e){return{integerValue:""+e}}function ny(e,t){return eI(t)?np(t):ng(e,t)}class nw{constructor(){this._=void 0}}function nv(e,t){return e instanceof nS?tf(t)||t&&"doubleValue"in t?t:{integerValue:0}:null}class nI extends nw{}class nT extends nw{constructor(e){super(),this.elements=e}}function n_(e,t){let n=nD(t);for(let t of e.elements)n.some(e=>ta(e,t))||n.push(t);return{arrayValue:{values:n}}}class nE extends nw{constructor(e){super(),this.elements=e}}function nb(e,t){let n=nD(t);for(let t of e.elements)n=n.filter(e=>!ta(e,t));return{arrayValue:{values:n}}}class nS extends nw{constructor(e,t){super(),this.serializer=e,this.Pe=t}}function nx(e){return e8(e.integerValue||e.doubleValue)}function nD(e){return tm(e)&&e.arrayValue.values?e.arrayValue.values.slice():[]}class nC{constructor(e,t){this.field=e,this.transform=t}}class nN{constructor(e,t){this.version=e,this.transformResults=t}}class nA{constructor(e,t){this.updateTime=e,this.exists=t}static none(){return new nA}static exists(e){return new nA(void 0,e)}static updateTime(e){return new nA(e)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}}function nk(e,t){return void 0!==e.updateTime?t.isFoundDocument()&&t.version.isEqual(e.updateTime):void 0===e.exists||e.exists===t.isFoundDocument()}class nV{}function nR(e,t){if(!e.hasLocalMutations||t&&0===t.fields.length)return null;if(null===t)return e.isNoDocument()?new nB(e.key,nA.none()):new nF(e.key,e.data,nA.none());{let n=e.data,r=tb.empty(),i=new eX(G.comparator);for(let e of t.fields)if(!i.has(e)){let t=n.field(e);null===t&&e.length>1&&(e=e.popLast(),t=n.field(e)),null===t?r.delete(e):r.set(e,t),i=i.add(e)}return new nL(e.key,r,new e1(i.toArray()),nA.none())}}function nO(e,t,n,r){return e instanceof nF?function(e,t,n,r){if(!nk(e.precondition,t))return n;let i=e.value.clone(),s=nq(e.fieldTransforms,r,t);return i.setAll(s),t.convertToFoundDocument(t.version,i).setHasLocalMutations(),null}(e,t,n,r):e instanceof nL?function(e,t,n,r){if(!nk(e.precondition,t))return n;let i=nq(e.fieldTransforms,r,t),s=t.data;return(s.setAll(nP(e)),s.setAll(i),t.convertToFoundDocument(t.version,s).setHasLocalMutations(),null===n)?null:n.unionWith(e.fieldMask.fields).unionWith(e.fieldTransforms.map(e=>e.field))}(e,t,n,r):nk(e.precondition,t)?(t.convertToNoDocument(t.version).setHasLocalMutations(),null):n}function nM(e,t){var n,r;return e.type===t.type&&!!e.key.isEqual(t.key)&&!!e.precondition.isEqual(t.precondition)&&(n=e.fieldTransforms,r=t.fieldTransforms,!!(void 0===n&&void 0===r||!(!n||!r)&&P(n,r,(e,t)=>{var n,r;return e.field.isEqual(t.field)&&(n=e.transform,r=t.transform,n instanceof nT&&r instanceof nT||n instanceof nE&&r instanceof nE?P(n.elements,r.elements,ta):n instanceof nS&&r instanceof nS?ta(n.Pe,r.Pe):n instanceof nI&&r instanceof nI)})))&&(0===e.type?e.value.isEqual(t.value):1!==e.type||e.data.isEqual(t.data)&&e.fieldMask.isEqual(t.fieldMask))}class nF extends nV{constructor(e,t,n,r=[]){super(),this.key=e,this.value=t,this.precondition=n,this.fieldTransforms=r,this.type=0}getFieldMask(){return null}}class nL extends nV{constructor(e,t,n,r,i=[]){super(),this.key=e,this.data=t,this.fieldMask=n,this.precondition=r,this.fieldTransforms=i,this.type=1}getFieldMask(){return this.fieldMask}}function nP(e){let t=new Map;return e.fieldMask.fields.forEach(n=>{if(!n.isEmpty()){let r=e.data.field(n);t.set(n,r)}}),t}function nU(e,t,n){var r;let i=new Map;e.length===n.length||b();for(let s=0;s<n.length;s++){let a=e[s],o=a.transform,l=t.data.field(a.field);i.set(a.field,(r=n[s],o instanceof nT?n_(o,l):o instanceof nE?nb(o,l):r))}return i}function nq(e,t,n){let r=new Map;for(let i of e){let e=i.transform,s=n.data.field(i.field);r.set(i.field,e instanceof nI?function(e,t){let n={fields:{__type__:{stringValue:"server_timestamp"},__local_write_time__:{timestampValue:{seconds:e.seconds,nanos:e.nanoseconds}}}};return t&&e9(t)&&(t=e7(t)),t&&(n.fields.__previous_value__=t),{mapValue:n}}(t,s):e instanceof nT?n_(e,s):e instanceof nE?nb(e,s):function(e,t){let n=nv(e,t),r=nx(n)+nx(e.Pe);return tf(n)&&tf(e.Pe)?np(r):ng(e.serializer,r)}(e,s))}return r}class nB extends nV{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class nK extends nV{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}}class nz{constructor(e,t,n,r){this.batchId=e,this.localWriteTime=t,this.baseMutations=n,this.mutations=r}applyToRemoteDocument(e,t){let n=t.mutationResults;for(let t=0;t<this.mutations.length;t++){let r=this.mutations[t];r.key.isEqual(e.key)&&function(e,t,n){e instanceof nF?function(e,t,n){let r=e.value.clone(),i=nU(e.fieldTransforms,t,n.transformResults);r.setAll(i),t.convertToFoundDocument(n.version,r).setHasCommittedMutations()}(e,t,n):e instanceof nL?function(e,t,n){if(!nk(e.precondition,t))return t.convertToUnknownDocument(n.version);let r=nU(e.fieldTransforms,t,n.transformResults),i=t.data;i.setAll(nP(e)),i.setAll(r),t.convertToFoundDocument(n.version,i).setHasCommittedMutations()}(e,t,n):t.convertToNoDocument(n.version).setHasCommittedMutations()}(r,e,n[t])}}applyToLocalView(e,t){for(let n of this.baseMutations)n.key.isEqual(e.key)&&(t=nO(n,e,t,this.localWriteTime));for(let n of this.mutations)n.key.isEqual(e.key)&&(t=nO(n,e,t,this.localWriteTime));return t}applyToLocalDocumentSet(e,t){let n=nh();return this.mutations.forEach(r=>{let i=e.get(r.key),s=i.overlayedDocument,a=this.applyToLocalView(s,i.mutatedFields),o=nR(s,a=t.has(r.key)?null:a);null!==o&&n.set(r.key,o),s.isValidDocument()||s.convertToNoDocument(q.min())}),n}keys(){return this.mutations.reduce((e,t)=>e.add(t.key),nf())}isEqual(e){return this.batchId===e.batchId&&P(this.mutations,e.mutations,(e,t)=>nM(e,t))&&P(this.baseMutations,e.baseMutations,(e,t)=>nM(e,t))}}class nG{constructor(e,t,n,r){this.batch=e,this.commitVersion=t,this.mutationResults=n,this.docVersions=r}static from(e,t,n){e.mutations.length===n.length||b();let r=nc,i=e.mutations;for(let e=0;e<i.length;e++)r=r.insert(i[e].key,n[e].version);return new nG(e,t,n,r)}}class n${constructor(e,t){this.largestBatchId=e,this.mutation=t}getKey(){return this.mutation.key}isEqual(e){return null!==e&&this.mutation===e.mutation}toString(){return`Overlay{
      largestBatchId: ${this.largestBatchId},
      mutation: ${this.mutation.toString()}
    }`}}class nQ{constructor(e,t,n){this.alias=e,this.aggregateType=t,this.fieldPath=n}}class nj{constructor(e,t){this.count=e,this.unchangedNames=t}}function nW(e){switch(e){default:return b();case S.CANCELLED:case S.UNKNOWN:case S.DEADLINE_EXCEEDED:case S.RESOURCE_EXHAUSTED:case S.INTERNAL:case S.UNAVAILABLE:case S.UNAUTHENTICATED:return!1;case S.INVALID_ARGUMENT:case S.NOT_FOUND:case S.ALREADY_EXISTS:case S.PERMISSION_DENIED:case S.FAILED_PRECONDITION:case S.ABORTED:case S.OUT_OF_RANGE:case S.UNIMPLEMENTED:case S.DATA_LOSS:return!0}}function nH(e){if(void 0===e)return T("GRPC error has no .code"),S.UNKNOWN;switch(e){case r.OK:return S.OK;case r.CANCELLED:return S.CANCELLED;case r.UNKNOWN:return S.UNKNOWN;case r.DEADLINE_EXCEEDED:return S.DEADLINE_EXCEEDED;case r.RESOURCE_EXHAUSTED:return S.RESOURCE_EXHAUSTED;case r.INTERNAL:return S.INTERNAL;case r.UNAVAILABLE:return S.UNAVAILABLE;case r.UNAUTHENTICATED:return S.UNAUTHENTICATED;case r.INVALID_ARGUMENT:return S.INVALID_ARGUMENT;case r.NOT_FOUND:return S.NOT_FOUND;case r.ALREADY_EXISTS:return S.ALREADY_EXISTS;case r.PERMISSION_DENIED:return S.PERMISSION_DENIED;case r.FAILED_PRECONDITION:return S.FAILED_PRECONDITION;case r.ABORTED:return S.ABORTED;case r.OUT_OF_RANGE:return S.OUT_OF_RANGE;case r.UNIMPLEMENTED:return S.UNIMPLEMENTED;case r.DATA_LOSS:return S.DATA_LOSS;default:return b()}}(i=r||(r={}))[i.OK=0]="OK",i[i.CANCELLED=1]="CANCELLED",i[i.UNKNOWN=2]="UNKNOWN",i[i.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",i[i.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",i[i.NOT_FOUND=5]="NOT_FOUND",i[i.ALREADY_EXISTS=6]="ALREADY_EXISTS",i[i.PERMISSION_DENIED=7]="PERMISSION_DENIED",i[i.UNAUTHENTICATED=16]="UNAUTHENTICATED",i[i.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",i[i.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",i[i.ABORTED=10]="ABORTED",i[i.OUT_OF_RANGE=11]="OUT_OF_RANGE",i[i.UNIMPLEMENTED=12]="UNIMPLEMENTED",i[i.INTERNAL=13]="INTERNAL",i[i.UNAVAILABLE=14]="UNAVAILABLE",i[i.DATA_LOSS=15]="DATA_LOSS";let nJ=new c.jz([0xffffffff,0xffffffff],0);function nY(e){let t=(new TextEncoder).encode(e),n=new c.VV;return n.update(t),new Uint8Array(n.digest())}function nX(e){let t=new DataView(e.buffer),n=t.getUint32(0,!0),r=t.getUint32(4,!0),i=t.getUint32(8,!0),s=t.getUint32(12,!0);return[new c.jz([n,r],0),new c.jz([i,s],0)]}class nZ{constructor(e,t,n){if(this.bitmap=e,this.padding=t,this.hashCount=n,t<0||t>=8)throw new n0(`Invalid padding: ${t}`);if(n<0||e.length>0&&0===this.hashCount)throw new n0(`Invalid hash count: ${n}`);if(0===e.length&&0!==t)throw new n0(`Invalid padding when bitmap length is 0: ${t}`);this.Ie=8*e.length-t,this.Te=c.jz.fromNumber(this.Ie)}Ee(e,t,n){let r=e.add(t.multiply(c.jz.fromNumber(n)));return 1===r.compare(nJ)&&(r=new c.jz([r.getBits(0),r.getBits(1)],0)),r.modulo(this.Te).toNumber()}de(e){return 0!=(this.bitmap[Math.floor(e/8)]&1<<e%8)}mightContain(e){if(0===this.Ie)return!1;let[t,n]=nX(nY(e));for(let e=0;e<this.hashCount;e++){let r=this.Ee(t,n,e);if(!this.de(r))return!1}return!0}static create(e,t,n){let r=new nZ(new Uint8Array(Math.ceil(e/8)),e%8==0?0:8-e%8,t);return n.forEach(e=>r.insert(e)),r}insert(e){if(0===this.Ie)return;let[t,n]=nX(nY(e));for(let e=0;e<this.hashCount;e++){let r=this.Ee(t,n,e);this.Ae(r)}}Ae(e){let t=Math.floor(e/8);this.bitmap[t]|=1<<e%8}}class n0 extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}}class n1{constructor(e,t,n,r,i){this.snapshotVersion=e,this.targetChanges=t,this.targetMismatches=n,this.documentUpdates=r,this.resolvedLimboDocuments=i}static createSynthesizedRemoteEventForCurrentChange(e,t,n){let r=new Map;return r.set(e,n2.createSynthesizedTargetChangeForCurrentChange(e,t,n)),new n1(q.min(),r,new eH(L),na,nf())}}class n2{constructor(e,t,n,r,i){this.resumeToken=e,this.current=t,this.addedDocuments=n,this.modifiedDocuments=r,this.removedDocuments=i}static createSynthesizedTargetChangeForCurrentChange(e,t,n){return new n2(n,t,nf(),nf(),nf())}}class n5{constructor(e,t,n,r){this.Re=e,this.removedTargetIds=t,this.key=n,this.Ve=r}}class n3{constructor(e,t){this.targetId=e,this.me=t}}class n4{constructor(e,t,n=e5.EMPTY_BYTE_STRING,r=null){this.state=e,this.targetIds=t,this.resumeToken=n,this.cause=r}}class n8{constructor(){this.fe=0,this.ge=n7(),this.pe=e5.EMPTY_BYTE_STRING,this.ye=!1,this.we=!0}get current(){return this.ye}get resumeToken(){return this.pe}get Se(){return 0!==this.fe}get be(){return this.we}De(e){e.approximateByteSize()>0&&(this.we=!0,this.pe=e)}ve(){let e=nf(),t=nf(),n=nf();return this.ge.forEach((r,i)=>{switch(i){case 0:e=e.add(r);break;case 2:t=t.add(r);break;case 1:n=n.add(r);break;default:b()}}),new n2(this.pe,this.ye,e,t,n)}Ce(){this.we=!1,this.ge=n7()}Fe(e,t){this.we=!0,this.ge=this.ge.insert(e,t)}Me(e){this.we=!0,this.ge=this.ge.remove(e)}xe(){this.fe+=1}Oe(){this.fe-=1,this.fe>=0||b()}Ne(){this.we=!0,this.ye=!0}}class n6{constructor(e){this.Le=e,this.Be=new Map,this.ke=na,this.qe=n9(),this.Qe=new eH(L)}Ke(e){for(let t of e.Re)e.Ve&&e.Ve.isFoundDocument()?this.$e(t,e.Ve):this.Ue(t,e.key,e.Ve);for(let t of e.removedTargetIds)this.Ue(t,e.key,e.Ve)}We(e){this.forEachTarget(e,t=>{let n=this.Ge(t);switch(e.state){case 0:this.ze(t)&&n.De(e.resumeToken);break;case 1:n.Oe(),n.Se||n.Ce(),n.De(e.resumeToken);break;case 2:n.Oe(),n.Se||this.removeTarget(t);break;case 3:this.ze(t)&&(n.Ne(),n.De(e.resumeToken));break;case 4:this.ze(t)&&(this.je(t),n.De(e.resumeToken));break;default:b()}})}forEachTarget(e,t){e.targetIds.length>0?e.targetIds.forEach(t):this.Be.forEach((e,n)=>{this.ze(n)&&t(n)})}He(e){let t=e.targetId,n=e.me.count,r=this.Je(t);if(r){let i=r.target;if(tJ(i))if(0===n){let e=new $(i.path);this.Ue(t,e,tS.newNoDocument(e,q.min()))}else 1===n||b();else{let r=this.Ye(t);if(r!==n){let n=this.Ze(e),i=n?this.Xe(n,e,r):1;0!==i&&(this.je(t),this.Qe=this.Qe.insert(t,2===i?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch"))}}}}Ze(e){let t,n,r=e.me.unchangedNames;if(!r||!r.bits)return null;let{bits:{bitmap:i="",padding:s=0},hashCount:a=0}=r;try{t=e6(i).toUint8Array()}catch(e){if(e instanceof e2)return _("Decoding the base64 bloom filter in existence filter failed ("+e.message+"); ignoring the bloom filter and falling back to full re-query."),null;throw e}try{n=new nZ(t,s,a)}catch(e){return _(e instanceof n0?"BloomFilter error: ":"Applying bloom filter failed: ",e),null}return 0===n.Ie?null:n}Xe(e,t,n){return 2*(t.me.count!==n-this.nt(e,t.targetId))}nt(e,t){let n=this.Le.getRemoteKeysForTarget(t),r=0;return n.forEach(n=>{let i=this.Le.tt(),s=`projects/${i.projectId}/databases/${i.database}/documents/${n.path.canonicalString()}`;e.mightContain(s)||(this.Ue(t,n,null),r++)}),r}rt(e){let t=new Map;this.Be.forEach((n,r)=>{let i=this.Je(r);if(i){if(n.current&&tJ(i.target)){let t=new $(i.target.path);null!==this.ke.get(t)||this.it(r,t)||this.Ue(r,t,tS.newNoDocument(t,e))}n.be&&(t.set(r,n.ve()),n.Ce())}});let n=nf();this.qe.forEach((e,t)=>{let r=!0;t.forEachWhile(e=>{let t=this.Je(e);return!t||"TargetPurposeLimboResolution"===t.purpose||(r=!1,!1)}),r&&(n=n.add(e))}),this.ke.forEach((t,n)=>n.setReadTime(e));let r=new n1(e,t,this.Qe,this.ke,n);return this.ke=na,this.qe=n9(),this.Qe=new eH(L),r}$e(e,t){if(!this.ze(e))return;let n=2*!!this.it(e,t.key);this.Ge(e).Fe(t.key,n),this.ke=this.ke.insert(t.key,t),this.qe=this.qe.insert(t.key,this.st(t.key).add(e))}Ue(e,t,n){if(!this.ze(e))return;let r=this.Ge(e);this.it(e,t)?r.Fe(t,1):r.Me(t),this.qe=this.qe.insert(t,this.st(t).delete(e)),n&&(this.ke=this.ke.insert(t,n))}removeTarget(e){this.Be.delete(e)}Ye(e){let t=this.Ge(e).ve();return this.Le.getRemoteKeysForTarget(e).size+t.addedDocuments.size-t.removedDocuments.size}xe(e){this.Ge(e).xe()}Ge(e){let t=this.Be.get(e);return t||(t=new n8,this.Be.set(e,t)),t}st(e){let t=this.qe.get(e);return t||(t=new eX(L),this.qe=this.qe.insert(e,t)),t}ze(e){let t=null!==this.Je(e);return t||I("WatchChangeAggregator","Detected inactive target",e),t}Je(e){let t=this.Be.get(e);return t&&t.Se?null:this.Le.ot(e)}je(e){this.Be.set(e,new n8),this.Le.getRemoteKeysForTarget(e).forEach(t=>{this.Ue(e,t,null)})}it(e,t){return this.Le.getRemoteKeysForTarget(e).has(t)}}function n9(){return new eH($.comparator)}function n7(){return new eH($.comparator)}let re={asc:"ASCENDING",desc:"DESCENDING"},rt={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},rn={and:"AND",or:"OR"};class rr{constructor(e,t){this.databaseId=e,this.useProto3Json=t}}function ri(e,t){return e.useProto3Json||ew(t)?t:{value:t}}function rs(e,t){return e.useProto3Json?`${new Date(1e3*t.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+t.nanoseconds).slice(-9)}Z`:{seconds:""+t.seconds,nanos:t.nanoseconds}}function ra(e,t){return e.useProto3Json?t.toBase64():t.toUint8Array()}function ro(e){return e||b(),q.fromTimestamp(function(e){let t=e4(e);return new U(t.seconds,t.nanos)}(e))}function rl(e,t){return ru(e,t).canonicalString()}function ru(e,t){let n=new K(["projects",e.projectId,"databases",e.database]).child("documents");return void 0===t?n:n.child(t)}function rh(e){let t=K.fromString(e);return rx(t)||b(),t}function rc(e,t){return rl(e.databaseId,t.path)}function rd(e,t){let n=rh(t);if(n.get(1)!==e.databaseId.projectId)throw new x(S.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+n.get(1)+" vs "+e.databaseId.projectId);if(n.get(3)!==e.databaseId.database)throw new x(S.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+n.get(3)+" vs "+e.databaseId.database);return new $(rp(n))}function rf(e,t){return rl(e.databaseId,t)}function rm(e){let t=rh(e);return 4===t.length?K.emptyPath():rp(t)}function rg(e){return new K(["projects",e.databaseId.projectId,"databases",e.databaseId.database]).canonicalString()}function rp(e){return e.length>4&&"documents"===e.get(4)||b(),e.popFirst(5)}function ry(e,t,n){return{name:rc(e,t),fields:n.value.mapValue.fields}}function rw(e,t,n){let r=rd(e,t.name),i=ro(t.updateTime),s=t.createTime?ro(t.createTime):q.min(),a=new tb({mapValue:{fields:t.fields}}),o=tS.newFoundDocument(r,i,s,a);return n&&o.setHasCommittedMutations(),n?o.setHasCommittedMutations():o}function rv(e,t){var n;let r;if(t instanceof nF)r={update:ry(e,t.key,t.value)};else if(t instanceof nB)r={delete:rc(e,t.key)};else if(t instanceof nL)r={update:ry(e,t.key,t.data),updateMask:function(e){let t=[];return e.fields.forEach(e=>t.push(e.canonicalString())),{fieldPaths:t}}(t.fieldMask)};else{if(!(t instanceof nK))return b();r={verify:rc(e,t.key)}}return t.fieldTransforms.length>0&&(r.updateTransforms=t.fieldTransforms.map(e=>(function(e,t){let n=t.transform;if(n instanceof nI)return{fieldPath:t.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(n instanceof nT)return{fieldPath:t.field.canonicalString(),appendMissingElements:{values:n.elements}};if(n instanceof nE)return{fieldPath:t.field.canonicalString(),removeAllFromArray:{values:n.elements}};if(n instanceof nS)return{fieldPath:t.field.canonicalString(),increment:n.Pe};throw b()})(0,e))),t.precondition.isNone||(r.currentDocument=void 0!==(n=t.precondition).updateTime?{updateTime:rs(e,n.updateTime.toTimestamp())}:void 0!==n.exists?{exists:n.exists}:b()),r}function rI(e,t){var n;let r=t.currentDocument?void 0!==(n=t.currentDocument).updateTime?nA.updateTime(ro(n.updateTime)):void 0!==n.exists?nA.exists(n.exists):nA.none():nA.none(),i=t.updateTransforms?t.updateTransforms.map(t=>{var n,r;let i;return n=e,i=null,"setToServerValue"in(r=t)?("REQUEST_TIME"===r.setToServerValue||b(),i=new nI):"appendMissingElements"in r?i=new nT(r.appendMissingElements.values||[]):"removeAllFromArray"in r?i=new nE(r.removeAllFromArray.values||[]):"increment"in r?i=new nS(n,r.increment):b(),new nC(G.fromServerFormat(r.fieldPath),i)}):[];if(t.update){t.update.name;let n=rd(e,t.update.name),s=new tb({mapValue:{fields:t.update.fields}});return t.updateMask?new nL(n,s,new e1((t.updateMask.fieldPaths||[]).map(e=>G.fromServerFormat(e))),r,i):new nF(n,s,r,i)}return t.delete?new nB(rd(e,t.delete),r):t.verify?new nK(rd(e,t.verify),r):b()}function rT(e,t){return{documents:[rf(e,t.path)]}}function r_(e,t){var n,r;let i,s={structuredQuery:{}},a=t.path;null!==t.collectionGroup?(i=a,s.structuredQuery.from=[{collectionId:t.collectionGroup,allDescendants:!0}]):(i=a.popLast(),s.structuredQuery.from=[{collectionId:a.lastSegment()}]),s.parent=rf(e,i);let o=function(e){if(0!==e.length)return function e(t){return t instanceof tk?function(e){if("=="===e.op){if(tp(e.value))return{unaryFilter:{field:rb(e.field),op:"IS_NAN"}};if(tg(e.value))return{unaryFilter:{field:rb(e.field),op:"IS_NULL"}}}else if("!="===e.op){if(tp(e.value))return{unaryFilter:{field:rb(e.field),op:"IS_NOT_NAN"}};if(tg(e.value))return{unaryFilter:{field:rb(e.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:rb(e.field),op:rt[e.op],value:e.value}}}(t):t instanceof tV?function(t){let n=t.getFilters().map(t=>e(t));return 1===n.length?n[0]:{compositeFilter:{op:rn[t.op],filters:n}}}(t):b()}(tV.create(e,"and"))}(t.filters);o&&(s.structuredQuery.where=o);let l=function(e){if(0!==e.length)return e.map(e=>({field:rb(e.field),direction:re[e.dir]}))}(t.orderBy);l&&(s.structuredQuery.orderBy=l);let u=ri(e,t.limit);return null!==u&&(s.structuredQuery.limit=u),t.startAt&&(s.structuredQuery.startAt={before:(n=t.startAt).inclusive,values:n.position}),t.endAt&&(s.structuredQuery.endAt={before:!(r=t.endAt).inclusive,values:r.position}),{_t:s,parent:i}}function rE(e){var t;let n,r=rm(e.parent),i=e.structuredQuery,s=i.from?i.from.length:0,a=null;if(s>0){1===s||b();let e=i.from[0];e.allDescendants?a=e.collectionId:r=r.child(e.collectionId)}let o=[];i.where&&(o=function(e){let t=function e(t){return void 0!==t.unaryFilter?function(e){switch(e.unaryFilter.op){case"IS_NAN":let t=rS(e.unaryFilter.field);return tk.create(t,"==",{doubleValue:NaN});case"IS_NULL":let n=rS(e.unaryFilter.field);return tk.create(n,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":let r=rS(e.unaryFilter.field);return tk.create(r,"!=",{doubleValue:NaN});case"IS_NOT_NULL":let i=rS(e.unaryFilter.field);return tk.create(i,"!=",{nullValue:"NULL_VALUE"});default:return b()}}(t):void 0!==t.fieldFilter?tk.create(rS(t.fieldFilter.field),function(e){switch(e){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";default:return b()}}(t.fieldFilter.op),t.fieldFilter.value):void 0!==t.compositeFilter?tV.create(t.compositeFilter.filters.map(t=>e(t)),function(e){switch(e){case"AND":return"and";case"OR":return"or";default:return b()}}(t.compositeFilter.op)):b()}(e);return t instanceof tV&&tM(t)?t.getFilters():[t]}(i.where));let l=[];i.orderBy&&(l=i.orderBy.map(e=>new tN(rS(e.field),function(e){switch(e){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}}(e.direction))));let u=null;i.limit&&(u=ew(n="object"==typeof(t=i.limit)?t.value:t)?null:n);let h=null;i.startAt&&(h=function(e){let t=!!e.before;return new tx(e.values||[],t)}(i.startAt));let c=null;return i.endAt&&(c=function(e){let t=!e.before;return new tx(e.values||[],t)}(i.endAt)),new t0(r,a,l,o,u,"F",h,c)}function rb(e){return{fieldPath:e.canonicalString()}}function rS(e){return G.fromServerFormat(e.fieldPath)}function rx(e){return e.length>=4&&"projects"===e.get(0)&&"databases"===e.get(2)}class rD{constructor(e,t,n,r,i=q.min(),s=q.min(),a=e5.EMPTY_BYTE_STRING,o=null){this.target=e,this.targetId=t,this.purpose=n,this.sequenceNumber=r,this.snapshotVersion=i,this.lastLimboFreeSnapshotVersion=s,this.resumeToken=a,this.expectedCount=o}withSequenceNumber(e){return new rD(this.target,this.targetId,this.purpose,e,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(e,t){return new rD(this.target,this.targetId,this.purpose,this.sequenceNumber,t,this.lastLimboFreeSnapshotVersion,e,null)}withExpectedCount(e){return new rD(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,e)}withLastLimboFreeSnapshotVersion(e){return new rD(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,e,this.resumeToken,this.expectedCount)}}class rC{constructor(e){this.ct=e}}function rN(e,t){let n=t.key,r={prefixPath:n.getCollectionPath().popLast().toArray(),collectionGroup:n.collectionGroup,documentId:n.path.lastSegment(),readTime:rA(t.readTime),hasCommittedMutations:t.hasCommittedMutations};if(t.isFoundDocument()){var i;r.document={name:rc(i=e.ct,t.key),fields:t.data.value.mapValue.fields,updateTime:rs(i,t.version.toTimestamp()),createTime:rs(i,t.createTime.toTimestamp())}}else if(t.isNoDocument())r.noDocument={path:n.path.toArray(),readTime:rk(t.version)};else{if(!t.isUnknownDocument())return b();r.unknownDocument={path:n.path.toArray(),version:rk(t.version)}}return r}function rA(e){let t=e.toTimestamp();return[t.seconds,t.nanoseconds]}function rk(e){let t=e.toTimestamp();return{seconds:t.seconds,nanoseconds:t.nanoseconds}}function rV(e){let t=new U(e.seconds,e.nanoseconds);return q.fromTimestamp(t)}function rR(e,t){let n=(t.baseMutations||[]).map(t=>rI(e.ct,t));for(let e=0;e<t.mutations.length-1;++e){let n=t.mutations[e];e+1<t.mutations.length&&void 0!==t.mutations[e+1].transform&&(n.updateTransforms=t.mutations[e+1].transform.fieldTransforms,t.mutations.splice(e+1,1),++e)}let r=t.mutations.map(t=>rI(e.ct,t)),i=U.fromMillis(t.localWriteTimeMs);return new nz(t.batchId,i,n,r)}function rO(e){var t;let n=rV(e.readTime),r=void 0!==e.lastLimboFreeSnapshotVersion?rV(e.lastLimboFreeSnapshotVersion):q.min();return new rD(void 0!==e.query.documents?(1===(t=e.query).documents.length||b(),t4(t1(rm(t.documents[0])))):t4(rE(e.query)),e.targetId,"TargetPurposeListen",e.lastListenSequenceNumber,n,r,e5.fromBase64String(e.resumeToken))}function rM(e,t){let n,r=rk(t.snapshotVersion),i=rk(t.lastLimboFreeSnapshotVersion);n=tJ(t.target)?rT(e.ct,t.target):r_(e.ct,t.target)._t;let s=t.resumeToken.toBase64();return{targetId:t.targetId,canonicalId:tW(t.target),readTime:r,resumeToken:s,lastListenSequenceNumber:t.sequenceNumber,lastLimboFreeSnapshotVersion:i,query:n}}function rF(e){let t=rE({parent:e.parent,structuredQuery:e.structuredQuery});return"LAST"===e.limitType?t9(t,t.limit,"L"):t}function rL(e,t){return new n$(t.largestBatchId,rI(e.ct,t.overlayMutation))}function rP(e,t){let n=t.path.lastSegment();return[e,eT(t.path.popLast()),n]}function rU(e,t,n,r){return{indexId:e,uid:t,sequenceNumber:n,readTime:rk(r.readTime),documentKey:eT(r.documentKey.path),largestBatchId:r.largestBatchId}}class rq{getBundleMetadata(e,t){return rB(e).get(t).next(e=>{if(e)return{id:e.bundleId,createTime:rV(e.createTime),version:e.version}})}saveBundleMetadata(e,t){return rB(e).put({bundleId:t.id,createTime:rk(ro(t.createTime)),version:t.version})}getNamedQuery(e,t){return rK(e).get(t).next(e=>{if(e)return{name:e.name,query:rF(e.bundledQuery),readTime:rV(e.readTime)}})}saveNamedQuery(e,t){return rK(e).put({name:t.name,readTime:rk(ro(t.readTime)),bundledQuery:t.bundledQuery})}}function rB(e){return e$(e,"bundles")}function rK(e){return e$(e,"namedQueries")}class rz{constructor(e,t){this.serializer=e,this.userId=t}static lt(e,t){return new rz(e,t.uid||"")}getOverlay(e,t){return rG(e).get(rP(this.userId,t)).next(e=>e?rL(this.serializer,e):null)}getOverlays(e,t){let n=nh();return ei.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&n.set(t,e)})).next(()=>n)}saveOverlays(e,t,n){let r=[];return n.forEach((n,i)=>{let s=new n$(t,i);r.push(this.ht(e,s))}),ei.waitFor(r)}removeOverlaysForBatchId(e,t,n){let r=new Set;t.forEach(e=>r.add(eT(e.getCollectionPath())));let i=[];return r.forEach(t=>{let r=IDBKeyRange.bound([this.userId,t,n],[this.userId,t,n+1],!1,!0);i.push(rG(e).j("collectionPathOverlayIndex",r))}),ei.waitFor(i)}getOverlaysForCollection(e,t,n){let r=nh(),i=eT(t),s=IDBKeyRange.bound([this.userId,i,n],[this.userId,i,Number.POSITIVE_INFINITY],!0);return rG(e).U("collectionPathOverlayIndex",s).next(e=>{for(let t of e){let e=rL(this.serializer,t);r.set(e.getKey(),e)}return r})}getOverlaysForCollectionGroup(e,t,n,r){let i,s=nh(),a=IDBKeyRange.bound([this.userId,t,n],[this.userId,t,Number.POSITIVE_INFINITY],!0);return rG(e).J({index:"collectionGroupOverlayIndex",range:a},(e,t,n)=>{let a=rL(this.serializer,t);s.size()<r||a.largestBatchId===i?(s.set(a.getKey(),a),i=a.largestBatchId):n.done()}).next(()=>s)}ht(e,t){return rG(e).put(function(e,t,n){let[r,i,s]=rP(t,n.mutation.key);return{userId:t,collectionPath:i,documentId:s,collectionGroup:n.mutation.key.getCollectionGroup(),largestBatchId:n.largestBatchId,overlayMutation:rv(e.ct,n.mutation)}}(this.serializer,this.userId,t))}}function rG(e){return e$(e,"documentOverlays")}class r${Pt(e){return e$(e,"globals")}getSessionToken(e){return this.Pt(e).get("sessionToken").next(e=>{let t=null==e?void 0:e.value;return t?e5.fromUint8Array(t):e5.EMPTY_BYTE_STRING})}setSessionToken(e,t){return this.Pt(e).put({name:"sessionToken",value:t.toUint8Array()})}}class rQ{constructor(){}It(e,t){this.Tt(e,t),t.Et()}Tt(e,t){if("nullValue"in e)this.dt(t,5);else if("booleanValue"in e)this.dt(t,10),t.At(+!!e.booleanValue);else if("integerValue"in e)this.dt(t,15),t.At(e8(e.integerValue));else if("doubleValue"in e){let n=e8(e.doubleValue);isNaN(n)?this.dt(t,13):(this.dt(t,15),ev(n)?t.At(0):t.At(n))}else if("timestampValue"in e){let n=e.timestampValue;this.dt(t,20),"string"==typeof n&&(n=e4(n)),t.Rt(`${n.seconds||""}`),t.At(n.nanos||0)}else if("stringValue"in e)this.Vt(e.stringValue,t),this.ft(t);else if("bytesValue"in e)this.dt(t,30),t.gt(e6(e.bytesValue)),this.ft(t);else if("referenceValue"in e)this.yt(e.referenceValue,t);else if("geoPointValue"in e){let n=e.geoPointValue;this.dt(t,45),t.At(n.latitude||0),t.At(n.longitude||0)}else"mapValue"in e?tI(e)?this.dt(t,Number.MAX_SAFE_INTEGER):tw(e)?this.wt(e.mapValue,t):(this.St(e.mapValue,t),this.ft(t)):"arrayValue"in e?(this.bt(e.arrayValue,t),this.ft(t)):b()}Vt(e,t){this.dt(t,25),this.Dt(e,t)}Dt(e,t){t.Rt(e)}St(e,t){let n=e.fields||{};for(let e of(this.dt(t,55),Object.keys(n)))this.Vt(e,t),this.Tt(n[e],t)}wt(e,t){var n,r;let i=e.fields||{};this.dt(t,53);let s="value",a=(null==(r=null==(n=i[s].arrayValue)?void 0:n.values)?void 0:r.length)||0;this.dt(t,15),t.At(e8(a)),this.Vt(s,t),this.Tt(i[s],t)}bt(e,t){let n=e.values||[];for(let e of(this.dt(t,50),n))this.Tt(e,t)}yt(e,t){this.dt(t,37),$.fromName(e).path.forEach(e=>{this.dt(t,60),this.Dt(e,t)})}dt(e,t){e.At(t)}ft(e){e.At(2)}}function rj(e){return Math.ceil((64-function(e){let t=0;for(let n=0;n<8;++n){let r=function(e){if(0===e)return 8;let t=0;return e>>4==0&&(t+=4,e<<=4),e>>6==0&&(t+=2,e<<=2),e>>7==0&&(t+=1),t}(255&e[n]);if(t+=r,8!==r)break}return t}(e))/8)}rQ.vt=new rQ;class rW{constructor(){this.buffer=new Uint8Array(1024),this.position=0}Ct(e){let t=e[Symbol.iterator](),n=t.next();for(;!n.done;)this.Ft(n.value),n=t.next();this.Mt()}xt(e){let t=e[Symbol.iterator](),n=t.next();for(;!n.done;)this.Ot(n.value),n=t.next();this.Nt()}Lt(e){for(let t of e){let e=t.charCodeAt(0);if(e<128)this.Ft(e);else if(e<2048)this.Ft(960|e>>>6),this.Ft(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.Ft(480|e>>>12),this.Ft(128|63&e>>>6),this.Ft(128|63&e);else{let e=t.codePointAt(0);this.Ft(240|e>>>18),this.Ft(128|63&e>>>12),this.Ft(128|63&e>>>6),this.Ft(128|63&e)}}this.Mt()}Bt(e){for(let t of e){let e=t.charCodeAt(0);if(e<128)this.Ot(e);else if(e<2048)this.Ot(960|e>>>6),this.Ot(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.Ot(480|e>>>12),this.Ot(128|63&e>>>6),this.Ot(128|63&e);else{let e=t.codePointAt(0);this.Ot(240|e>>>18),this.Ot(128|63&e>>>12),this.Ot(128|63&e>>>6),this.Ot(128|63&e)}}this.Nt()}kt(e){let t=this.qt(e),n=rj(t);this.Qt(1+n),this.buffer[this.position++]=255&n;for(let e=t.length-n;e<t.length;++e)this.buffer[this.position++]=255&t[e]}Kt(e){let t=this.qt(e),n=rj(t);this.Qt(1+n),this.buffer[this.position++]=~(255&n);for(let e=t.length-n;e<t.length;++e)this.buffer[this.position++]=~(255&t[e])}$t(){this.Ut(255),this.Ut(255)}Wt(){this.Gt(255),this.Gt(255)}reset(){this.position=0}seed(e){this.Qt(e.length),this.buffer.set(e,this.position),this.position+=e.length}zt(){return this.buffer.slice(0,this.position)}qt(e){let t=function(e){let t=new DataView(new ArrayBuffer(8));return t.setFloat64(0,e,!1),new Uint8Array(t.buffer)}(e),n=0!=(128&t[0]);t[0]^=n?255:128;for(let e=1;e<t.length;++e)t[e]^=255*!!n;return t}Ft(e){let t=255&e;0===t?(this.Ut(0),this.Ut(255)):255===t?(this.Ut(255),this.Ut(0)):this.Ut(t)}Ot(e){let t=255&e;0===t?(this.Gt(0),this.Gt(255)):255===t?(this.Gt(255),this.Gt(0)):this.Gt(e)}Mt(){this.Ut(0),this.Ut(1)}Nt(){this.Gt(0),this.Gt(1)}Ut(e){this.Qt(1),this.buffer[this.position++]=e}Gt(e){this.Qt(1),this.buffer[this.position++]=~e}Qt(e){let t=e+this.position;if(t<=this.buffer.length)return;let n=2*this.buffer.length;n<t&&(n=t);let r=new Uint8Array(n);r.set(this.buffer),this.buffer=r}}class rH{constructor(e){this.jt=e}gt(e){this.jt.Ct(e)}Rt(e){this.jt.Lt(e)}At(e){this.jt.kt(e)}Et(){this.jt.$t()}}class rJ{constructor(e){this.jt=e}gt(e){this.jt.xt(e)}Rt(e){this.jt.Bt(e)}At(e){this.jt.Kt(e)}Et(){this.jt.Wt()}}class rY{constructor(){this.jt=new rW,this.Ht=new rH(this.jt),this.Jt=new rJ(this.jt)}seed(e){this.jt.seed(e)}Yt(e){return 0===e?this.Ht:this.Jt}zt(){return this.jt.zt()}reset(){this.jt.reset()}}class rX{constructor(e,t,n,r){this.indexId=e,this.documentKey=t,this.arrayValue=n,this.directionalValue=r}Zt(){let e=this.directionalValue.length,t=0===e||255===this.directionalValue[e-1]?e+1:e,n=new Uint8Array(t);return n.set(this.directionalValue,0),t!==e?n.set([0],this.directionalValue.length):++n[n.length-1],new rX(this.indexId,this.documentKey,this.arrayValue,n)}}function rZ(e,t){let n=e.indexId-t.indexId;return 0!==n||0!==(n=r0(e.arrayValue,t.arrayValue))||0!==(n=r0(e.directionalValue,t.directionalValue))?n:$.comparator(e.documentKey,t.documentKey)}function r0(e,t){for(let n=0;n<e.length&&n<t.length;++n){let r=e[n]-t[n];if(0!==r)return r}return e.length-t.length}class r1{constructor(e){for(let t of(this.Xt=new eX((e,t)=>G.comparator(e.field,t.field)),this.collectionId=null!=e.collectionGroup?e.collectionGroup:e.path.lastSegment(),this.en=e.orderBy,this.tn=[],e.filters))t.isInequality()?this.Xt=this.Xt.add(t):this.tn.push(t)}get nn(){return this.Xt.size>1}rn(e){if(e.collectionGroup===this.collectionId||b(),this.nn)return!1;let t=j(e);if(void 0!==t&&!this.sn(t))return!1;let n=W(e),r=new Set,i=0,s=0;for(;i<n.length&&this.sn(n[i]);++i)r=r.add(n[i].fieldPath.canonicalString());if(i===n.length)return!0;if(this.Xt.size>0){let e=this.Xt.getIterator().getNext();if(!r.has(e.field.canonicalString())){let t=n[i];if(!this.on(e,t)||!this._n(this.en[s++],t))return!1}++i}for(;i<n.length;++i){let e=n[i];if(s>=this.en.length||!this._n(this.en[s++],e))return!1}return!0}an(){if(this.nn)return null;let e=new eX(G.comparator),t=[];for(let n of this.tn)if(!n.field.isKeyField())if("array-contains"===n.op||"array-contains-any"===n.op)t.push(new H(n.field,2));else{if(e.has(n.field))continue;e=e.add(n.field),t.push(new H(n.field,0))}for(let n of this.en)n.field.isKeyField()||e.has(n.field)||(e=e.add(n.field),t.push(new H(n.field,+("asc"!==n.dir))));return new Q(Q.UNKNOWN_ID,this.collectionId,t,J.empty())}sn(e){for(let t of this.tn)if(this.on(t,e))return!0;return!1}on(e,t){if(void 0===e||!e.field.isEqual(t.fieldPath))return!1;let n="array-contains"===e.op||"array-contains-any"===e.op;return 2===t.kind===n}_n(e,t){return!!e.field.isEqual(t.fieldPath)&&(0===t.kind&&"asc"===e.dir||1===t.kind&&"desc"===e.dir)}}function r2(e){return e instanceof tk}function r5(e){return e instanceof tV&&tM(e)}function r3(e){return r2(e)||r5(e)||function(e){if(e instanceof tV&&tO(e)){for(let t of e.getFilters())if(!r2(t)&&!r5(t))return!1;return!0}return!1}(e)}function r4(e,t){return e instanceof tk||e instanceof tV||b(),t instanceof tk||t instanceof tV||b(),r6(e instanceof tk?t instanceof tk?tV.create([e,t],"and"):r8(e,t):t instanceof tk?r8(t,e):function(e,t){if(e.filters.length>0&&t.filters.length>0||b(),tR(e)&&tR(t))return tL(e,t.getFilters());let n=tO(e)?e:t,r=tO(e)?t:e,i=n.filters.map(e=>r4(e,r));return tV.create(i,"or")}(e,t))}function r8(e,t){if(tR(t))return tL(t,e.getFilters());{let n=t.filters.map(t=>r4(e,t));return tV.create(n,"or")}}function r6(e){if(e instanceof tk||e instanceof tV||b(),e instanceof tk)return e;let t=e.getFilters();if(1===t.length)return r6(t[0]);if(tF(e))return e;let n=t.map(e=>r6(e)),r=[];return n.forEach(t=>{t instanceof tk?r.push(t):t instanceof tV&&(t.op===e.op?r.push(...t.filters):r.push(t))}),1===r.length?r[0]:tV.create(r,e.op)}class r9{constructor(){this.un=new r7}addToCollectionParentIndex(e,t){return this.un.add(t),ei.resolve()}getCollectionParents(e,t){return ei.resolve(this.un.getEntries(t))}addFieldIndex(e,t){return ei.resolve()}deleteFieldIndex(e,t){return ei.resolve()}deleteAllFieldIndexes(e){return ei.resolve()}createTargetIndexes(e,t){return ei.resolve()}getDocumentsMatchingTarget(e,t){return ei.resolve(null)}getIndexType(e,t){return ei.resolve(0)}getFieldIndexes(e,t){return ei.resolve([])}getNextCollectionGroupToUpdate(e){return ei.resolve(null)}getMinOffset(e,t){return ei.resolve(Z.min())}getMinOffsetFromCollectionGroup(e,t){return ei.resolve(Z.min())}updateCollectionGroup(e,t,n){return ei.resolve()}updateIndexEntries(e,t){return ei.resolve()}}class r7{constructor(){this.index={}}add(e){let t=e.lastSegment(),n=e.popLast(),r=this.index[t]||new eX(K.comparator),i=!r.has(n);return this.index[t]=r.add(n),i}has(e){let t=e.lastSegment(),n=e.popLast(),r=this.index[t];return r&&r.has(n)}getEntries(e){return(this.index[e]||new eX(K.comparator)).toArray()}}let ie=new Uint8Array(0);class it{constructor(e,t){this.databaseId=t,this.cn=new r7,this.ln=new ns(e=>tW(e),(e,t)=>tH(e,t)),this.uid=e.uid||""}addToCollectionParentIndex(e,t){if(!this.cn.has(t)){let n=t.lastSegment(),r=t.popLast();e.addOnCommittedListener(()=>{this.cn.add(t)});let i={collectionId:n,parent:eT(r)};return ir(e).put(i)}return ei.resolve()}getCollectionParents(e,t){let n=[],r=IDBKeyRange.bound([t,""],[t+"\0",""],!1,!0);return ir(e).U(r).next(e=>{for(let r of e){if(r.collectionId!==t)break;n.push(e_(r.parent))}return n})}addFieldIndex(e,t){let n=is(e),r={indexId:t.indexId,collectionGroup:t.collectionGroup,fields:t.fields.map(e=>[e.fieldPath.canonicalString(),e.kind])};delete r.indexId;let i=n.add(r);if(t.indexState){let n=ia(e);return i.next(e=>{n.put(rU(e,this.uid,t.indexState.sequenceNumber,t.indexState.offset))})}return i.next()}deleteFieldIndex(e,t){let n=is(e),r=ia(e),i=ii(e);return n.delete(t.indexId).next(()=>r.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0))).next(()=>i.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0)))}deleteAllFieldIndexes(e){let t=is(e),n=ii(e),r=ia(e);return t.j().next(()=>n.j()).next(()=>r.j())}createTargetIndexes(e,t){return ei.forEach(this.hn(t),t=>this.getIndexType(e,t).next(n=>{if(0===n||1===n){let n=new r1(t).an();if(null!=n)return this.addFieldIndex(e,n)}}))}getDocumentsMatchingTarget(e,t){let n=ii(e),r=!0,i=new Map;return ei.forEach(this.hn(t),t=>this.Pn(e,t).next(e=>{r&&(r=!!e),i.set(t,e)})).next(()=>{if(r){let e=nf(),r=[];return ei.forEach(i,(i,s)=>{I("IndexedDbIndexManager",`Using index id=${i.indexId}|cg=${i.collectionGroup}|f=${i.fields.map(e=>`${e.fieldPath}:${e.kind}`).join(",")} to execute ${tW(t)}`);let a=function(e,t){let n=j(t);if(void 0===n)return null;for(let t of tY(e,n.fieldPath))switch(t.op){case"array-contains-any":return t.value.arrayValue.values||[];case"array-contains":return[t.value]}return null}(s,i),o=function(e,t){let n=new Map;for(let r of W(t))for(let t of tY(e,r.fieldPath))switch(t.op){case"==":case"in":n.set(r.fieldPath.canonicalString(),t.value);break;case"not-in":case"!=":return n.set(r.fieldPath.canonicalString(),t.value),Array.from(n.values())}return null}(s,i),l=function(e,t){let n=[],r=!0;for(let i of W(t)){let t=0===i.kind?tX(e,i.fieldPath,e.startAt):tZ(e,i.fieldPath,e.startAt);n.push(t.value),r&&(r=t.inclusive)}return new tx(n,r)}(s,i),u=function(e,t){let n=[],r=!0;for(let i of W(t)){let t=0===i.kind?tZ(e,i.fieldPath,e.endAt):tX(e,i.fieldPath,e.endAt);n.push(t.value),r&&(r=t.inclusive)}return new tx(n,r)}(s,i),h=this.In(i,s,l),c=this.In(i,s,u),d=this.Tn(i,s,o),f=this.En(i.indexId,a,h,l.inclusive,c,u.inclusive,d);return ei.forEach(f,i=>n.G(i,t.limit).next(t=>{t.forEach(t=>{let n=$.fromSegments(t.documentKey);e.has(n)||(e=e.add(n),r.push(n))})}))}).next(()=>r)}return ei.resolve(null)})}hn(e){let t=this.ln.get(e);return t||(t=0===e.filters.length?[e]:(function(e){if(0===e.getFilters().length)return[];let t=function e(t){if(t instanceof tk||t instanceof tV||b(),t instanceof tk)return t;if(1===t.filters.length)return e(t.filters[0]);let n=t.filters.map(t=>e(t)),r=tV.create(n,t.op);return r3(r=r6(r))?r:(r instanceof tV||b(),tR(r)||b(),r.filters.length>1||b(),r.filters.reduce((e,t)=>r4(e,t)))}(function e(t){var n,r;if(t instanceof tk||t instanceof tV||b(),t instanceof tk){if(t instanceof tz){let e=(null==(r=null==(n=t.value.arrayValue)?void 0:n.values)?void 0:r.map(e=>tk.create(t.field,"==",e)))||[];return tV.create(e,"or")}return t}let i=t.filters.map(t=>e(t));return tV.create(i,t.op)}(e));return r3(t)||b(),r2(t)||r5(t)?[t]:t.getFilters()})(tV.create(e.filters,"and")).map(t=>tj(e.path,e.collectionGroup,e.orderBy,t.getFilters(),e.limit,e.startAt,e.endAt)),this.ln.set(e,t)),t}En(e,t,n,r,i,s,a){let o=(null!=t?t.length:1)*Math.max(n.length,i.length),l=o/(null!=t?t.length:1),u=[];for(let h=0;h<o;++h){let o=t?this.dn(t[h/l]):ie,c=this.An(e,o,n[h%l],r),d=this.Rn(e,o,i[h%l],s),f=a.map(t=>this.An(e,o,t,!0));u.push(...this.createRange(c,d,f))}return u}An(e,t,n,r){let i=new rX(e,$.empty(),t,n);return r?i:i.Zt()}Rn(e,t,n,r){let i=new rX(e,$.empty(),t,n);return r?i.Zt():i}Pn(e,t){let n=new r1(t),r=null!=t.collectionGroup?t.collectionGroup:t.path.lastSegment();return this.getFieldIndexes(e,r).next(e=>{let t=null;for(let r of e)n.rn(r)&&(!t||r.fields.length>t.fields.length)&&(t=r);return t})}getIndexType(e,t){let n=2,r=this.hn(t);return ei.forEach(r,t=>this.Pn(e,t).next(e=>{e?0!==n&&e.fields.length<function(e){let t=new eX(G.comparator),n=!1;for(let r of e.filters)for(let e of r.getFlattenedFilters())e.field.isKeyField()||("array-contains"===e.op||"array-contains-any"===e.op?n=!0:t=t.add(e.field));for(let n of e.orderBy)n.field.isKeyField()||(t=t.add(n.field));return t.size+ +!!n}(t)&&(n=1):n=0})).next(()=>null!==t.limit&&r.length>1&&2===n?1:n)}Vn(e,t){let n=new rY;for(let r of W(e)){let e=t.data.field(r.fieldPath);if(null==e)return null;let i=n.Yt(r.kind);rQ.vt.It(e,i)}return n.zt()}dn(e){let t=new rY;return rQ.vt.It(e,t.Yt(0)),t.zt()}mn(e,t){let n=new rY;return rQ.vt.It(td(this.databaseId,t),n.Yt(function(e){let t=W(e);return 0===t.length?0:t[t.length-1].kind}(e))),n.zt()}Tn(e,t,n){if(null===n)return[];let r=[];r.push(new rY);let i=0;for(let s of W(e)){let e=n[i++];for(let n of r)if(this.fn(t,s.fieldPath)&&tm(e))r=this.gn(r,s,e);else{let t=n.Yt(s.kind);rQ.vt.It(e,t)}}return this.pn(r)}In(e,t,n){return this.Tn(e,t,n.position)}pn(e){let t=[];for(let n=0;n<e.length;++n)t[n]=e[n].zt();return t}gn(e,t,n){let r=[...e],i=[];for(let e of n.arrayValue.values||[])for(let n of r){let r=new rY;r.seed(n.zt()),rQ.vt.It(e,r.Yt(t.kind)),i.push(r)}return i}fn(e,t){return!!e.filters.find(e=>e instanceof tk&&e.field.isEqual(t)&&("in"===e.op||"not-in"===e.op))}getFieldIndexes(e,t){let n=is(e),r=ia(e);return(t?n.U("collectionGroupIndex",IDBKeyRange.bound(t,t)):n.U()).next(e=>{let t=[];return ei.forEach(e,e=>r.get([e.indexId,this.uid]).next(n=>{t.push(function(e,t){let n=t?new J(t.sequenceNumber,new Z(rV(t.readTime),new $(e_(t.documentKey)),t.largestBatchId)):J.empty(),r=e.fields.map(([e,t])=>new H(G.fromServerFormat(e),t));return new Q(e.indexId,e.collectionGroup,r,n)}(e,n))})).next(()=>t)})}getNextCollectionGroupToUpdate(e){return this.getFieldIndexes(e).next(e=>0===e.length?null:(e.sort((e,t)=>{let n=e.indexState.sequenceNumber-t.indexState.sequenceNumber;return 0!==n?n:L(e.collectionGroup,t.collectionGroup)}),e[0].collectionGroup))}updateCollectionGroup(e,t,n){let r=is(e),i=ia(e);return this.yn(e).next(e=>r.U("collectionGroupIndex",IDBKeyRange.bound(t,t)).next(t=>ei.forEach(t,t=>i.put(rU(t.indexId,this.uid,e,n)))))}updateIndexEntries(e,t){let n=new Map;return ei.forEach(t,(t,r)=>{let i=n.get(t.collectionGroup);return(i?ei.resolve(i):this.getFieldIndexes(e,t.collectionGroup)).next(i=>(n.set(t.collectionGroup,i),ei.forEach(i,n=>this.wn(e,t,n).next(t=>{let i=this.Sn(r,n);return t.isEqual(i)?ei.resolve():this.bn(e,r,n,t,i)}))))})}Dn(e,t,n,r){return ii(e).put({indexId:r.indexId,uid:this.uid,arrayValue:r.arrayValue,directionalValue:r.directionalValue,orderedDocumentKey:this.mn(n,t.key),documentKey:t.key.path.toArray()})}vn(e,t,n,r){return ii(e).delete([r.indexId,this.uid,r.arrayValue,r.directionalValue,this.mn(n,t.key),t.key.path.toArray()])}wn(e,t,n){let r=ii(e),i=new eX(rZ);return r.J({index:"documentKeyIndex",range:IDBKeyRange.only([n.indexId,this.uid,this.mn(n,t)])},(e,r)=>{i=i.add(new rX(n.indexId,t,r.arrayValue,r.directionalValue))}).next(()=>i)}Sn(e,t){let n=new eX(rZ),r=this.Vn(t,e);if(null==r)return n;let i=j(t);if(null!=i){let s=e.data.field(i.fieldPath);if(tm(s))for(let i of s.arrayValue.values||[])n=n.add(new rX(t.indexId,e.key,this.dn(i),r))}else n=n.add(new rX(t.indexId,e.key,ie,r));return n}bn(e,t,n,r,i){I("IndexedDbIndexManager","Updating index entries for document '%s'",t.key);let s=[];return function(e,t,n,r,i){let s=e.getIterator(),a=t.getIterator(),o=e0(s),l=e0(a);for(;o||l;){let e=!1,t=!1;if(o&&l){let r=n(o,l);r<0?t=!0:r>0&&(e=!0)}else null!=o?t=!0:e=!0;e?(r(l),l=e0(a)):t?(i(o),o=e0(s)):(o=e0(s),l=e0(a))}}(r,i,rZ,r=>{s.push(this.Dn(e,t,n,r))},r=>{s.push(this.vn(e,t,n,r))}),ei.waitFor(s)}yn(e){let t=1;return ia(e).J({index:"sequenceNumberIndex",reverse:!0,range:IDBKeyRange.upperBound([this.uid,Number.MAX_SAFE_INTEGER])},(e,n,r)=>{r.done(),t=n.sequenceNumber+1}).next(()=>t)}createRange(e,t,n){n=n.sort((e,t)=>rZ(e,t)).filter((e,t,n)=>!t||0!==rZ(e,n[t-1]));let r=[];for(let i of(r.push(e),n)){let n=rZ(i,e),s=rZ(i,t);if(0===n)r[0]=e.Zt();else if(n>0&&s<0)r.push(i),r.push(i.Zt());else if(s>0)break}r.push(t);let i=[];for(let e=0;e<r.length;e+=2){if(this.Cn(r[e],r[e+1]))return[];let t=[r[e].indexId,this.uid,r[e].arrayValue,r[e].directionalValue,ie,[]],n=[r[e+1].indexId,this.uid,r[e+1].arrayValue,r[e+1].directionalValue,ie,[]];i.push(IDBKeyRange.bound(t,n))}return i}Cn(e,t){return rZ(e,t)>0}getMinOffsetFromCollectionGroup(e,t){return this.getFieldIndexes(e,t).next(io)}getMinOffset(e,t){return ei.mapArray(this.hn(t),t=>this.Pn(e,t).next(e=>e||b())).next(io)}}function ir(e){return e$(e,"collectionParents")}function ii(e){return e$(e,"indexEntries")}function is(e){return e$(e,"indexConfiguration")}function ia(e){return e$(e,"indexState")}function io(e){0!==e.length||b();let t=e[0].indexState.offset,n=t.largestBatchId;for(let r=1;r<e.length;r++){let i=e[r].indexState.offset;0>ee(i,t)&&(t=i),n<i.largestBatchId&&(n=i.largestBatchId)}return new Z(t.readTime,t.documentKey,n)}let il={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0};class iu{constructor(e,t,n){this.cacheSizeCollectionThreshold=e,this.percentileToCollect=t,this.maximumSequenceNumbersToCollect=n}static withCacheSize(e){return new iu(e,iu.DEFAULT_COLLECTION_PERCENTILE,iu.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}}function ih(e,t,n){let r=e.store("mutations"),i=e.store("documentMutations"),s=[],a=IDBKeyRange.only(n.batchId),o=0,l=r.J({range:a},(e,t,n)=>(o++,n.delete()));s.push(l.next(()=>{1===o||b()}));let u=[];for(let e of n.mutations){var h,c;let r=(h=e.key.path,c=n.batchId,[t,eT(h),c]);s.push(i.delete(r)),u.push(e.key)}return ei.waitFor(s).next(()=>u)}function ic(e){let t;if(!e)return 0;if(e.document)t=e.document;else if(e.unknownDocument)t=e.unknownDocument;else{if(!e.noDocument)throw b();t=e.noDocument}return JSON.stringify(t).length}iu.DEFAULT_COLLECTION_PERCENTILE=10,iu.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,iu.DEFAULT=new iu(0x2800000,iu.DEFAULT_COLLECTION_PERCENTILE,iu.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),iu.DISABLED=new iu(-1,0,0);class id{constructor(e,t,n,r){this.userId=e,this.serializer=t,this.indexManager=n,this.referenceDelegate=r,this.Fn={}}static lt(e,t,n,r){return""!==e.uid||b(),new id(e.isAuthenticated()?e.uid:"",t,n,r)}checkEmpty(e){let t=!0,n=IDBKeyRange.bound([this.userId,Number.NEGATIVE_INFINITY],[this.userId,Number.POSITIVE_INFINITY]);return ig(e).J({index:"userMutationsIndex",range:n},(e,n,r)=>{t=!1,r.done()}).next(()=>t)}addMutationBatch(e,t,n,r){let i=ip(e),s=ig(e);return s.add({}).next(a=>{var o,l;"number"==typeof a||b();let u=new nz(a,t,n,r),h=function(e,t,n){let r=n.baseMutations.map(t=>rv(e.ct,t)),i=n.mutations.map(t=>rv(e.ct,t));return{userId:t,batchId:n.batchId,localWriteTimeMs:n.localWriteTime.toMillis(),baseMutations:r,mutations:i}}(this.serializer,this.userId,u),c=[],d=new eX((e,t)=>L(e.canonicalString(),t.canonicalString()));for(let e of r){let t=(o=this.userId,l=e.key.path,[o,eT(l),a]);d=d.add(e.key.path.popLast()),c.push(s.put(h)),c.push(i.put(t,eb))}return d.forEach(t=>{c.push(this.indexManager.addToCollectionParentIndex(e,t))}),e.addOnCommittedListener(()=>{this.Fn[a]=u.keys()}),ei.waitFor(c).next(()=>u)})}lookupMutationBatch(e,t){return ig(e).get(t).next(e=>e?(e.userId===this.userId||b(),rR(this.serializer,e)):null)}Mn(e,t){return this.Fn[t]?ei.resolve(this.Fn[t]):this.lookupMutationBatch(e,t).next(e=>{if(e){let n=e.keys();return this.Fn[t]=n,n}return null})}getNextMutationBatchAfterBatchId(e,t){let n=t+1,r=IDBKeyRange.lowerBound([this.userId,n]),i=null;return ig(e).J({index:"userMutationsIndex",range:r},(e,t,r)=>{t.userId===this.userId&&(t.batchId>=n||b(),i=rR(this.serializer,t)),r.done()}).next(()=>i)}getHighestUnacknowledgedBatchId(e){let t=IDBKeyRange.upperBound([this.userId,Number.POSITIVE_INFINITY]),n=-1;return ig(e).J({index:"userMutationsIndex",range:t,reverse:!0},(e,t,r)=>{n=t.batchId,r.done()}).next(()=>n)}getAllMutationBatches(e){let t=IDBKeyRange.bound([this.userId,-1],[this.userId,Number.POSITIVE_INFINITY]);return ig(e).U("userMutationsIndex",t).next(e=>e.map(e=>rR(this.serializer,e)))}getAllMutationBatchesAffectingDocumentKey(e,t){let n=[this.userId,eT(t.path)],r=IDBKeyRange.lowerBound(n),i=[];return ip(e).J({range:r},(n,r,s)=>{let[a,o,l]=n,u=e_(o);if(a===this.userId&&t.path.isEqual(u))return ig(e).get(l).next(e=>{if(!e)throw b();e.userId===this.userId||b(),i.push(rR(this.serializer,e))});s.done()}).next(()=>i)}getAllMutationBatchesAffectingDocumentKeys(e,t){let n=new eX(L),r=[];return t.forEach(t=>{let i=[this.userId,eT(t.path)],s=IDBKeyRange.lowerBound(i),a=ip(e).J({range:s},(e,r,i)=>{let[s,a,o]=e,l=e_(a);s===this.userId&&t.path.isEqual(l)?n=n.add(o):i.done()});r.push(a)}),ei.waitFor(r).next(()=>this.xn(e,n))}getAllMutationBatchesAffectingQuery(e,t){let n=t.path,r=n.length+1,i=[this.userId,eT(n)],s=IDBKeyRange.lowerBound(i),a=new eX(L);return ip(e).J({range:s},(e,t,i)=>{let[s,o,l]=e,u=e_(o);s===this.userId&&n.isPrefixOf(u)?u.length===r&&(a=a.add(l)):i.done()}).next(()=>this.xn(e,a))}xn(e,t){let n=[],r=[];return t.forEach(t=>{r.push(ig(e).get(t).next(e=>{if(null===e)throw b();e.userId===this.userId||b(),n.push(rR(this.serializer,e))}))}),ei.waitFor(r).next(()=>n)}removeMutationBatch(e,t){return ih(e._e,this.userId,t).next(n=>(e.addOnCommittedListener(()=>{this.On(t.batchId)}),ei.forEach(n,t=>this.referenceDelegate.markPotentiallyOrphaned(e,t))))}On(e){delete this.Fn[e]}performConsistencyCheck(e){return this.checkEmpty(e).next(t=>{if(!t)return ei.resolve();let n=IDBKeyRange.lowerBound([this.userId]),r=[];return ip(e).J({range:n},(e,t,n)=>{if(e[0]===this.userId){let t=e_(e[1]);r.push(t)}else n.done()}).next(()=>{0===r.length||b()})})}containsKey(e,t){return im(e,this.userId,t)}Nn(e){return iy(e).get(this.userId).next(e=>e||{userId:this.userId,lastAcknowledgedBatchId:-1,lastStreamToken:""})}}function im(e,t,n){let r=[t,eT(n.path)],i=r[1],s=IDBKeyRange.lowerBound(r),a=!1;return ip(e).J({range:s,H:!0},(e,n,r)=>{let[s,o,l]=e;s===t&&o===i&&(a=!0),r.done()}).next(()=>a)}function ig(e){return e$(e,"mutations")}function ip(e){return e$(e,"documentMutations")}function iy(e){return e$(e,"mutationQueues")}class iw{constructor(e){this.Ln=e}next(){return this.Ln+=2,this.Ln}static Bn(){return new iw(0)}static kn(){return new iw(-1)}}class iv{constructor(e,t){this.referenceDelegate=e,this.serializer=t}allocateTargetId(e){return this.qn(e).next(t=>{let n=new iw(t.highestTargetId);return t.highestTargetId=n.next(),this.Qn(e,t).next(()=>t.highestTargetId)})}getLastRemoteSnapshotVersion(e){return this.qn(e).next(e=>q.fromTimestamp(new U(e.lastRemoteSnapshotVersion.seconds,e.lastRemoteSnapshotVersion.nanoseconds)))}getHighestSequenceNumber(e){return this.qn(e).next(e=>e.highestListenSequenceNumber)}setTargetsMetadata(e,t,n){return this.qn(e).next(r=>(r.highestListenSequenceNumber=t,n&&(r.lastRemoteSnapshotVersion=n.toTimestamp()),t>r.highestListenSequenceNumber&&(r.highestListenSequenceNumber=t),this.Qn(e,r)))}addTargetData(e,t){return this.Kn(e,t).next(()=>this.qn(e).next(n=>(n.targetCount+=1,this.$n(t,n),this.Qn(e,n))))}updateTargetData(e,t){return this.Kn(e,t)}removeTargetData(e,t){return this.removeMatchingKeysForTargetId(e,t.targetId).next(()=>iI(e).delete(t.targetId)).next(()=>this.qn(e)).next(t=>(t.targetCount>0||b(),t.targetCount-=1,this.Qn(e,t)))}removeTargets(e,t,n){let r=0,i=[];return iI(e).J((s,a)=>{let o=rO(a);o.sequenceNumber<=t&&null===n.get(o.targetId)&&(r++,i.push(this.removeTargetData(e,o)))}).next(()=>ei.waitFor(i)).next(()=>r)}forEachTarget(e,t){return iI(e).J((e,n)=>{t(rO(n))})}qn(e){return iT(e).get("targetGlobalKey").next(e=>(null!==e||b(),e))}Qn(e,t){return iT(e).put("targetGlobalKey",t)}Kn(e,t){return iI(e).put(rM(this.serializer,t))}$n(e,t){let n=!1;return e.targetId>t.highestTargetId&&(t.highestTargetId=e.targetId,n=!0),e.sequenceNumber>t.highestListenSequenceNumber&&(t.highestListenSequenceNumber=e.sequenceNumber,n=!0),n}getTargetCount(e){return this.qn(e).next(e=>e.targetCount)}getTargetData(e,t){let n=tW(t),r=IDBKeyRange.bound([n,Number.NEGATIVE_INFINITY],[n,Number.POSITIVE_INFINITY]),i=null;return iI(e).J({range:r,index:"queryTargetsIndex"},(e,n,r)=>{let s=rO(n);tH(t,s.target)&&(i=s,r.done())}).next(()=>i)}addMatchingKeys(e,t,n){let r=[],i=i_(e);return t.forEach(t=>{let s=eT(t.path);r.push(i.put({targetId:n,path:s})),r.push(this.referenceDelegate.addReference(e,n,t))}),ei.waitFor(r)}removeMatchingKeys(e,t,n){let r=i_(e);return ei.forEach(t,t=>{let i=eT(t.path);return ei.waitFor([r.delete([n,i]),this.referenceDelegate.removeReference(e,n,t)])})}removeMatchingKeysForTargetId(e,t){let n=i_(e),r=IDBKeyRange.bound([t],[t+1],!1,!0);return n.delete(r)}getMatchingKeysForTargetId(e,t){let n=IDBKeyRange.bound([t],[t+1],!1,!0),r=i_(e),i=nf();return r.J({range:n,H:!0},(e,t,n)=>{let r=new $(e_(e[1]));i=i.add(r)}).next(()=>i)}containsKey(e,t){let n=eT(t.path),r=IDBKeyRange.bound([n],[n+"\0"],!1,!0),i=0;return i_(e).J({index:"documentTargetsIndex",H:!0,range:r},([e,t],n,r)=>{0!==e&&(i++,r.done())}).next(()=>i>0)}ot(e,t){return iI(e).get(t).next(e=>e?rO(e):null)}}function iI(e){return e$(e,"targets")}function iT(e){return e$(e,"targetGlobal")}function i_(e){return e$(e,"targetDocuments")}function iE([e,t],[n,r]){let i=L(e,n);return 0===i?L(t,r):i}class ib{constructor(e){this.Un=e,this.buffer=new eX(iE),this.Wn=0}Gn(){return++this.Wn}zn(e){let t=[e,this.Gn()];if(this.buffer.size<this.Un)this.buffer=this.buffer.add(t);else{let e=this.buffer.last();0>iE(t,e)&&(this.buffer=this.buffer.delete(e).add(t))}}get maxValue(){return this.buffer.last()[0]}}class iS{constructor(e,t,n){this.garbageCollector=e,this.asyncQueue=t,this.localStore=n,this.jn=null}start(){-1!==this.garbageCollector.params.cacheSizeCollectionThreshold&&this.Hn(6e4)}stop(){this.jn&&(this.jn.cancel(),this.jn=null)}get started(){return null!==this.jn}Hn(e){I("LruGarbageCollector",`Garbage collection scheduled in ${e}ms`),this.jn=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,async()=>{this.jn=null;try{await this.localStore.collectGarbage(this.garbageCollector)}catch(e){eh(e)?I("LruGarbageCollector","Ignoring IndexedDB error during garbage collection: ",e):await er(e)}await this.Hn(3e5)})}}class ix{constructor(e,t){this.Jn=e,this.params=t}calculateTargetCount(e,t){return this.Jn.Yn(e).next(e=>Math.floor(t/100*e))}nthSequenceNumber(e,t){if(0===t)return ei.resolve(ey.oe);let n=new ib(t);return this.Jn.forEachTarget(e,e=>n.zn(e.sequenceNumber)).next(()=>this.Jn.Zn(e,e=>n.zn(e))).next(()=>n.maxValue)}removeTargets(e,t,n){return this.Jn.removeTargets(e,t,n)}removeOrphanedDocuments(e,t){return this.Jn.removeOrphanedDocuments(e,t)}collect(e,t){return -1===this.params.cacheSizeCollectionThreshold?(I("LruGarbageCollector","Garbage collection skipped; disabled"),ei.resolve(il)):this.getCacheSize(e).next(n=>n<this.params.cacheSizeCollectionThreshold?(I("LruGarbageCollector",`Garbage collection skipped; Cache size ${n} is lower than threshold ${this.params.cacheSizeCollectionThreshold}`),il):this.Xn(e,t))}getCacheSize(e){return this.Jn.getCacheSize(e)}Xn(e,t){let n,r,i,s,a,o,l,h=Date.now();return this.calculateTargetCount(e,this.params.percentileToCollect).next(t=>(t>this.params.maximumSequenceNumbersToCollect?(I("LruGarbageCollector",`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from ${t}`),r=this.params.maximumSequenceNumbersToCollect):r=t,s=Date.now(),this.nthSequenceNumber(e,r))).next(r=>(n=r,a=Date.now(),this.removeTargets(e,n,t))).next(t=>(i=t,o=Date.now(),this.removeOrphanedDocuments(e,n))).next(e=>(l=Date.now(),v()<=u.$b.DEBUG&&I("LruGarbageCollector",`LRU Garbage Collection
	Counted targets in ${s-h}ms
	Determined least recently used ${r} in `+(a-s)+"ms\n"+`	Removed ${i} targets in `+(o-a)+"ms\n"+`	Removed ${e} documents in `+(l-o)+"ms\n"+`Total Duration: ${l-h}ms`),ei.resolve({didRun:!0,sequenceNumbersCollected:r,targetsRemoved:i,documentsRemoved:e})))}}class iD{constructor(e,t){this.db=e,this.garbageCollector=new ix(this,t)}Yn(e){let t=this.er(e);return this.db.getTargetCache().getTargetCount(e).next(e=>t.next(t=>e+t))}er(e){let t=0;return this.Zn(e,e=>{t++}).next(()=>t)}forEachTarget(e,t){return this.db.getTargetCache().forEachTarget(e,t)}Zn(e,t){return this.tr(e,(e,n)=>t(n))}addReference(e,t,n){return iC(e,n)}removeReference(e,t,n){return iC(e,n)}removeTargets(e,t,n){return this.db.getTargetCache().removeTargets(e,t,n)}markPotentiallyOrphaned(e,t){return iC(e,t)}nr(e,t){let n;return n=!1,iy(e).Y(r=>im(e,r,t).next(e=>(e&&(n=!0),ei.resolve(!e)))).next(()=>n)}removeOrphanedDocuments(e,t){let n=this.db.getRemoteDocumentCache().newChangeBuffer(),r=[],i=0;return this.tr(e,(s,a)=>{if(a<=t){let t=this.nr(e,s).next(t=>{if(!t)return i++,n.getEntry(e,s).next(()=>(n.removeEntry(s,q.min()),i_(e).delete([0,eT(s.path)])))});r.push(t)}}).next(()=>ei.waitFor(r)).next(()=>n.apply(e)).next(()=>i)}removeTarget(e,t){let n=t.withSequenceNumber(e.currentSequenceNumber);return this.db.getTargetCache().updateTargetData(e,n)}updateLimboDocument(e,t){return iC(e,t)}tr(e,t){let n=i_(e),r,i=ey.oe;return n.J({index:"documentTargetsIndex"},([e,n],{path:s,sequenceNumber:a})=>{0===e?(i!==ey.oe&&t(new $(e_(r)),i),i=a,r=s):i=ey.oe}).next(()=>{i!==ey.oe&&t(new $(e_(r)),i)})}getCacheSize(e){return this.db.getRemoteDocumentCache().getSize(e)}}function iC(e,t){var n;return i_(e).put((n=e.currentSequenceNumber,{targetId:0,path:eT(t.path),sequenceNumber:n}))}class iN{constructor(){this.changes=new ns(e=>e.toString(),(e,t)=>e.isEqual(t)),this.changesApplied=!1}addEntry(e){this.assertNotApplied(),this.changes.set(e.key,e)}removeEntry(e,t){this.assertNotApplied(),this.changes.set(e,tS.newInvalidDocument(e).setReadTime(t))}getEntry(e,t){this.assertNotApplied();let n=this.changes.get(t);return void 0!==n?ei.resolve(n):this.getFromCache(e,t)}getEntries(e,t){return this.getAllFromCache(e,t)}apply(e){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(e)}assertNotApplied(){}}class iA{constructor(e){this.serializer=e}setIndexManager(e){this.indexManager=e}addEntry(e,t,n){return iR(e).put(n)}removeEntry(e,t,n){return iR(e).delete(function(e,t){let n=e.path.toArray();return[n.slice(0,n.length-2),n[n.length-2],rA(t),n[n.length-1]]}(t,n))}updateMetadata(e,t){return this.getMetadata(e).next(n=>(n.byteSize+=t,this.rr(e,n)))}getEntry(e,t){let n=tS.newInvalidDocument(t);return iR(e).J({index:"documentKeyIndex",range:IDBKeyRange.only(iO(t))},(e,r)=>{n=this.ir(t,r)}).next(()=>n)}sr(e,t){let n={size:0,document:tS.newInvalidDocument(t)};return iR(e).J({index:"documentKeyIndex",range:IDBKeyRange.only(iO(t))},(e,r)=>{n={document:this.ir(t,r),size:ic(r)}}).next(()=>n)}getEntries(e,t){let n=na;return this._r(e,t,(e,t)=>{let r=this.ir(e,t);n=n.insert(e,r)}).next(()=>n)}ar(e,t){let n=na,r=new eH($.comparator);return this._r(e,t,(e,t)=>{let i=this.ir(e,t);n=n.insert(e,i),r=r.insert(e,ic(t))}).next(()=>({documents:n,ur:r}))}_r(e,t,n){if(t.isEmpty())return ei.resolve();let r=new eX(iF);t.forEach(e=>r=r.add(e));let i=IDBKeyRange.bound(iO(r.first()),iO(r.last())),s=r.getIterator(),a=s.getNext();return iR(e).J({index:"documentKeyIndex",range:i},(e,t,r)=>{let i=$.fromSegments([...t.prefixPath,t.collectionGroup,t.documentId]);for(;a&&0>iF(a,i);)n(a,null),a=s.getNext();a&&a.isEqual(i)&&(n(a,t),a=s.hasNext()?s.getNext():null),a?r.$(iO(a)):r.done()}).next(()=>{for(;a;)n(a,null),a=s.hasNext()?s.getNext():null})}getDocumentsMatchingQuery(e,t,n,r,i){let s=t.path,a=[s.popLast().toArray(),s.lastSegment(),rA(n.readTime),n.documentKey.path.isEmpty()?"":n.documentKey.path.lastSegment()],o=[s.popLast().toArray(),s.lastSegment(),[Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],""];return iR(e).U(IDBKeyRange.bound(a,o,!0)).next(e=>{null==i||i.incrementDocumentReadCount(e.length);let n=na;for(let i of e){let e=this.ir($.fromSegments(i.prefixPath.concat(i.collectionGroup,i.documentId)),i);e.isFoundDocument()&&(nn(t,e)||r.has(e.key))&&(n=n.insert(e.key,e))}return n})}getAllFromCollectionGroup(e,t,n,r){let i=na,s=iM(t,n),a=iM(t,Z.max());return iR(e).J({index:"collectionGroupIndex",range:IDBKeyRange.bound(s,a,!0)},(e,t,n)=>{let s=this.ir($.fromSegments(t.prefixPath.concat(t.collectionGroup,t.documentId)),t);(i=i.insert(s.key,s)).size===r&&n.done()}).next(()=>i)}newChangeBuffer(e){return new ik(this,!!e&&e.trackRemovals)}getSize(e){return this.getMetadata(e).next(e=>e.byteSize)}getMetadata(e){return iV(e).get("remoteDocumentGlobalKey").next(e=>(e||b(),e))}rr(e,t){return iV(e).put("remoteDocumentGlobalKey",t)}ir(e,t){if(t){let e=function(e,t){let n;if(t.document)n=rw(e.ct,t.document,!!t.hasCommittedMutations);else if(t.noDocument){let e=$.fromSegments(t.noDocument.path),r=rV(t.noDocument.readTime);n=tS.newNoDocument(e,r),t.hasCommittedMutations&&n.setHasCommittedMutations()}else{if(!t.unknownDocument)return b();{let e=$.fromSegments(t.unknownDocument.path),r=rV(t.unknownDocument.version);n=tS.newUnknownDocument(e,r)}}return t.readTime&&n.setReadTime(function(e){let t=new U(e[0],e[1]);return q.fromTimestamp(t)}(t.readTime)),n}(this.serializer,t);if(!(e.isNoDocument()&&e.version.isEqual(q.min())))return e}return tS.newInvalidDocument(e)}}class ik extends iN{constructor(e,t){super(),this.cr=e,this.trackRemovals=t,this.lr=new ns(e=>e.toString(),(e,t)=>e.isEqual(t))}applyChanges(e){let t=[],n=0,r=new eX((e,t)=>L(e.canonicalString(),t.canonicalString()));return this.changes.forEach((i,s)=>{let a=this.lr.get(i);if(t.push(this.cr.removeEntry(e,i,a.readTime)),s.isValidDocument()){let o=rN(this.cr.serializer,s);r=r.add(i.path.popLast());let l=ic(o);n+=l-a.size,t.push(this.cr.addEntry(e,i,o))}else if(n-=a.size,this.trackRemovals){let n=rN(this.cr.serializer,s.convertToNoDocument(q.min()));t.push(this.cr.addEntry(e,i,n))}}),r.forEach(n=>{t.push(this.cr.indexManager.addToCollectionParentIndex(e,n))}),t.push(this.cr.updateMetadata(e,n)),ei.waitFor(t)}getFromCache(e,t){return this.cr.sr(e,t).next(e=>(this.lr.set(t,{size:e.size,readTime:e.document.readTime}),e.document))}getAllFromCache(e,t){return this.cr.ar(e,t).next(({documents:e,ur:t})=>(t.forEach((t,n)=>{this.lr.set(t,{size:n,readTime:e.get(t).readTime})}),e))}}function iV(e){return e$(e,"remoteDocumentGlobal")}function iR(e){return e$(e,"remoteDocumentsV14")}function iO(e){let t=e.path.toArray();return[t.slice(0,t.length-2),t[t.length-2],t[t.length-1]]}function iM(e,t){let n=t.documentKey.path.toArray();return[e,rA(t.readTime),n.slice(0,n.length-2),n.length>0?n[n.length-1]:""]}function iF(e,t){let n=e.path.toArray(),r=t.path.toArray(),i=0;for(let e=0;e<n.length-2&&e<r.length-2;++e)if(i=L(n[e],r[e]))return i;return(i=L(n.length,r.length))||(i=L(n[n.length-2],r[r.length-2]))||L(n[n.length-1],r[r.length-1])}class iL{constructor(e,t){this.overlayedDocument=e,this.mutatedFields=t}}class iP{constructor(e,t,n,r){this.remoteDocumentCache=e,this.mutationQueue=t,this.documentOverlayCache=n,this.indexManager=r}getDocument(e,t){let n=null;return this.documentOverlayCache.getOverlay(e,t).next(r=>(n=r,this.remoteDocumentCache.getEntry(e,t))).next(e=>(null!==n&&nO(n.mutation,e,e1.empty(),U.now()),e))}getDocuments(e,t){return this.remoteDocumentCache.getEntries(e,t).next(t=>this.getLocalViewOfDocuments(e,t,nf()).next(()=>t))}getLocalViewOfDocuments(e,t,n=nf()){let r=nh();return this.populateOverlays(e,r,t).next(()=>this.computeViews(e,t,r,n).next(e=>{let t=nl();return e.forEach((e,n)=>{t=t.insert(e,n.overlayedDocument)}),t}))}getOverlayedDocuments(e,t){let n=nh();return this.populateOverlays(e,n,t).next(()=>this.computeViews(e,t,n,nf()))}populateOverlays(e,t,n){let r=[];return n.forEach(e=>{t.has(e)||r.push(e)}),this.documentOverlayCache.getOverlays(e,r).next(e=>{e.forEach((e,n)=>{t.set(e,n)})})}computeViews(e,t,n,r){let i=na,s=nh(),a=nh();return t.forEach((e,t)=>{let a=n.get(t.key);r.has(t.key)&&(void 0===a||a.mutation instanceof nL)?i=i.insert(t.key,t):void 0!==a?(s.set(t.key,a.mutation.getFieldMask()),nO(a.mutation,t,a.mutation.getFieldMask(),U.now())):s.set(t.key,e1.empty())}),this.recalculateAndSaveOverlays(e,i).next(e=>(e.forEach((e,t)=>s.set(e,t)),t.forEach((e,t)=>{var n;return a.set(e,new iL(t,null!=(n=s.get(e))?n:null))}),a))}recalculateAndSaveOverlays(e,t){let n=nh(),r=new eH((e,t)=>e-t),i=nf();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(e,t).next(e=>{for(let i of e)i.keys().forEach(e=>{let s=t.get(e);if(null===s)return;let a=n.get(e)||e1.empty();a=i.applyToLocalView(s,a),n.set(e,a);let o=(r.get(i.batchId)||nf()).add(e);r=r.insert(i.batchId,o)})}).next(()=>{let s=[],a=r.getReverseIterator();for(;a.hasNext();){let r=a.getNext(),o=r.key,l=r.value,u=nh();l.forEach(e=>{if(!i.has(e)){let r=nR(t.get(e),n.get(e));null!==r&&u.set(e,r),i=i.add(e)}}),s.push(this.documentOverlayCache.saveOverlays(e,o,u))}return ei.waitFor(s)}).next(()=>n)}recalculateAndSaveOverlaysForDocumentKeys(e,t){return this.remoteDocumentCache.getEntries(e,t).next(t=>this.recalculateAndSaveOverlays(e,t))}getDocumentsMatchingQuery(e,t,n,r){return $.isDocumentKey(t.path)&&null===t.collectionGroup&&0===t.filters.length?this.getDocumentsMatchingDocumentQuery(e,t.path):t5(t)?this.getDocumentsMatchingCollectionGroupQuery(e,t,n,r):this.getDocumentsMatchingCollectionQuery(e,t,n,r)}getNextDocuments(e,t,n,r){return this.remoteDocumentCache.getAllFromCollectionGroup(e,t,n,r).next(i=>{let s=r-i.size>0?this.documentOverlayCache.getOverlaysForCollectionGroup(e,t,n.largestBatchId,r-i.size):ei.resolve(nh()),a=-1,o=i;return s.next(t=>ei.forEach(t,(t,n)=>(a<n.largestBatchId&&(a=n.largestBatchId),i.get(t)?ei.resolve():this.remoteDocumentCache.getEntry(e,t).next(e=>{o=o.insert(t,e)}))).next(()=>this.populateOverlays(e,t,i)).next(()=>this.computeViews(e,o,t,nf())).next(e=>({batchId:a,changes:nu(e)})))})}getDocumentsMatchingDocumentQuery(e,t){return this.getDocument(e,new $(t)).next(e=>{let t=nl();return e.isFoundDocument()&&(t=t.insert(e.key,e)),t})}getDocumentsMatchingCollectionGroupQuery(e,t,n,r){let i=t.collectionGroup,s=nl();return this.indexManager.getCollectionParents(e,i).next(a=>ei.forEach(a,a=>{let o=new t0(a.child(i),null,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,t.endAt);return this.getDocumentsMatchingCollectionQuery(e,o,n,r).next(e=>{e.forEach((e,t)=>{s=s.insert(e,t)})})}).next(()=>s))}getDocumentsMatchingCollectionQuery(e,t,n,r){let i;return this.documentOverlayCache.getOverlaysForCollection(e,t.path,n.largestBatchId).next(s=>(i=s,this.remoteDocumentCache.getDocumentsMatchingQuery(e,t,n,i,r))).next(e=>{i.forEach((t,n)=>{let r=n.getKey();null===e.get(r)&&(e=e.insert(r,tS.newInvalidDocument(r)))});let n=nl();return e.forEach((e,r)=>{let s=i.get(e);void 0!==s&&nO(s.mutation,r,e1.empty(),U.now()),nn(t,r)&&(n=n.insert(e,r))}),n})}}class iU{constructor(e){this.serializer=e,this.hr=new Map,this.Pr=new Map}getBundleMetadata(e,t){return ei.resolve(this.hr.get(t))}saveBundleMetadata(e,t){return this.hr.set(t.id,{id:t.id,version:t.version,createTime:ro(t.createTime)}),ei.resolve()}getNamedQuery(e,t){return ei.resolve(this.Pr.get(t))}saveNamedQuery(e,t){return this.Pr.set(t.name,{name:t.name,query:rF(t.bundledQuery),readTime:ro(t.readTime)}),ei.resolve()}}class iq{constructor(){this.overlays=new eH($.comparator),this.Ir=new Map}getOverlay(e,t){return ei.resolve(this.overlays.get(t))}getOverlays(e,t){let n=nh();return ei.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&n.set(t,e)})).next(()=>n)}saveOverlays(e,t,n){return n.forEach((n,r)=>{this.ht(e,t,r)}),ei.resolve()}removeOverlaysForBatchId(e,t,n){let r=this.Ir.get(n);return void 0!==r&&(r.forEach(e=>this.overlays=this.overlays.remove(e)),this.Ir.delete(n)),ei.resolve()}getOverlaysForCollection(e,t,n){let r=nh(),i=t.length+1,s=new $(t.child("")),a=this.overlays.getIteratorFrom(s);for(;a.hasNext();){let e=a.getNext().value,s=e.getKey();if(!t.isPrefixOf(s.path))break;s.path.length===i&&e.largestBatchId>n&&r.set(e.getKey(),e)}return ei.resolve(r)}getOverlaysForCollectionGroup(e,t,n,r){let i=new eH((e,t)=>e-t),s=this.overlays.getIterator();for(;s.hasNext();){let e=s.getNext().value;if(e.getKey().getCollectionGroup()===t&&e.largestBatchId>n){let t=i.get(e.largestBatchId);null===t&&(t=nh(),i=i.insert(e.largestBatchId,t)),t.set(e.getKey(),e)}}let a=nh(),o=i.getIterator();for(;o.hasNext()&&(o.getNext().value.forEach((e,t)=>a.set(e,t)),!(a.size()>=r)););return ei.resolve(a)}ht(e,t,n){let r=this.overlays.get(n.key);if(null!==r){let e=this.Ir.get(r.largestBatchId).delete(n.key);this.Ir.set(r.largestBatchId,e)}this.overlays=this.overlays.insert(n.key,new n$(t,n));let i=this.Ir.get(t);void 0===i&&(i=nf(),this.Ir.set(t,i)),this.Ir.set(t,i.add(n.key))}}class iB{constructor(){this.sessionToken=e5.EMPTY_BYTE_STRING}getSessionToken(e){return ei.resolve(this.sessionToken)}setSessionToken(e,t){return this.sessionToken=t,ei.resolve()}}class iK{constructor(){this.Tr=new eX(iz.Er),this.dr=new eX(iz.Ar)}isEmpty(){return this.Tr.isEmpty()}addReference(e,t){let n=new iz(e,t);this.Tr=this.Tr.add(n),this.dr=this.dr.add(n)}Rr(e,t){e.forEach(e=>this.addReference(e,t))}removeReference(e,t){this.Vr(new iz(e,t))}mr(e,t){e.forEach(e=>this.removeReference(e,t))}gr(e){let t=new $(new K([])),n=new iz(t,e),r=new iz(t,e+1),i=[];return this.dr.forEachInRange([n,r],e=>{this.Vr(e),i.push(e.key)}),i}pr(){this.Tr.forEach(e=>this.Vr(e))}Vr(e){this.Tr=this.Tr.delete(e),this.dr=this.dr.delete(e)}yr(e){let t=new $(new K([])),n=new iz(t,e),r=new iz(t,e+1),i=nf();return this.dr.forEachInRange([n,r],e=>{i=i.add(e.key)}),i}containsKey(e){let t=new iz(e,0),n=this.Tr.firstAfterOrEqual(t);return null!==n&&e.isEqual(n.key)}}class iz{constructor(e,t){this.key=e,this.wr=t}static Er(e,t){return $.comparator(e.key,t.key)||L(e.wr,t.wr)}static Ar(e,t){return L(e.wr,t.wr)||$.comparator(e.key,t.key)}}class iG{constructor(e,t){this.indexManager=e,this.referenceDelegate=t,this.mutationQueue=[],this.Sr=1,this.br=new eX(iz.Er)}checkEmpty(e){return ei.resolve(0===this.mutationQueue.length)}addMutationBatch(e,t,n,r){let i=this.Sr;this.Sr++,this.mutationQueue.length>0&&this.mutationQueue[this.mutationQueue.length-1];let s=new nz(i,t,n,r);for(let t of(this.mutationQueue.push(s),r))this.br=this.br.add(new iz(t.key,i)),this.indexManager.addToCollectionParentIndex(e,t.key.path.popLast());return ei.resolve(s)}lookupMutationBatch(e,t){return ei.resolve(this.Dr(t))}getNextMutationBatchAfterBatchId(e,t){let n=this.vr(t+1),r=n<0?0:n;return ei.resolve(this.mutationQueue.length>r?this.mutationQueue[r]:null)}getHighestUnacknowledgedBatchId(){return ei.resolve(0===this.mutationQueue.length?-1:this.Sr-1)}getAllMutationBatches(e){return ei.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(e,t){let n=new iz(t,0),r=new iz(t,Number.POSITIVE_INFINITY),i=[];return this.br.forEachInRange([n,r],e=>{let t=this.Dr(e.wr);i.push(t)}),ei.resolve(i)}getAllMutationBatchesAffectingDocumentKeys(e,t){let n=new eX(L);return t.forEach(e=>{let t=new iz(e,0),r=new iz(e,Number.POSITIVE_INFINITY);this.br.forEachInRange([t,r],e=>{n=n.add(e.wr)})}),ei.resolve(this.Cr(n))}getAllMutationBatchesAffectingQuery(e,t){let n=t.path,r=n.length+1,i=n;$.isDocumentKey(i)||(i=i.child(""));let s=new iz(new $(i),0),a=new eX(L);return this.br.forEachWhile(e=>{let t=e.key.path;return!!n.isPrefixOf(t)&&(t.length===r&&(a=a.add(e.wr)),!0)},s),ei.resolve(this.Cr(a))}Cr(e){let t=[];return e.forEach(e=>{let n=this.Dr(e);null!==n&&t.push(n)}),t}removeMutationBatch(e,t){0===this.Fr(t.batchId,"removed")||b(),this.mutationQueue.shift();let n=this.br;return ei.forEach(t.mutations,r=>{let i=new iz(r.key,t.batchId);return n=n.delete(i),this.referenceDelegate.markPotentiallyOrphaned(e,r.key)}).next(()=>{this.br=n})}On(e){}containsKey(e,t){let n=new iz(t,0),r=this.br.firstAfterOrEqual(n);return ei.resolve(t.isEqual(r&&r.key))}performConsistencyCheck(e){return this.mutationQueue.length,ei.resolve()}Fr(e,t){return this.vr(e)}vr(e){return 0===this.mutationQueue.length?0:e-this.mutationQueue[0].batchId}Dr(e){let t=this.vr(e);return t<0||t>=this.mutationQueue.length?null:this.mutationQueue[t]}}class i${constructor(e){this.Mr=e,this.docs=new eH($.comparator),this.size=0}setIndexManager(e){this.indexManager=e}addEntry(e,t){let n=t.key,r=this.docs.get(n),i=r?r.size:0,s=this.Mr(t);return this.docs=this.docs.insert(n,{document:t.mutableCopy(),size:s}),this.size+=s-i,this.indexManager.addToCollectionParentIndex(e,n.path.popLast())}removeEntry(e){let t=this.docs.get(e);t&&(this.docs=this.docs.remove(e),this.size-=t.size)}getEntry(e,t){let n=this.docs.get(t);return ei.resolve(n?n.document.mutableCopy():tS.newInvalidDocument(t))}getEntries(e,t){let n=na;return t.forEach(e=>{let t=this.docs.get(e);n=n.insert(e,t?t.document.mutableCopy():tS.newInvalidDocument(e))}),ei.resolve(n)}getDocumentsMatchingQuery(e,t,n,r){let i=na,s=t.path,a=new $(s.child("")),o=this.docs.getIteratorFrom(a);for(;o.hasNext();){let{key:e,value:{document:a}}=o.getNext();if(!s.isPrefixOf(e.path))break;e.path.length>s.length+1||0>=ee(X(a),n)||(r.has(a.key)||nn(t,a))&&(i=i.insert(a.key,a.mutableCopy()))}return ei.resolve(i)}getAllFromCollectionGroup(e,t,n,r){b()}Or(e,t){return ei.forEach(this.docs,e=>t(e))}newChangeBuffer(e){return new iQ(this)}getSize(e){return ei.resolve(this.size)}}class iQ extends iN{constructor(e){super(),this.cr=e}applyChanges(e){let t=[];return this.changes.forEach((n,r)=>{r.isValidDocument()?t.push(this.cr.addEntry(e,r)):this.cr.removeEntry(n)}),ei.waitFor(t)}getFromCache(e,t){return this.cr.getEntry(e,t)}getAllFromCache(e,t){return this.cr.getEntries(e,t)}}class ij{constructor(e){this.persistence=e,this.Nr=new ns(e=>tW(e),tH),this.lastRemoteSnapshotVersion=q.min(),this.highestTargetId=0,this.Lr=0,this.Br=new iK,this.targetCount=0,this.kr=iw.Bn()}forEachTarget(e,t){return this.Nr.forEach((e,n)=>t(n)),ei.resolve()}getLastRemoteSnapshotVersion(e){return ei.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(e){return ei.resolve(this.Lr)}allocateTargetId(e){return this.highestTargetId=this.kr.next(),ei.resolve(this.highestTargetId)}setTargetsMetadata(e,t,n){return n&&(this.lastRemoteSnapshotVersion=n),t>this.Lr&&(this.Lr=t),ei.resolve()}Kn(e){this.Nr.set(e.target,e);let t=e.targetId;t>this.highestTargetId&&(this.kr=new iw(t),this.highestTargetId=t),e.sequenceNumber>this.Lr&&(this.Lr=e.sequenceNumber)}addTargetData(e,t){return this.Kn(t),this.targetCount+=1,ei.resolve()}updateTargetData(e,t){return this.Kn(t),ei.resolve()}removeTargetData(e,t){return this.Nr.delete(t.target),this.Br.gr(t.targetId),this.targetCount-=1,ei.resolve()}removeTargets(e,t,n){let r=0,i=[];return this.Nr.forEach((s,a)=>{a.sequenceNumber<=t&&null===n.get(a.targetId)&&(this.Nr.delete(s),i.push(this.removeMatchingKeysForTargetId(e,a.targetId)),r++)}),ei.waitFor(i).next(()=>r)}getTargetCount(e){return ei.resolve(this.targetCount)}getTargetData(e,t){let n=this.Nr.get(t)||null;return ei.resolve(n)}addMatchingKeys(e,t,n){return this.Br.Rr(t,n),ei.resolve()}removeMatchingKeys(e,t,n){this.Br.mr(t,n);let r=this.persistence.referenceDelegate,i=[];return r&&t.forEach(t=>{i.push(r.markPotentiallyOrphaned(e,t))}),ei.waitFor(i)}removeMatchingKeysForTargetId(e,t){return this.Br.gr(t),ei.resolve()}getMatchingKeysForTargetId(e,t){let n=this.Br.yr(t);return ei.resolve(n)}containsKey(e,t){return ei.resolve(this.Br.containsKey(t))}}class iW{constructor(e,t){this.qr={},this.overlays={},this.Qr=new ey(0),this.Kr=!1,this.Kr=!0,this.$r=new iB,this.referenceDelegate=e(this),this.Ur=new ij(this),this.indexManager=new r9,this.remoteDocumentCache=new i$(e=>this.referenceDelegate.Wr(e)),this.serializer=new rC(t),this.Gr=new iU(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.Kr=!1,Promise.resolve()}get started(){return this.Kr}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(e){return this.indexManager}getDocumentOverlayCache(e){let t=this.overlays[e.toKey()];return t||(t=new iq,this.overlays[e.toKey()]=t),t}getMutationQueue(e,t){let n=this.qr[e.toKey()];return n||(n=new iG(t,this.referenceDelegate),this.qr[e.toKey()]=n),n}getGlobalsCache(){return this.$r}getTargetCache(){return this.Ur}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.Gr}runTransaction(e,t,n){I("MemoryPersistence","Starting transaction:",e);let r=new iH(this.Qr.next());return this.referenceDelegate.zr(),n(r).next(e=>this.referenceDelegate.jr(r).next(()=>e)).toPromise().then(e=>(r.raiseOnCommittedEvent(),e))}Hr(e,t){return ei.or(Object.values(this.qr).map(n=>()=>n.containsKey(e,t)))}}class iH extends en{constructor(e){super(),this.currentSequenceNumber=e}}class iJ{constructor(e){this.persistence=e,this.Jr=new iK,this.Yr=null}static Zr(e){return new iJ(e)}get Xr(){if(this.Yr)return this.Yr;throw b()}addReference(e,t,n){return this.Jr.addReference(n,t),this.Xr.delete(n.toString()),ei.resolve()}removeReference(e,t,n){return this.Jr.removeReference(n,t),this.Xr.add(n.toString()),ei.resolve()}markPotentiallyOrphaned(e,t){return this.Xr.add(t.toString()),ei.resolve()}removeTarget(e,t){this.Jr.gr(t.targetId).forEach(e=>this.Xr.add(e.toString()));let n=this.persistence.getTargetCache();return n.getMatchingKeysForTargetId(e,t.targetId).next(e=>{e.forEach(e=>this.Xr.add(e.toString()))}).next(()=>n.removeTargetData(e,t))}zr(){this.Yr=new Set}jr(e){let t=this.persistence.getRemoteDocumentCache().newChangeBuffer();return ei.forEach(this.Xr,n=>{let r=$.fromPath(n);return this.ei(e,r).next(e=>{e||t.removeEntry(r,q.min())})}).next(()=>(this.Yr=null,t.apply(e)))}updateLimboDocument(e,t){return this.ei(e,t).next(e=>{e?this.Xr.delete(t.toString()):this.Xr.add(t.toString())})}Wr(e){return 0}ei(e,t){return ei.or([()=>ei.resolve(this.Jr.containsKey(t)),()=>this.persistence.getTargetCache().containsKey(e,t),()=>this.persistence.Hr(e,t)])}}class iY{constructor(e,t){this.persistence=e,this.ti=new ns(e=>eT(e.path),(e,t)=>e.isEqual(t)),this.garbageCollector=new ix(this,t)}static Zr(e,t){return new iY(e,t)}zr(){}jr(e){return ei.resolve()}forEachTarget(e,t){return this.persistence.getTargetCache().forEachTarget(e,t)}Yn(e){let t=this.er(e);return this.persistence.getTargetCache().getTargetCount(e).next(e=>t.next(t=>e+t))}er(e){let t=0;return this.Zn(e,e=>{t++}).next(()=>t)}Zn(e,t){return ei.forEach(this.ti,(n,r)=>this.nr(e,n,r).next(e=>e?ei.resolve():t(r)))}removeTargets(e,t,n){return this.persistence.getTargetCache().removeTargets(e,t,n)}removeOrphanedDocuments(e,t){let n=0,r=this.persistence.getRemoteDocumentCache(),i=r.newChangeBuffer();return r.Or(e,r=>this.nr(e,r,t).next(e=>{e||(n++,i.removeEntry(r,q.min()))})).next(()=>i.apply(e)).next(()=>n)}markPotentiallyOrphaned(e,t){return this.ti.set(t,e.currentSequenceNumber),ei.resolve()}removeTarget(e,t){let n=t.withSequenceNumber(e.currentSequenceNumber);return this.persistence.getTargetCache().updateTargetData(e,n)}addReference(e,t,n){return this.ti.set(n,e.currentSequenceNumber),ei.resolve()}removeReference(e,t,n){return this.ti.set(n,e.currentSequenceNumber),ei.resolve()}updateLimboDocument(e,t){return this.ti.set(t,e.currentSequenceNumber),ei.resolve()}Wr(e){let t=e.key.toString().length;return e.isFoundDocument()&&(t+=function e(t){switch(ts(t)){case 0:case 1:return 4;case 2:return 8;case 3:case 8:return 16;case 4:let n=e7(t);return n?16+e(n):16;case 5:return 2*t.stringValue.length;case 6:return e6(t.bytesValue).approximateByteSize();case 7:return t.referenceValue.length;case 9:return(t.arrayValue.values||[]).reduce((t,n)=>t+e(n),0);case 10:case 11:var r;let i;return r=t.mapValue,i=0,ej(r.fields,(t,n)=>{i+=t.length+e(n)}),i;default:throw b()}}(e.data.value)),t}nr(e,t,n){return ei.or([()=>this.persistence.Hr(e,t),()=>this.persistence.getTargetCache().containsKey(e,t),()=>{let e=this.ti.get(t);return ei.resolve(void 0!==e&&e>n)}])}getCacheSize(e){return this.persistence.getRemoteDocumentCache().getSize(e)}}class iX{constructor(e){this.serializer=e}O(e,t,n,r){let i=new es("createOrUpgrade",t);n<1&&r>=1&&(e.createObjectStore("owner"),e.createObjectStore("mutationQueues",{keyPath:"userId"}),e.createObjectStore("mutations",{keyPath:"batchId",autoIncrement:!0}).createIndex("userMutationsIndex",eE,{unique:!0}),e.createObjectStore("documentMutations"),iZ(e),e.createObjectStore("remoteDocuments"));let s=ei.resolve();return n<3&&r>=3&&(0!==n&&(e.deleteObjectStore("targetDocuments"),e.deleteObjectStore("targets"),e.deleteObjectStore("targetGlobal"),iZ(e)),s=s.next(()=>(function(e){let t=e.store("targetGlobal"),n={highestTargetId:0,highestListenSequenceNumber:0,lastRemoteSnapshotVersion:q.min().toTimestamp(),targetCount:0};return t.put("targetGlobalKey",n)})(i))),n<4&&r>=4&&(0!==n&&(s=s.next(()=>i.store("mutations").U().next(t=>{e.deleteObjectStore("mutations"),e.createObjectStore("mutations",{keyPath:"batchId",autoIncrement:!0}).createIndex("userMutationsIndex",eE,{unique:!0});let n=i.store("mutations"),r=t.map(e=>n.put(e));return ei.waitFor(r)}))),s=s.next(()=>{e.createObjectStore("clientMetadata",{keyPath:"clientId"})})),n<5&&r>=5&&(s=s.next(()=>this.ni(i))),n<6&&r>=6&&(s=s.next(()=>(e.createObjectStore("remoteDocumentGlobal"),this.ri(i)))),n<7&&r>=7&&(s=s.next(()=>this.ii(i))),n<8&&r>=8&&(s=s.next(()=>this.si(e,i))),n<9&&r>=9&&(s=s.next(()=>{e.objectStoreNames.contains("remoteDocumentChanges")&&e.deleteObjectStore("remoteDocumentChanges")})),n<10&&r>=10&&(s=s.next(()=>this.oi(i))),n<11&&r>=11&&(s=s.next(()=>{e.createObjectStore("bundles",{keyPath:"bundleId"}),e.createObjectStore("namedQueries",{keyPath:"name"})})),n<12&&r>=12&&(s=s.next(()=>{let t=e.createObjectStore("documentOverlays",{keyPath:eF});t.createIndex("collectionPathOverlayIndex",eL,{unique:!1}),t.createIndex("collectionGroupOverlayIndex",eP,{unique:!1})})),n<13&&r>=13&&(s=s.next(()=>(function(e){let t=e.createObjectStore("remoteDocumentsV14",{keyPath:eS});t.createIndex("documentKeyIndex",ex),t.createIndex("collectionGroupIndex",eD)})(e)).next(()=>this._i(e,i)).next(()=>e.deleteObjectStore("remoteDocuments"))),n<14&&r>=14&&(s=s.next(()=>this.ai(e,i))),n<15&&r>=15&&(s=s.next(()=>{e.createObjectStore("indexConfiguration",{keyPath:"indexId",autoIncrement:!0}).createIndex("collectionGroupIndex","collectionGroup",{unique:!1}),e.createObjectStore("indexState",{keyPath:eV}).createIndex("sequenceNumberIndex",eR,{unique:!1}),e.createObjectStore("indexEntries",{keyPath:eO}).createIndex("documentKeyIndex",eM,{unique:!1})})),n<16&&r>=16&&(s=s.next(()=>{t.objectStore("indexState").clear()}).next(()=>{t.objectStore("indexEntries").clear()})),n<17&&r>=17&&(s=s.next(()=>{e.createObjectStore("globals",{keyPath:"name"})})),s}ri(e){let t=0;return e.store("remoteDocuments").J((e,n)=>{t+=ic(n)}).next(()=>{let n={byteSize:t};return e.store("remoteDocumentGlobal").put("remoteDocumentGlobalKey",n)})}ni(e){let t=e.store("mutationQueues"),n=e.store("mutations");return t.U().next(t=>ei.forEach(t,t=>{let r=IDBKeyRange.bound([t.userId,-1],[t.userId,t.lastAcknowledgedBatchId]);return n.U("userMutationsIndex",r).next(n=>ei.forEach(n,n=>{n.userId===t.userId||b();let r=rR(this.serializer,n);return ih(e,t.userId,r).next(()=>{})}))}))}ii(e){let t=e.store("targetDocuments"),n=e.store("remoteDocuments");return e.store("targetGlobal").get("targetGlobalKey").next(e=>{let r=[];return n.J((n,i)=>{let s=new K(n),a=[0,eT(s)];r.push(t.get(a).next(n=>n?ei.resolve():t.put({targetId:0,path:eT(s),sequenceNumber:e.highestListenSequenceNumber})))}).next(()=>ei.waitFor(r))})}si(e,t){e.createObjectStore("collectionParents",{keyPath:ek});let n=t.store("collectionParents"),r=new r7,i=e=>{if(r.add(e)){let t=e.lastSegment(),r=e.popLast();return n.put({collectionId:t,parent:eT(r)})}};return t.store("remoteDocuments").J({H:!0},(e,t)=>i(new K(e).popLast())).next(()=>t.store("documentMutations").J({H:!0},([e,t,n],r)=>i(e_(t).popLast())))}oi(e){let t=e.store("targets");return t.J((e,n)=>{let r=rO(n),i=rM(this.serializer,r);return t.put(i)})}_i(e,t){let n=t.store("remoteDocuments"),r=[];return n.J((e,n)=>{let i=t.store("remoteDocumentsV14"),s=(n.document?new $(K.fromString(n.document.name).popFirst(5)):n.noDocument?$.fromSegments(n.noDocument.path):n.unknownDocument?$.fromSegments(n.unknownDocument.path):b()).path.toArray(),a={prefixPath:s.slice(0,s.length-2),collectionGroup:s[s.length-2],documentId:s[s.length-1],readTime:n.readTime||[0,0],unknownDocument:n.unknownDocument,noDocument:n.noDocument,document:n.document,hasCommittedMutations:!!n.hasCommittedMutations};r.push(i.put(a))}).next(()=>ei.waitFor(r))}ai(e,t){let n=t.store("mutations"),r=new iA(this.serializer),i=new iW(iJ.Zr,this.serializer.ct);return n.U().next(e=>{let n=new Map;return e.forEach(e=>{var t;let r=null!=(t=n.get(e.userId))?t:nf();rR(this.serializer,e).keys().forEach(e=>r=r.add(e)),n.set(e.userId,r)}),ei.forEach(n,(e,n)=>{let s=new p(n),a=rz.lt(this.serializer,s),o=i.getIndexManager(s);return new iP(r,id.lt(s,this.serializer,o,i.referenceDelegate),a,o).recalculateAndSaveOverlaysForDocumentKeys(new eG(t,ey.oe),e).next()})})}}function iZ(e){e.createObjectStore("targetDocuments",{keyPath:eN}).createIndex("documentTargetsIndex",eA,{unique:!0}),e.createObjectStore("targets",{keyPath:"targetId"}).createIndex("queryTargetsIndex",eC,{unique:!0}),e.createObjectStore("targetGlobal")}let i0="Failed to obtain exclusive access to the persistence layer. To allow shared access, multi-tab synchronization has to be enabled in all tabs. If you are using `experimentalForceOwningTab:true`, make sure that only one tab has persistence enabled at any given time.";class i1{constructor(e,t,n,r,i,s,a,o,l,u,h=17){if(this.allowTabSynchronization=e,this.persistenceKey=t,this.clientId=n,this.ui=i,this.window=s,this.document=a,this.ci=l,this.li=u,this.hi=h,this.Qr=null,this.Kr=!1,this.isPrimary=!1,this.networkEnabled=!0,this.Pi=null,this.inForeground=!1,this.Ii=null,this.Ti=null,this.Ei=Number.NEGATIVE_INFINITY,this.di=e=>Promise.resolve(),!i1.D())throw new x(S.UNIMPLEMENTED,"This platform is either missing IndexedDB or is known to have an incomplete implementation. Offline persistence has been disabled.");this.referenceDelegate=new iD(this,r),this.Ai=t+"main",this.serializer=new rC(o),this.Ri=new ea(this.Ai,this.hi,new iX(this.serializer)),this.$r=new r$,this.Ur=new iv(this.referenceDelegate,this.serializer),this.remoteDocumentCache=new iA(this.serializer),this.Gr=new rq,this.window&&this.window.localStorage?this.Vi=this.window.localStorage:(this.Vi=null,!1===u&&T("IndexedDbPersistence","LocalStorage is unavailable. As a result, persistence may not work reliably. In particular enablePersistence() could fail immediately after refreshing the page."))}start(){return this.mi().then(()=>{if(!this.isPrimary&&!this.allowTabSynchronization)throw new x(S.FAILED_PRECONDITION,i0);return this.fi(),this.gi(),this.pi(),this.runTransaction("getHighestListenSequenceNumber","readonly",e=>this.Ur.getHighestSequenceNumber(e))}).then(e=>{this.Qr=new ey(e,this.ci)}).then(()=>{this.Kr=!0}).catch(e=>(this.Ri&&this.Ri.close(),Promise.reject(e)))}yi(e){return this.di=async t=>{if(this.started)return e(t)},e(this.isPrimary)}setDatabaseDeletedListener(e){this.Ri.L(async t=>{null===t.newVersion&&await e()})}setNetworkEnabled(e){this.networkEnabled!==e&&(this.networkEnabled=e,this.ui.enqueueAndForget(async()=>{this.started&&await this.mi()}))}mi(){return this.runTransaction("updateClientMetadataAndTryBecomePrimary","readwrite",e=>i5(e).put({clientId:this.clientId,updateTimeMs:Date.now(),networkEnabled:this.networkEnabled,inForeground:this.inForeground}).next(()=>{if(this.isPrimary)return this.wi(e).next(e=>{e||(this.isPrimary=!1,this.ui.enqueueRetryable(()=>this.di(!1)))})}).next(()=>this.Si(e)).next(t=>this.isPrimary&&!t?this.bi(e).next(()=>!1):!!t&&this.Di(e).next(()=>!0))).catch(e=>{if(eh(e))return I("IndexedDbPersistence","Failed to extend owner lease: ",e),this.isPrimary;if(!this.allowTabSynchronization)throw e;return I("IndexedDbPersistence","Releasing owner lease after error during lease refresh",e),!1}).then(e=>{this.isPrimary!==e&&this.ui.enqueueRetryable(()=>this.di(e)),this.isPrimary=e})}wi(e){return i2(e).get("owner").next(e=>ei.resolve(this.vi(e)))}Ci(e){return i5(e).delete(this.clientId)}async Fi(){if(this.isPrimary&&!this.Mi(this.Ei,18e5)){this.Ei=Date.now();let e=await this.runTransaction("maybeGarbageCollectMultiClientState","readwrite-primary",e=>{let t=e$(e,"clientMetadata");return t.U().next(e=>{let n=this.xi(e,18e5),r=e.filter(e=>-1===n.indexOf(e));return ei.forEach(r,e=>t.delete(e.clientId)).next(()=>r)})}).catch(()=>[]);if(this.Vi)for(let t of e)this.Vi.removeItem(this.Oi(t.clientId))}}pi(){this.Ti=this.ui.enqueueAfterDelay("client_metadata_refresh",4e3,()=>this.mi().then(()=>this.Fi()).then(()=>this.pi()))}vi(e){return!!e&&e.ownerId===this.clientId}Si(e){return this.li?ei.resolve(!0):i2(e).get("owner").next(t=>{if(null!==t&&this.Mi(t.leaseTimestampMs,5e3)&&!this.Ni(t.ownerId)){if(this.vi(t)&&this.networkEnabled)return!0;if(!this.vi(t)){if(!t.allowTabSynchronization)throw new x(S.FAILED_PRECONDITION,i0);return!1}}return!(!this.networkEnabled||!this.inForeground)||i5(e).U().next(e=>void 0===this.xi(e,5e3).find(e=>{if(this.clientId!==e.clientId){let t=!this.networkEnabled&&e.networkEnabled,n=!this.inForeground&&e.inForeground,r=this.networkEnabled===e.networkEnabled;if(t||n&&r)return!0}return!1}))}).next(e=>(this.isPrimary!==e&&I("IndexedDbPersistence",`Client ${e?"is":"is not"} eligible for a primary lease.`),e))}async shutdown(){this.Kr=!1,this.Li(),this.Ti&&(this.Ti.cancel(),this.Ti=null),this.Bi(),this.ki(),await this.Ri.runTransaction("shutdown","readwrite",["owner","clientMetadata"],e=>{let t=new eG(e,ey.oe);return this.bi(t).next(()=>this.Ci(t))}),this.Ri.close(),this.qi()}xi(e,t){return e.filter(e=>this.Mi(e.updateTimeMs,t)&&!this.Ni(e.clientId))}Qi(){return this.runTransaction("getActiveClients","readonly",e=>i5(e).U().next(e=>this.xi(e,18e5).map(e=>e.clientId)))}get started(){return this.Kr}getGlobalsCache(){return this.$r}getMutationQueue(e,t){return id.lt(e,this.serializer,t,this.referenceDelegate)}getTargetCache(){return this.Ur}getRemoteDocumentCache(){return this.remoteDocumentCache}getIndexManager(e){return new it(e,this.serializer.ct.databaseId)}getDocumentOverlayCache(e){return rz.lt(this.serializer,e)}getBundleCache(){return this.Gr}runTransaction(e,t,n){var r;let i;I("IndexedDbPersistence","Starting transaction:",e);let s=17===(r=this.hi)?ez:16===r||15===r?eK:14===r||13===r?eB:12===r?eq:11===r?eU:void b();return this.Ri.runTransaction(e,"readonly"===t?"readonly":"readwrite",s,r=>(i=new eG(r,this.Qr?this.Qr.next():ey.oe),"readwrite-primary"===t?this.wi(i).next(e=>!!e||this.Si(i)).next(t=>{if(!t)throw T(`Failed to obtain primary lease for action '${e}'.`),this.isPrimary=!1,this.ui.enqueueRetryable(()=>this.di(!1)),new x(S.FAILED_PRECONDITION,et);return n(i)}).next(e=>this.Di(i).next(()=>e)):this.Ki(i).next(()=>n(i)))).then(e=>(i.raiseOnCommittedEvent(),e))}Ki(e){return i2(e).get("owner").next(e=>{if(null!==e&&this.Mi(e.leaseTimestampMs,5e3)&&!this.Ni(e.ownerId)&&!this.vi(e)&&!(this.li||this.allowTabSynchronization&&e.allowTabSynchronization))throw new x(S.FAILED_PRECONDITION,i0)})}Di(e){let t={ownerId:this.clientId,allowTabSynchronization:this.allowTabSynchronization,leaseTimestampMs:Date.now()};return i2(e).put("owner",t)}static D(){return ea.D()}bi(e){let t=i2(e);return t.get("owner").next(e=>this.vi(e)?(I("IndexedDbPersistence","Releasing primary lease."),t.delete("owner")):ei.resolve())}Mi(e,t){let n=Date.now();return!(e<n-t)&&(!(e>n)||(T(`Detected an update time that is in the future: ${e} > ${n}`),!1))}fi(){null!==this.document&&"function"==typeof this.document.addEventListener&&(this.Ii=()=>{this.ui.enqueueAndForget(()=>(this.inForeground="visible"===this.document.visibilityState,this.mi()))},this.document.addEventListener("visibilitychange",this.Ii),this.inForeground="visible"===this.document.visibilityState)}Bi(){this.Ii&&(this.document.removeEventListener("visibilitychange",this.Ii),this.Ii=null)}gi(){var e;"function"==typeof(null==(e=this.window)?void 0:e.addEventListener)&&(this.Pi=()=>{this.Li();let e=/(?:Version|Mobile)\/1[456]/;(0,h.nr)()&&(navigator.appVersion.match(e)||navigator.userAgent.match(e))&&this.ui.enterRestrictedMode(!0),this.ui.enqueueAndForget(()=>this.shutdown())},this.window.addEventListener("pagehide",this.Pi))}ki(){this.Pi&&(this.window.removeEventListener("pagehide",this.Pi),this.Pi=null)}Ni(e){var t;try{let n=null!==(null==(t=this.Vi)?void 0:t.getItem(this.Oi(e)));return I("IndexedDbPersistence",`Client '${e}' ${n?"is":"is not"} zombied in LocalStorage`),n}catch(e){return T("IndexedDbPersistence","Failed to get zombied client id.",e),!1}}Li(){if(this.Vi)try{this.Vi.setItem(this.Oi(this.clientId),String(Date.now()))}catch(e){T("Failed to set zombie client id.",e)}}qi(){if(this.Vi)try{this.Vi.removeItem(this.Oi(this.clientId))}catch(e){}}Oi(e){return`firestore_zombie_${this.persistenceKey}_${e}`}}function i2(e){return e$(e,"owner")}function i5(e){return e$(e,"clientMetadata")}function i3(e,t){let n=e.projectId;return e.isDefaultDatabase||(n+="."+e.database),"firestore/"+t+"/"+n+"/"}class i4{constructor(e,t,n,r){this.targetId=e,this.fromCache=t,this.$i=n,this.Ui=r}static Wi(e,t){let n=nf(),r=nf();for(let e of t.docChanges)switch(e.type){case 0:n=n.add(e.doc.key);break;case 1:r=r.add(e.doc.key)}return new i4(e,t.fromCache,n,r)}}class i8{constructor(){this._documentReadCount=0}get documentReadCount(){return this._documentReadCount}incrementDocumentReadCount(e){this._documentReadCount+=e}}class i6{constructor(){this.Gi=!1,this.zi=!1,this.ji=100,this.Hi=(0,h.nr)()?8:eo((0,h.ZQ)())>0?6:4}initialize(e,t){this.Ji=e,this.indexManager=t,this.Gi=!0}getDocumentsMatchingQuery(e,t,n,r){let i={result:null};return this.Yi(e,t).next(e=>{i.result=e}).next(()=>{if(!i.result)return this.Zi(e,t,r,n).next(e=>{i.result=e})}).next(()=>{if(i.result)return;let n=new i8;return this.Xi(e,t,n).next(r=>{if(i.result=r,this.zi)return this.es(e,t,n,r.size)})}).next(()=>i.result)}es(e,t,n,r){return n.documentReadCount<this.ji?(v()<=u.$b.DEBUG&&I("QueryEngine","SDK will not create cache indexes for query:",nt(t),"since it only creates cache indexes for collection contains","more than or equal to",this.ji,"documents"),ei.resolve()):(v()<=u.$b.DEBUG&&I("QueryEngine","Query:",nt(t),"scans",n.documentReadCount,"local documents and returns",r,"documents as results."),n.documentReadCount>this.Hi*r?(v()<=u.$b.DEBUG&&I("QueryEngine","The SDK decides to create cache indexes for query:",nt(t),"as using cache indexes may help improve performance."),this.indexManager.createTargetIndexes(e,t4(t))):ei.resolve())}Yi(e,t){if(t2(t))return ei.resolve(null);let n=t4(t);return this.indexManager.getIndexType(e,n).next(r=>0===r?null:(null!==t.limit&&1===r&&(n=t4(t=t9(t,null,"F"))),this.indexManager.getDocumentsMatchingTarget(e,n).next(r=>{let i=nf(...r);return this.Ji.getDocuments(e,i).next(r=>this.indexManager.getMinOffset(e,n).next(n=>{let s=this.ts(t,r);return this.ns(t,s,i,n.readTime)?this.Yi(e,t9(t,null,"F")):this.rs(e,s,t,n)}))})))}Zi(e,t,n,r){return t2(t)||r.isEqual(q.min())?ei.resolve(null):this.Ji.getDocuments(e,n).next(i=>{let s=this.ts(t,i);return this.ns(t,s,n,r)?ei.resolve(null):(v()<=u.$b.DEBUG&&I("QueryEngine","Re-using previous result from %s to execute query: %s",r.toString(),nt(t)),this.rs(e,s,t,Y(r,-1)).next(e=>e))})}ts(e,t){let n=new eX(ni(e));return t.forEach((t,r)=>{nn(e,r)&&(n=n.add(r))}),n}ns(e,t,n,r){if(null===e.limit)return!1;if(n.size!==t.size)return!0;let i="F"===e.limitType?t.last():t.first();return!!i&&(i.hasPendingWrites||i.version.compareTo(r)>0)}Xi(e,t,n){return v()<=u.$b.DEBUG&&I("QueryEngine","Using full collection scan to execute query:",nt(t)),this.Ji.getDocumentsMatchingQuery(e,t,Z.min(),n)}rs(e,t,n,r){return this.Ji.getDocumentsMatchingQuery(e,n,r).next(e=>(t.forEach(t=>{e=e.insert(t.key,t)}),e))}}class i9{constructor(e,t,n,r){this.persistence=e,this.ss=t,this.serializer=r,this.os=new eH(L),this._s=new ns(e=>tW(e),tH),this.us=new Map,this.cs=e.getRemoteDocumentCache(),this.Ur=e.getTargetCache(),this.Gr=e.getBundleCache(),this.ls(n)}ls(e){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(e),this.indexManager=this.persistence.getIndexManager(e),this.mutationQueue=this.persistence.getMutationQueue(e,this.indexManager),this.localDocuments=new iP(this.cs,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.cs.setIndexManager(this.indexManager),this.ss.initialize(this.localDocuments,this.indexManager)}collectGarbage(e){return this.persistence.runTransaction("Collect garbage","readwrite-primary",t=>e.collect(t,this.os))}}async function i7(e,t){return await e.persistence.runTransaction("Handle user change","readonly",n=>{let r;return e.mutationQueue.getAllMutationBatches(n).next(i=>(r=i,e.ls(t),e.mutationQueue.getAllMutationBatches(n))).next(t=>{let i=[],s=[],a=nf();for(let e of r)for(let t of(i.push(e.batchId),e.mutations))a=a.add(t.key);for(let e of t)for(let t of(s.push(e.batchId),e.mutations))a=a.add(t.key);return e.localDocuments.getDocuments(n,a).next(e=>({hs:e,removedBatchIds:i,addedBatchIds:s}))})})}function se(e){return e.persistence.runTransaction("Get last remote snapshot version","readonly",t=>e.Ur.getLastRemoteSnapshotVersion(t))}function st(e,t,n){let r=nf(),i=nf();return n.forEach(e=>r=r.add(e)),t.getEntries(e,r).next(e=>{let r=na;return n.forEach((n,s)=>{let a=e.get(n);s.isFoundDocument()!==a.isFoundDocument()&&(i=i.add(n)),s.isNoDocument()&&s.version.isEqual(q.min())?(t.removeEntry(n,s.readTime),r=r.insert(n,s)):!a.isValidDocument()||s.version.compareTo(a.version)>0||0===s.version.compareTo(a.version)&&a.hasPendingWrites?(t.addEntry(s),r=r.insert(n,s)):I("LocalStore","Ignoring outdated watch update for ",n,". Current version:",a.version," Watch version:",s.version)}),{Ps:r,Is:i}})}function sn(e,t){return e.persistence.runTransaction("Allocate target","readwrite",n=>{let r;return e.Ur.getTargetData(n,t).next(i=>i?(r=i,ei.resolve(r)):e.Ur.allocateTargetId(n).next(i=>(r=new rD(t,i,"TargetPurposeListen",n.currentSequenceNumber),e.Ur.addTargetData(n,r).next(()=>r))))}).then(n=>{let r=e.os.get(n.targetId);return(null===r||n.snapshotVersion.compareTo(r.snapshotVersion)>0)&&(e.os=e.os.insert(n.targetId,n),e._s.set(t,n.targetId)),n})}async function sr(e,t,n){let r=e.os.get(t);try{n||await e.persistence.runTransaction("Release target",n?"readwrite":"readwrite-primary",t=>e.persistence.referenceDelegate.removeTarget(t,r))}catch(e){if(!eh(e))throw e;I("LocalStore",`Failed to update sequence numbers for target ${t}: ${e}`)}e.os=e.os.remove(t),e._s.delete(r.target)}function si(e,t,n){let r=q.min(),i=nf();return e.persistence.runTransaction("Execute query","readwrite",s=>(function(e,t,n){let r=e._s.get(n);return void 0!==r?ei.resolve(e.os.get(r)):e.Ur.getTargetData(t,n)})(e,s,t4(t)).next(t=>{if(t)return r=t.lastLimboFreeSnapshotVersion,e.Ur.getMatchingKeysForTargetId(s,t.targetId).next(e=>{i=e})}).next(()=>e.ss.getDocumentsMatchingQuery(s,t,n?r:q.min(),n?i:nf())).next(n=>(so(e,nr(t),n),{documents:n,Ts:i})))}function ss(e,t){let n=e.Ur,r=e.os.get(t);return r?Promise.resolve(r.target):e.persistence.runTransaction("Get target data","readonly",e=>n.ot(e,t).next(e=>e?e.target:null))}function sa(e,t){let n=e.us.get(t)||q.min();return e.persistence.runTransaction("Get new document changes","readonly",r=>e.cs.getAllFromCollectionGroup(r,t,Y(n,-1),Number.MAX_SAFE_INTEGER)).then(n=>(so(e,t,n),n))}function so(e,t,n){let r=e.us.get(t)||q.min();n.forEach((e,t)=>{t.readTime.compareTo(r)>0&&(r=t.readTime)}),e.us.set(t,r)}async function sl(e,t,n,r){let i=nf(),s=na;for(let e of n){let n=t.Es(e.metadata.name);e.document&&(i=i.add(n));let r=t.ds(e);r.setReadTime(t.As(e.metadata.readTime)),s=s.insert(n,r)}let a=e.cs.newChangeBuffer({trackRemovals:!0}),o=await sn(e,t4(t1(K.fromString(`__bundle__/docs/${r}`))));return e.persistence.runTransaction("Apply bundle documents","readwrite",t=>st(t,a,s).next(e=>(a.apply(t),e)).next(n=>e.Ur.removeMatchingKeysForTargetId(t,o.targetId).next(()=>e.Ur.addMatchingKeys(t,i,o.targetId)).next(()=>e.localDocuments.getLocalViewOfDocuments(t,n.Ps,n.Is)).next(()=>n.Ps)))}async function su(e,t,n=nf()){let r=await sn(e,t4(rF(t.bundledQuery)));return e.persistence.runTransaction("Save named query","readwrite",i=>{let s=ro(t.readTime);if(r.snapshotVersion.compareTo(s)>=0)return e.Gr.saveNamedQuery(i,t);let a=r.withResumeToken(e5.EMPTY_BYTE_STRING,s);return e.os=e.os.insert(a.targetId,a),e.Ur.updateTargetData(i,a).next(()=>e.Ur.removeMatchingKeysForTargetId(i,r.targetId)).next(()=>e.Ur.addMatchingKeys(i,n,r.targetId)).next(()=>e.Gr.saveNamedQuery(i,t))})}function sh(e,t){return`firestore_clients_${e}_${t}`}function sc(e,t,n){let r=`firestore_mutations_${e}_${n}`;return t.isAuthenticated()&&(r+=`_${t.uid}`),r}function sd(e,t){return`firestore_targets_${e}_${t}`}class sf{constructor(e,t,n,r){this.user=e,this.batchId=t,this.state=n,this.error=r}static Rs(e,t,n){let r=JSON.parse(n),i,s="object"==typeof r&&-1!==["pending","acknowledged","rejected"].indexOf(r.state)&&(void 0===r.error||"object"==typeof r.error);return s&&r.error&&(s="string"==typeof r.error.message&&"string"==typeof r.error.code)&&(i=new x(r.error.code,r.error.message)),s?new sf(e,t,r.state,i):(T("SharedClientState",`Failed to parse mutation state for ID '${t}': ${n}`),null)}Vs(){let e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class sm{constructor(e,t,n){this.targetId=e,this.state=t,this.error=n}static Rs(e,t){let n=JSON.parse(t),r,i="object"==typeof n&&-1!==["not-current","current","rejected"].indexOf(n.state)&&(void 0===n.error||"object"==typeof n.error);return i&&n.error&&(i="string"==typeof n.error.message&&"string"==typeof n.error.code)&&(r=new x(n.error.code,n.error.message)),i?new sm(e,n.state,r):(T("SharedClientState",`Failed to parse target state for ID '${e}': ${t}`),null)}Vs(){let e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class sg{constructor(e,t){this.clientId=e,this.activeTargetIds=t}static Rs(e,t){let n=JSON.parse(t),r="object"==typeof n&&n.activeTargetIds instanceof Array,i=nm;for(let e=0;r&&e<n.activeTargetIds.length;++e)r=eI(n.activeTargetIds[e]),i=i.add(n.activeTargetIds[e]);return r?new sg(e,i):(T("SharedClientState",`Failed to parse client data for instance '${e}': ${t}`),null)}}class sp{constructor(e,t){this.clientId=e,this.onlineState=t}static Rs(e){let t=JSON.parse(e);return"object"==typeof t&&-1!==["Unknown","Online","Offline"].indexOf(t.onlineState)&&"string"==typeof t.clientId?new sp(t.clientId,t.onlineState):(T("SharedClientState",`Failed to parse online state: ${e}`),null)}}class sy{constructor(){this.activeTargetIds=nm}fs(e){this.activeTargetIds=this.activeTargetIds.add(e)}gs(e){this.activeTargetIds=this.activeTargetIds.delete(e)}Vs(){return JSON.stringify({activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()})}}class sw{constructor(e,t,n,r,i){var s,a,o;this.window=e,this.ui=t,this.persistenceKey=n,this.ps=r,this.syncEngine=null,this.onlineStateHandler=null,this.sequenceNumberHandler=null,this.ys=this.ws.bind(this),this.Ss=new eH(L),this.started=!1,this.bs=[];let l=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");this.storage=this.window.localStorage,this.currentUser=i,this.Ds=sh(this.persistenceKey,this.ps),this.vs=(s=this.persistenceKey,`firestore_sequence_number_${s}`),this.Ss=this.Ss.insert(this.ps,new sy),this.Cs=RegExp(`^firestore_clients_${l}_([^_]*)$`),this.Fs=RegExp(`^firestore_mutations_${l}_(\\d+)(?:_(.*))?$`),this.Ms=RegExp(`^firestore_targets_${l}_(\\d+)$`),this.xs=(a=this.persistenceKey,`firestore_online_state_${a}`),this.Os=(o=this.persistenceKey,`firestore_bundle_loaded_v2_${o}`),this.window.addEventListener("storage",this.ys)}static D(e){return!(!e||!e.localStorage)}async start(){for(let e of(await this.syncEngine.Qi())){if(e===this.ps)continue;let t=this.getItem(sh(this.persistenceKey,e));if(t){let n=sg.Rs(e,t);n&&(this.Ss=this.Ss.insert(n.clientId,n))}}this.Ns();let e=this.storage.getItem(this.xs);if(e){let t=this.Ls(e);t&&this.Bs(t)}for(let e of this.bs)this.ws(e);this.bs=[],this.window.addEventListener("pagehide",()=>this.shutdown()),this.started=!0}writeSequenceNumber(e){this.setItem(this.vs,JSON.stringify(e))}getAllActiveQueryTargets(){return this.ks(this.Ss)}isActiveQueryTarget(e){let t=!1;return this.Ss.forEach((n,r)=>{r.activeTargetIds.has(e)&&(t=!0)}),t}addPendingMutation(e){this.qs(e,"pending")}updateMutationState(e,t,n){this.qs(e,t,n),this.Qs(e)}addLocalQueryTarget(e,t=!0){let n="not-current";if(this.isActiveQueryTarget(e)){let t=this.storage.getItem(sd(this.persistenceKey,e));if(t){let r=sm.Rs(e,t);r&&(n=r.state)}}return t&&this.Ks.fs(e),this.Ns(),n}removeLocalQueryTarget(e){this.Ks.gs(e),this.Ns()}isLocalQueryTarget(e){return this.Ks.activeTargetIds.has(e)}clearQueryState(e){this.removeItem(sd(this.persistenceKey,e))}updateQueryState(e,t,n){this.$s(e,t,n)}handleUserChange(e,t,n){t.forEach(e=>{this.Qs(e)}),this.currentUser=e,n.forEach(e=>{this.addPendingMutation(e)})}setOnlineState(e){this.Us(e)}notifyBundleLoaded(e){this.Ws(e)}shutdown(){this.started&&(this.window.removeEventListener("storage",this.ys),this.removeItem(this.Ds),this.started=!1)}getItem(e){let t=this.storage.getItem(e);return I("SharedClientState","READ",e,t),t}setItem(e,t){I("SharedClientState","SET",e,t),this.storage.setItem(e,t)}removeItem(e){I("SharedClientState","REMOVE",e),this.storage.removeItem(e)}ws(e){if(e.storageArea===this.storage){if(I("SharedClientState","EVENT",e.key,e.newValue),e.key===this.Ds)return void T("Received WebStorage notification for local change. Another client might have garbage-collected our state");this.ui.enqueueRetryable(async()=>{if(this.started){if(null!==e.key){if(this.Cs.test(e.key)){if(null==e.newValue){let t=this.Gs(e.key);return this.zs(t,null)}{let t=this.js(e.key,e.newValue);if(t)return this.zs(t.clientId,t)}}else if(this.Fs.test(e.key)){if(null!==e.newValue){let t=this.Hs(e.key,e.newValue);if(t)return this.Js(t)}}else if(this.Ms.test(e.key)){if(null!==e.newValue){let t=this.Ys(e.key,e.newValue);if(t)return this.Zs(t)}}else if(e.key===this.xs){if(null!==e.newValue){let t=this.Ls(e.newValue);if(t)return this.Bs(t)}}else if(e.key===this.vs){let t=function(e){let t=ey.oe;if(null!=e)try{let n=JSON.parse(e);"number"==typeof n||b(),t=n}catch(e){T("SharedClientState","Failed to read sequence number from WebStorage",e)}return t}(e.newValue);t!==ey.oe&&this.sequenceNumberHandler(t)}else if(e.key===this.Os){let t=this.Xs(e.newValue);await Promise.all(t.map(e=>this.syncEngine.eo(e)))}}}else this.bs.push(e)})}}get Ks(){return this.Ss.get(this.ps)}Ns(){this.setItem(this.Ds,this.Ks.Vs())}qs(e,t,n){let r=new sf(this.currentUser,e,t,n),i=sc(this.persistenceKey,this.currentUser,e);this.setItem(i,r.Vs())}Qs(e){let t=sc(this.persistenceKey,this.currentUser,e);this.removeItem(t)}Us(e){let t={clientId:this.ps,onlineState:e};this.storage.setItem(this.xs,JSON.stringify(t))}$s(e,t,n){let r=sd(this.persistenceKey,e),i=new sm(e,t,n);this.setItem(r,i.Vs())}Ws(e){let t=JSON.stringify(Array.from(e));this.setItem(this.Os,t)}Gs(e){let t=this.Cs.exec(e);return t?t[1]:null}js(e,t){let n=this.Gs(e);return sg.Rs(n,t)}Hs(e,t){let n=this.Fs.exec(e),r=Number(n[1]),i=void 0!==n[2]?n[2]:null;return sf.Rs(new p(i),r,t)}Ys(e,t){let n=Number(this.Ms.exec(e)[1]);return sm.Rs(n,t)}Ls(e){return sp.Rs(e)}Xs(e){return JSON.parse(e)}async Js(e){if(e.user.uid===this.currentUser.uid)return this.syncEngine.no(e.batchId,e.state,e.error);I("SharedClientState",`Ignoring mutation for non-active user ${e.user.uid}`)}Zs(e){return this.syncEngine.ro(e.targetId,e.state,e.error)}zs(e,t){let n=t?this.Ss.insert(e,t):this.Ss.remove(e),r=this.ks(this.Ss),i=this.ks(n),s=[],a=[];return i.forEach(e=>{r.has(e)||s.push(e)}),r.forEach(e=>{i.has(e)||a.push(e)}),this.syncEngine.io(s,a).then(()=>{this.Ss=n})}Bs(e){this.Ss.get(e.clientId)&&this.onlineStateHandler(e.onlineState)}ks(e){let t=nm;return e.forEach((e,n)=>{t=t.unionWith(n.activeTargetIds)}),t}}class sv{constructor(){this.so=new sy,this.oo={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(e){}updateMutationState(e,t,n){}addLocalQueryTarget(e,t=!0){return t&&this.so.fs(e),this.oo[e]||"not-current"}updateQueryState(e,t,n){this.oo[e]=t}removeLocalQueryTarget(e){this.so.gs(e)}isLocalQueryTarget(e){return this.so.activeTargetIds.has(e)}clearQueryState(e){delete this.oo[e]}getAllActiveQueryTargets(){return this.so.activeTargetIds}isActiveQueryTarget(e){return this.so.activeTargetIds.has(e)}start(){return this.so=new sy,Promise.resolve()}handleUserChange(e,t,n){}setOnlineState(e){}shutdown(){}writeSequenceNumber(e){}notifyBundleLoaded(e){}}class sI{_o(e){}shutdown(){}}class sT{constructor(){this.ao=()=>this.uo(),this.co=()=>this.lo(),this.ho=[],this.Po()}_o(e){this.ho.push(e)}shutdown(){window.removeEventListener("online",this.ao),window.removeEventListener("offline",this.co)}Po(){window.addEventListener("online",this.ao),window.addEventListener("offline",this.co)}uo(){for(let e of(I("ConnectivityMonitor","Network connectivity changed: AVAILABLE"),this.ho))e(0)}lo(){for(let e of(I("ConnectivityMonitor","Network connectivity changed: UNAVAILABLE"),this.ho))e(1)}static D(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener}}let s_=null;function sE(){return null===s_?s_=0x10000000+Math.round(0x80000000*Math.random()):s_++,"0x"+s_.toString(16)}let sb={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class sS{constructor(e){this.Io=e.Io,this.To=e.To}Eo(e){this.Ao=e}Ro(e){this.Vo=e}mo(e){this.fo=e}onMessage(e){this.po=e}close(){this.To()}send(e){this.Io(e)}yo(){this.Ao()}wo(){this.Vo()}So(e){this.fo(e)}bo(e){this.po(e)}}let sx="WebChannelConnection";class sD extends class{constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;let t=e.ssl?"https":"http",n=encodeURIComponent(this.databaseId.projectId),r=encodeURIComponent(this.databaseId.database);this.Do=t+"://"+e.host,this.vo=`projects/${n}/databases/${r}`,this.Co="(default)"===this.databaseId.database?`project_id=${n}`:`project_id=${n}&database_id=${r}`}get Fo(){return!1}Mo(e,t,n,r,i){let s=sE(),a=this.xo(e,t.toUriEncodedString());I("RestConnection",`Sending RPC '${e}' ${s}:`,a,n);let o={"google-cloud-resource-prefix":this.vo,"x-goog-request-params":this.Co};return this.Oo(o,r,i),this.No(e,a,o,n).then(t=>(I("RestConnection",`Received RPC '${e}' ${s}: `,t),t),t=>{throw _("RestConnection",`RPC '${e}' ${s} failed with error: `,t,"url: ",a,"request:",n),t})}Lo(e,t,n,r,i,s){return this.Mo(e,t,n,r,i)}Oo(e,t,n){e["X-Goog-Api-Client"]=function(){return"gl-js/ fire/"+y}(),e["Content-Type"]="text/plain",this.databaseInfo.appId&&(e["X-Firebase-GMPID"]=this.databaseInfo.appId),t&&t.headers.forEach((t,n)=>e[n]=t),n&&n.headers.forEach((t,n)=>e[n]=t)}xo(e,t){let n=sb[e];return`${this.Do}/v1/${t}:${n}`}terminate(){}}{constructor(e){super(e),this.forceLongPolling=e.forceLongPolling,this.autoDetectLongPolling=e.autoDetectLongPolling,this.useFetchStreams=e.useFetchStreams,this.longPollingOptions=e.longPollingOptions}No(e,t,n,r){let i=sE();return new Promise((s,a)=>{let o=new d.ZS;o.setWithCredentials(!0),o.listenOnce(d.Bx.COMPLETE,()=>{try{switch(o.getLastErrorCode()){case d.O4.NO_ERROR:let t=o.getResponseJson();I(sx,`XHR for RPC '${e}' ${i} received:`,JSON.stringify(t)),s(t);break;case d.O4.TIMEOUT:I(sx,`RPC '${e}' ${i} timed out`),a(new x(S.DEADLINE_EXCEEDED,"Request time out"));break;case d.O4.HTTP_ERROR:let n=o.getStatus();if(I(sx,`RPC '${e}' ${i} failed with status:`,n,"response text:",o.getResponseText()),n>0){let e=o.getResponseJson();Array.isArray(e)&&(e=e[0]);let t=null==e?void 0:e.error;if(t&&t.status&&t.message){let e=function(e){let t=e.toLowerCase().replace(/_/g,"-");return Object.values(S).indexOf(t)>=0?t:S.UNKNOWN}(t.status);a(new x(e,t.message))}else a(new x(S.UNKNOWN,"Server responded with status "+o.getStatus()))}else a(new x(S.UNAVAILABLE,"Connection failed."));break;default:b()}}finally{I(sx,`RPC '${e}' ${i} completed.`)}});let l=JSON.stringify(r);I(sx,`RPC '${e}' ${i} sending request:`,r),o.send(t,"POST",l,n,15)})}Bo(e,t,n){let i=sE(),s=[this.Do,"/","google.firestore.v1.Firestore","/",e,"/channel"],a=(0,d.fF)(),o=(0,d.Ao)(),l={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/${this.databaseId.database}`},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},u=this.longPollingOptions.timeoutSeconds;void 0!==u&&(l.longPollingTimeout=Math.round(1e3*u)),this.useFetchStreams&&(l.useFetchStreams=!0),this.Oo(l.initMessageHeaders,t,n),l.encodeInitMessageHeaders=!0;let h=s.join("");I(sx,`Creating RPC '${e}' stream ${i}: ${h}`,l);let c=a.createWebChannel(h,l),f=!1,m=!1,g=new sS({Io:t=>{m?I(sx,`Not sending because RPC '${e}' stream ${i} is closed:`,t):(f||(I(sx,`Opening RPC '${e}' stream ${i} transport.`),c.open(),f=!0),I(sx,`RPC '${e}' stream ${i} sending:`,t),c.send(t))},To:()=>c.close()}),p=(e,t,n)=>{e.listen(t,e=>{try{n(e)}catch(e){setTimeout(()=>{throw e},0)}})};return p(c,d.iO.EventType.OPEN,()=>{m||(I(sx,`RPC '${e}' stream ${i} transport opened.`),g.yo())}),p(c,d.iO.EventType.CLOSE,()=>{m||(m=!0,I(sx,`RPC '${e}' stream ${i} transport closed`),g.So())}),p(c,d.iO.EventType.ERROR,t=>{m||(m=!0,_(sx,`RPC '${e}' stream ${i} transport errored:`,t),g.So(new x(S.UNAVAILABLE,"The operation could not be completed")))}),p(c,d.iO.EventType.MESSAGE,t=>{var n;if(!m){let s=t.data[0];s||b();let a=s.error||(null==(n=s[0])?void 0:n.error);if(a){I(sx,`RPC '${e}' stream ${i} received error:`,a);let t=a.status,n=function(e){let t=r[e];if(void 0!==t)return nH(t)}(t),s=a.message;void 0===n&&(n=S.INTERNAL,s="Unknown error status: "+t+" with message "+a.message),m=!0,g.So(new x(n,s)),c.close()}else I(sx,`RPC '${e}' stream ${i} received:`,s),g.bo(s)}}),p(o,d.Jh.STAT_EVENT,t=>{t.stat===d.ro.PROXY?I(sx,`RPC '${e}' stream ${i} detected buffering proxy`):t.stat===d.ro.NOPROXY&&I(sx,`RPC '${e}' stream ${i} detected no buffering proxy`)}),setTimeout(()=>{g.wo()},0),g}}function sC(){return"undefined"!=typeof window?window:null}function sN(){return"undefined"!=typeof document?document:null}function sA(e){return new rr(e,!0)}class sk{constructor(e,t,n=1e3,r=1.5,i=6e4){this.ui=e,this.timerId=t,this.ko=n,this.qo=r,this.Qo=i,this.Ko=0,this.$o=null,this.Uo=Date.now(),this.reset()}reset(){this.Ko=0}Wo(){this.Ko=this.Qo}Go(e){this.cancel();let t=Math.floor(this.Ko+this.zo()),n=Math.max(0,Date.now()-this.Uo),r=Math.max(0,t-n);r>0&&I("ExponentialBackoff",`Backing off for ${r} ms (base delay: ${this.Ko} ms, delay with jitter: ${t} ms, last attempt: ${n} ms ago)`),this.$o=this.ui.enqueueAfterDelay(this.timerId,r,()=>(this.Uo=Date.now(),e())),this.Ko*=this.qo,this.Ko<this.ko&&(this.Ko=this.ko),this.Ko>this.Qo&&(this.Ko=this.Qo)}jo(){null!==this.$o&&(this.$o.skipDelay(),this.$o=null)}cancel(){null!==this.$o&&(this.$o.cancel(),this.$o=null)}zo(){return(Math.random()-.5)*this.Ko}}class sV{constructor(e,t,n,r,i,s,a,o){this.ui=e,this.Ho=n,this.Jo=r,this.connection=i,this.authCredentialsProvider=s,this.appCheckCredentialsProvider=a,this.listener=o,this.state=0,this.Yo=0,this.Zo=null,this.Xo=null,this.stream=null,this.e_=0,this.t_=new sk(e,t)}n_(){return 1===this.state||5===this.state||this.r_()}r_(){return 2===this.state||3===this.state}start(){this.e_=0,4!==this.state?this.auth():this.i_()}async stop(){this.n_()&&await this.close(0)}s_(){this.state=0,this.t_.reset()}o_(){this.r_()&&null===this.Zo&&(this.Zo=this.ui.enqueueAfterDelay(this.Ho,6e4,()=>this.__()))}a_(e){this.u_(),this.stream.send(e)}async __(){if(this.r_())return this.close(0)}u_(){this.Zo&&(this.Zo.cancel(),this.Zo=null)}c_(){this.Xo&&(this.Xo.cancel(),this.Xo=null)}async close(e,t){this.u_(),this.c_(),this.t_.cancel(),this.Yo++,4!==e?this.t_.reset():t&&t.code===S.RESOURCE_EXHAUSTED?(T(t.toString()),T("Using maximum backoff delay to prevent overloading the backend."),this.t_.Wo()):t&&t.code===S.UNAUTHENTICATED&&3!==this.state&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),null!==this.stream&&(this.l_(),this.stream.close(),this.stream=null),this.state=e,await this.listener.mo(t)}l_(){}auth(){this.state=1;let e=this.h_(this.Yo),t=this.Yo;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then(([e,n])=>{this.Yo===t&&this.P_(e,n)},t=>{e(()=>{let e=new x(S.UNKNOWN,"Fetching auth token failed: "+t.message);return this.I_(e)})})}P_(e,t){let n=this.h_(this.Yo);this.stream=this.T_(e,t),this.stream.Eo(()=>{n(()=>this.listener.Eo())}),this.stream.Ro(()=>{n(()=>(this.state=2,this.Xo=this.ui.enqueueAfterDelay(this.Jo,1e4,()=>(this.r_()&&(this.state=3),Promise.resolve())),this.listener.Ro()))}),this.stream.mo(e=>{n(()=>this.I_(e))}),this.stream.onMessage(e=>{n(()=>1==++this.e_?this.E_(e):this.onNext(e))})}i_(){this.state=5,this.t_.Go(async()=>{this.state=0,this.start()})}I_(e){return I("PersistentStream",`close with error: ${e}`),this.stream=null,this.close(4,e)}h_(e){return t=>{this.ui.enqueueAndForget(()=>this.Yo===e?t():(I("PersistentStream","stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve()))}}}class sR extends sV{constructor(e,t,n,r,i,s){super(e,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",t,n,r,s),this.serializer=i}T_(e,t){return this.connection.Bo("Listen",e,t)}E_(e){return this.onNext(e)}onNext(e){this.t_.reset();let t=function(e,t){let n;if("targetChange"in t){var r,i;t.targetChange;let s="NO_CHANGE"===(r=t.targetChange.targetChangeType||"NO_CHANGE")?0:"ADD"===r?1:"REMOVE"===r?2:"CURRENT"===r?3:"RESET"===r?4:b(),a=t.targetChange.targetIds||[],o=(i=t.targetChange.resumeToken,e.useProto3Json?(void 0===i||"string"==typeof i||b(),e5.fromBase64String(i||"")):(void 0===i||i instanceof m||i instanceof Uint8Array||b(),e5.fromUint8Array(i||new Uint8Array))),l=t.targetChange.cause;n=new n4(s,a,o,l&&new x(void 0===l.code?S.UNKNOWN:nH(l.code),l.message||"")||null)}else if("documentChange"in t){t.documentChange;let r=t.documentChange;r.document,r.document.name,r.document.updateTime;let i=rd(e,r.document.name),s=ro(r.document.updateTime),a=r.document.createTime?ro(r.document.createTime):q.min(),o=new tb({mapValue:{fields:r.document.fields}}),l=tS.newFoundDocument(i,s,a,o);n=new n5(r.targetIds||[],r.removedTargetIds||[],l.key,l)}else if("documentDelete"in t){t.documentDelete;let r=t.documentDelete;r.document;let i=rd(e,r.document),s=r.readTime?ro(r.readTime):q.min(),a=tS.newNoDocument(i,s);n=new n5([],r.removedTargetIds||[],a.key,a)}else if("documentRemove"in t){t.documentRemove;let r=t.documentRemove;r.document;let i=rd(e,r.document);n=new n5([],r.removedTargetIds||[],i,null)}else{if(!("filter"in t))return b();{t.filter;let e=t.filter;e.targetId;let{count:r=0,unchangedNames:i}=e,s=new nj(r,i);n=new n3(e.targetId,s)}}return n}(this.serializer,e),n=function(e){if(!("targetChange"in e))return q.min();let t=e.targetChange;return t.targetIds&&t.targetIds.length?q.min():t.readTime?ro(t.readTime):q.min()}(e);return this.listener.d_(t,n)}A_(e){let t={};t.database=rg(this.serializer),t.addTarget=function(e,t){let n,r=t.target;if((n=tJ(r)?{documents:rT(e,r)}:{query:r_(e,r)._t}).targetId=t.targetId,t.resumeToken.approximateByteSize()>0){n.resumeToken=ra(e,t.resumeToken);let r=ri(e,t.expectedCount);null!==r&&(n.expectedCount=r)}else if(t.snapshotVersion.compareTo(q.min())>0){n.readTime=rs(e,t.snapshotVersion.toTimestamp());let r=ri(e,t.expectedCount);null!==r&&(n.expectedCount=r)}return n}(this.serializer,e);let n=function(e,t){let n=function(e){switch(e){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return b()}}(t.purpose);return null==n?null:{"goog-listen-tags":n}}(this.serializer,e);n&&(t.labels=n),this.a_(t)}R_(e){let t={};t.database=rg(this.serializer),t.removeTarget=e,this.a_(t)}}class sO extends sV{constructor(e,t,n,r,i,s){super(e,"write_stream_connection_backoff","write_stream_idle","health_check_timeout",t,n,r,s),this.serializer=i}get V_(){return this.e_>0}start(){this.lastStreamToken=void 0,super.start()}l_(){this.V_&&this.m_([])}T_(e,t){return this.connection.Bo("Write",e,t)}E_(e){return e.streamToken||b(),this.lastStreamToken=e.streamToken,e.writeResults&&0!==e.writeResults.length&&b(),this.listener.f_()}onNext(e){var t,n;e.streamToken||b(),this.lastStreamToken=e.streamToken,this.t_.reset();let r=(t=e.writeResults,n=e.commitTime,t&&t.length>0?(void 0!==n||b(),t.map(e=>{let t;return(t=e.updateTime?ro(e.updateTime):ro(n)).isEqual(q.min())&&(t=ro(n)),new nN(t,e.transformResults||[])})):[]),i=ro(e.commitTime);return this.listener.g_(i,r)}p_(){let e={};e.database=rg(this.serializer),this.a_(e)}m_(e){let t={streamToken:this.lastStreamToken,writes:e.map(e=>rv(this.serializer,e))};this.a_(t)}}class sM extends class{}{constructor(e,t,n,r){super(),this.authCredentials=e,this.appCheckCredentials=t,this.connection=n,this.serializer=r,this.y_=!1}w_(){if(this.y_)throw new x(S.FAILED_PRECONDITION,"The client has already been terminated.")}Mo(e,t,n,r){return this.w_(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([i,s])=>this.connection.Mo(e,ru(t,n),r,i,s)).catch(e=>{throw"FirebaseError"===e.name?(e.code===S.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new x(S.UNKNOWN,e.toString())})}Lo(e,t,n,r,i){return this.w_(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([s,a])=>this.connection.Lo(e,ru(t,n),r,s,a,i)).catch(e=>{throw"FirebaseError"===e.name?(e.code===S.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new x(S.UNKNOWN,e.toString())})}terminate(){this.y_=!0,this.connection.terminate()}}class sF{constructor(e,t){this.asyncQueue=e,this.onlineStateHandler=t,this.state="Unknown",this.S_=0,this.b_=null,this.D_=!0}v_(){0===this.S_&&(this.C_("Unknown"),this.b_=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,()=>(this.b_=null,this.F_("Backend didn't respond within 10 seconds."),this.C_("Offline"),Promise.resolve())))}M_(e){"Online"===this.state?this.C_("Unknown"):(this.S_++,this.S_>=1&&(this.x_(),this.F_(`Connection failed 1 times. Most recent error: ${e.toString()}`),this.C_("Offline")))}set(e){this.x_(),this.S_=0,"Online"===e&&(this.D_=!1),this.C_(e)}C_(e){e!==this.state&&(this.state=e,this.onlineStateHandler(e))}F_(e){let t=`Could not reach Cloud Firestore backend. ${e}
This typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this.D_?(T(t),this.D_=!1):I("OnlineStateTracker",t)}x_(){null!==this.b_&&(this.b_.cancel(),this.b_=null)}}class sL{constructor(e,t,n,r,i){this.localStore=e,this.datastore=t,this.asyncQueue=n,this.remoteSyncer={},this.O_=[],this.N_=new Map,this.L_=new Set,this.B_=[],this.k_=i,this.k_._o(e=>{n.enqueueAndForget(async()=>{sQ(this)&&(I("RemoteStore","Restarting streams for network reachability change."),await async function(e){e.L_.add(4),await sU(e),e.q_.set("Unknown"),e.L_.delete(4),await sP(e)}(this))})}),this.q_=new sF(n,r)}}async function sP(e){if(sQ(e))for(let t of e.B_)await t(!0)}async function sU(e){for(let t of e.B_)await t(!1)}function sq(e,t){e.N_.has(t.targetId)||(e.N_.set(t.targetId,t),s$(e)?sG(e):s9(e).r_()&&sK(e,t))}function sB(e,t){let n=s9(e);e.N_.delete(t),n.r_()&&sz(e,t),0===e.N_.size&&(n.r_()?n.o_():sQ(e)&&e.q_.set("Unknown"))}function sK(e,t){if(e.Q_.xe(t.targetId),t.resumeToken.approximateByteSize()>0||t.snapshotVersion.compareTo(q.min())>0){let n=e.remoteSyncer.getRemoteKeysForTarget(t.targetId).size;t=t.withExpectedCount(n)}s9(e).A_(t)}function sz(e,t){e.Q_.xe(t),s9(e).R_(t)}function sG(e){e.Q_=new n6({getRemoteKeysForTarget:t=>e.remoteSyncer.getRemoteKeysForTarget(t),ot:t=>e.N_.get(t)||null,tt:()=>e.datastore.serializer.databaseId}),s9(e).start(),e.q_.v_()}function s$(e){return sQ(e)&&!s9(e).n_()&&e.N_.size>0}function sQ(e){return 0===e.L_.size}async function sj(e){e.q_.set("Online")}async function sW(e){e.N_.forEach((t,n)=>{sK(e,t)})}async function sH(e,t){e.Q_=void 0,s$(e)?(e.q_.M_(t),sG(e)):e.q_.set("Unknown")}async function sJ(e,t,n){if(e.q_.set("Online"),t instanceof n4&&2===t.state&&t.cause)try{await async function(e,t){let n=t.cause;for(let r of t.targetIds)e.N_.has(r)&&(await e.remoteSyncer.rejectListen(r,n),e.N_.delete(r),e.Q_.removeTarget(r))}(e,t)}catch(n){I("RemoteStore","Failed to remove targets %s: %s ",t.targetIds.join(","),n),await sY(e,n)}else if(t instanceof n5?e.Q_.Ke(t):t instanceof n3?e.Q_.He(t):e.Q_.We(t),!n.isEqual(q.min()))try{let t=await se(e.localStore);n.compareTo(t)>=0&&await function(e,t){let n=e.Q_.rt(t);return n.targetChanges.forEach((n,r)=>{if(n.resumeToken.approximateByteSize()>0){let i=e.N_.get(r);i&&e.N_.set(r,i.withResumeToken(n.resumeToken,t))}}),n.targetMismatches.forEach((t,n)=>{let r=e.N_.get(t);if(!r)return;e.N_.set(t,r.withResumeToken(e5.EMPTY_BYTE_STRING,r.snapshotVersion)),sz(e,t);let i=new rD(r.target,t,n,r.sequenceNumber);sK(e,i)}),e.remoteSyncer.applyRemoteEvent(n)}(e,n)}catch(t){I("RemoteStore","Failed to raise snapshot:",t),await sY(e,t)}}async function sY(e,t,n){if(!eh(t))throw t;e.L_.add(1),await sU(e),e.q_.set("Offline"),n||(n=()=>se(e.localStore)),e.asyncQueue.enqueueRetryable(async()=>{I("RemoteStore","Retrying IndexedDB access"),await n(),e.L_.delete(1),await sP(e)})}function sX(e,t){return t().catch(n=>sY(e,n,t))}async function sZ(e){var t;let n=s7(e),r=e.O_.length>0?e.O_[e.O_.length-1].batchId:-1;for(;sQ(t=e)&&t.O_.length<10;)try{let t=await function(e,t){return e.persistence.runTransaction("Get next mutation batch","readonly",n=>(void 0===t&&(t=-1),e.mutationQueue.getNextMutationBatchAfterBatchId(n,t)))}(e.localStore,r);if(null===t){0===e.O_.length&&n.o_();break}r=t.batchId,function(e,t){e.O_.push(t);let n=s7(e);n.r_()&&n.V_&&n.m_(t.mutations)}(e,t)}catch(t){await sY(e,t)}s0(e)&&s1(e)}function s0(e){return sQ(e)&&!s7(e).n_()&&e.O_.length>0}function s1(e){s7(e).start()}async function s2(e){s7(e).p_()}async function s5(e){let t=s7(e);for(let n of e.O_)t.m_(n.mutations)}async function s3(e,t,n){let r=e.O_.shift(),i=nG.from(r,t,n);await sX(e,()=>e.remoteSyncer.applySuccessfulWrite(i)),await sZ(e)}async function s4(e,t){t&&s7(e).V_&&await async function(e,t){var n;if(nW(n=t.code)&&n!==S.ABORTED){let n=e.O_.shift();s7(e).s_(),await sX(e,()=>e.remoteSyncer.rejectFailedWrite(n.batchId,t)),await sZ(e)}}(e,t),s0(e)&&s1(e)}async function s8(e,t){e.asyncQueue.verifyOperationInProgress(),I("RemoteStore","RemoteStore received new credentials");let n=sQ(e);e.L_.add(3),await sU(e),n&&e.q_.set("Unknown"),await e.remoteSyncer.handleCredentialChange(t),e.L_.delete(3),await sP(e)}async function s6(e,t){t?(e.L_.delete(2),await sP(e)):t||(e.L_.add(2),await sU(e),e.q_.set("Unknown"))}function s9(e){var t,n,r;return e.K_||(t=e.datastore,n=e.asyncQueue,r={Eo:sj.bind(null,e),Ro:sW.bind(null,e),mo:sH.bind(null,e),d_:sJ.bind(null,e)},t.w_(),e.K_=new sR(n,t.connection,t.authCredentials,t.appCheckCredentials,t.serializer,r),e.B_.push(async t=>{t?(e.K_.s_(),s$(e)?sG(e):e.q_.set("Unknown")):(await e.K_.stop(),e.Q_=void 0)})),e.K_}function s7(e){var t,n,r;return e.U_||(t=e.datastore,n=e.asyncQueue,r={Eo:()=>Promise.resolve(),Ro:s2.bind(null,e),mo:s4.bind(null,e),f_:s5.bind(null,e),g_:s3.bind(null,e)},t.w_(),e.U_=new sO(n,t.connection,t.authCredentials,t.appCheckCredentials,t.serializer,r),e.B_.push(async t=>{t?(e.U_.s_(),await sZ(e)):(await e.U_.stop(),e.O_.length>0&&(I("RemoteStore",`Stopping write stream with ${e.O_.length} pending writes`),e.O_=[]))})),e.U_}class ae{constructor(e,t,n,r,i){this.asyncQueue=e,this.timerId=t,this.targetTimeMs=n,this.op=r,this.removalCallback=i,this.deferred=new D,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(e=>{})}get promise(){return this.deferred.promise}static createAndSchedule(e,t,n,r,i){let s=new ae(e,t,Date.now()+n,r,i);return s.start(n),s}start(e){this.timerHandle=setTimeout(()=>this.handleDelayElapsed(),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new x(S.CANCELLED,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget(()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then(e=>this.deferred.resolve(e))):Promise.resolve())}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}function at(e,t){if(T("AsyncQueue",`${t}: ${e}`),eh(e))return new x(S.UNAVAILABLE,`${t}: ${e}`);throw e}class an{constructor(e){this.comparator=e?(t,n)=>e(t,n)||$.comparator(t.key,n.key):(e,t)=>$.comparator(e.key,t.key),this.keyedMap=nl(),this.sortedSet=new eH(this.comparator)}static emptySet(e){return new an(e.comparator)}has(e){return null!=this.keyedMap.get(e)}get(e){return this.keyedMap.get(e)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(e){let t=this.keyedMap.get(e);return t?this.sortedSet.indexOf(t):-1}get size(){return this.sortedSet.size}forEach(e){this.sortedSet.inorderTraversal((t,n)=>(e(t),!1))}add(e){let t=this.delete(e.key);return t.copy(t.keyedMap.insert(e.key,e),t.sortedSet.insert(e,null))}delete(e){let t=this.get(e);return t?this.copy(this.keyedMap.remove(e),this.sortedSet.remove(t)):this}isEqual(e){if(!(e instanceof an)||this.size!==e.size)return!1;let t=this.sortedSet.getIterator(),n=e.sortedSet.getIterator();for(;t.hasNext();){let e=t.getNext().key,r=n.getNext().key;if(!e.isEqual(r))return!1}return!0}toString(){let e=[];return this.forEach(t=>{e.push(t.toString())}),0===e.length?"DocumentSet ()":"DocumentSet (\n  "+e.join("  \n")+"\n)"}copy(e,t){let n=new an;return n.comparator=this.comparator,n.keyedMap=e,n.sortedSet=t,n}}class ar{constructor(){this.W_=new eH($.comparator)}track(e){let t=e.doc.key,n=this.W_.get(t);n?0!==e.type&&3===n.type?this.W_=this.W_.insert(t,e):3===e.type&&1!==n.type?this.W_=this.W_.insert(t,{type:n.type,doc:e.doc}):2===e.type&&2===n.type?this.W_=this.W_.insert(t,{type:2,doc:e.doc}):2===e.type&&0===n.type?this.W_=this.W_.insert(t,{type:0,doc:e.doc}):1===e.type&&0===n.type?this.W_=this.W_.remove(t):1===e.type&&2===n.type?this.W_=this.W_.insert(t,{type:1,doc:n.doc}):0===e.type&&1===n.type?this.W_=this.W_.insert(t,{type:2,doc:e.doc}):b():this.W_=this.W_.insert(t,e)}G_(){let e=[];return this.W_.inorderTraversal((t,n)=>{e.push(n)}),e}}class ai{constructor(e,t,n,r,i,s,a,o,l){this.query=e,this.docs=t,this.oldDocs=n,this.docChanges=r,this.mutatedKeys=i,this.fromCache=s,this.syncStateChanged=a,this.excludesMetadataChanges=o,this.hasCachedResults=l}static fromInitialDocuments(e,t,n,r,i){let s=[];return t.forEach(e=>{s.push({type:0,doc:e})}),new ai(e,t,an.emptySet(t),s,n,r,!0,!1,i)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(e){if(!(this.fromCache===e.fromCache&&this.hasCachedResults===e.hasCachedResults&&this.syncStateChanged===e.syncStateChanged&&this.mutatedKeys.isEqual(e.mutatedKeys)&&t7(this.query,e.query)&&this.docs.isEqual(e.docs)&&this.oldDocs.isEqual(e.oldDocs)))return!1;let t=this.docChanges,n=e.docChanges;if(t.length!==n.length)return!1;for(let e=0;e<t.length;e++)if(t[e].type!==n[e].type||!t[e].doc.isEqual(n[e].doc))return!1;return!0}}class as{constructor(){this.z_=void 0,this.j_=[]}H_(){return this.j_.some(e=>e.J_())}}class aa{constructor(){this.queries=ao(),this.onlineState="Unknown",this.Y_=new Set}terminate(){!function(e,t){let n=e.queries;e.queries=ao(),n.forEach((e,n)=>{for(let e of n.j_)e.onError(t)})}(this,new x(S.ABORTED,"Firestore shutting down"))}}function ao(){return new ns(e=>ne(e),t7)}async function al(e,t){let n=3,r=t.query,i=e.queries.get(r);i?!i.H_()&&t.J_()&&(n=2):(i=new as,n=+!t.J_());try{switch(n){case 0:i.z_=await e.onListen(r,!0);break;case 1:i.z_=await e.onListen(r,!1);break;case 2:await e.onFirstRemoteStoreListen(r)}}catch(n){let e=at(n,`Initialization of query '${nt(t.query)}' failed`);return void t.onError(e)}e.queries.set(r,i),i.j_.push(t),t.Z_(e.onlineState),i.z_&&t.X_(i.z_)&&ad(e)}async function au(e,t){let n=t.query,r=3,i=e.queries.get(n);if(i){let e=i.j_.indexOf(t);e>=0&&(i.j_.splice(e,1),0===i.j_.length?r=+!t.J_():!i.H_()&&t.J_()&&(r=2))}switch(r){case 0:return e.queries.delete(n),e.onUnlisten(n,!0);case 1:return e.queries.delete(n),e.onUnlisten(n,!1);case 2:return e.onLastRemoteStoreUnlisten(n);default:return}}function ah(e,t){let n=!1;for(let r of t){let t=r.query,i=e.queries.get(t);if(i){for(let e of i.j_)e.X_(r)&&(n=!0);i.z_=r}}n&&ad(e)}function ac(e,t,n){let r=e.queries.get(t);if(r)for(let e of r.j_)e.onError(n);e.queries.delete(t)}function ad(e){e.Y_.forEach(e=>{e.next()})}(a=s||(s={})).ea="default",a.Cache="cache";class af{constructor(e,t,n){this.query=e,this.ta=t,this.na=!1,this.ra=null,this.onlineState="Unknown",this.options=n||{}}X_(e){if(!this.options.includeMetadataChanges){let t=[];for(let n of e.docChanges)3!==n.type&&t.push(n);e=new ai(e.query,e.docs,e.oldDocs,t,e.mutatedKeys,e.fromCache,e.syncStateChanged,!0,e.hasCachedResults)}let t=!1;return this.na?this.ia(e)&&(this.ta.next(e),t=!0):this.sa(e,this.onlineState)&&(this.oa(e),t=!0),this.ra=e,t}onError(e){this.ta.error(e)}Z_(e){this.onlineState=e;let t=!1;return this.ra&&!this.na&&this.sa(this.ra,e)&&(this.oa(this.ra),t=!0),t}sa(e,t){return!(e.fromCache&&this.J_())||(!this.options._a||"Offline"===t)&&(!e.docs.isEmpty()||e.hasCachedResults||"Offline"===t)}ia(e){if(e.docChanges.length>0)return!0;let t=this.ra&&this.ra.hasPendingWrites!==e.hasPendingWrites;return!(!e.syncStateChanged&&!t)&&!0===this.options.includeMetadataChanges}oa(e){e=ai.fromInitialDocuments(e.query,e.docs,e.mutatedKeys,e.fromCache,e.hasCachedResults),this.na=!0,this.ta.next(e)}J_(){return this.options.source!==s.Cache}}class am{constructor(e,t){this.aa=e,this.byteLength=t}ua(){return"metadata"in this.aa}}class ag{constructor(e){this.serializer=e}Es(e){return rd(this.serializer,e)}ds(e){return e.metadata.exists?rw(this.serializer,e.document,!1):tS.newNoDocument(this.Es(e.metadata.name),this.As(e.metadata.readTime))}As(e){return ro(e)}}class ap{constructor(e){this.key=e}}class ay{constructor(e){this.key=e}}class aw{constructor(e,t){this.query=e,this.Ta=t,this.Ea=null,this.hasCachedResults=!1,this.current=!1,this.da=nf(),this.mutatedKeys=nf(),this.Aa=ni(e),this.Ra=new an(this.Aa)}get Va(){return this.Ta}ma(e,t){let n=t?t.fa:new ar,r=t?t.Ra:this.Ra,i=t?t.mutatedKeys:this.mutatedKeys,s=r,a=!1,o="F"===this.query.limitType&&r.size===this.query.limit?r.last():null,l="L"===this.query.limitType&&r.size===this.query.limit?r.first():null;if(e.inorderTraversal((e,t)=>{let u=r.get(e),h=nn(this.query,t)?t:null,c=!!u&&this.mutatedKeys.has(u.key),d=!!h&&(h.hasLocalMutations||this.mutatedKeys.has(h.key)&&h.hasCommittedMutations),f=!1;u&&h?u.data.isEqual(h.data)?c!==d&&(n.track({type:3,doc:h}),f=!0):this.ga(u,h)||(n.track({type:2,doc:h}),f=!0,(o&&this.Aa(h,o)>0||l&&0>this.Aa(h,l))&&(a=!0)):!u&&h?(n.track({type:0,doc:h}),f=!0):u&&!h&&(n.track({type:1,doc:u}),f=!0,(o||l)&&(a=!0)),f&&(h?(s=s.add(h),i=d?i.add(e):i.delete(e)):(s=s.delete(e),i=i.delete(e)))}),null!==this.query.limit)for(;s.size>this.query.limit;){let e="F"===this.query.limitType?s.last():s.first();s=s.delete(e.key),i=i.delete(e.key),n.track({type:1,doc:e})}return{Ra:s,fa:n,ns:a,mutatedKeys:i}}ga(e,t){return e.hasLocalMutations&&t.hasCommittedMutations&&!t.hasLocalMutations}applyChanges(e,t,n,r){let i=this.Ra;this.Ra=e.Ra,this.mutatedKeys=e.mutatedKeys;let s=e.fa.G_();s.sort((e,t)=>(function(e,t){let n=e=>{switch(e){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return b()}};return n(e)-n(t)})(e.type,t.type)||this.Aa(e.doc,t.doc)),this.pa(n),r=null!=r&&r;let a=t&&!r?this.ya():[],o=0===this.da.size&&this.current&&!r?1:0,l=o!==this.Ea;return(this.Ea=o,0!==s.length||l)?{snapshot:new ai(this.query,e.Ra,i,s,e.mutatedKeys,0===o,l,!1,!!n&&n.resumeToken.approximateByteSize()>0),wa:a}:{wa:a}}Z_(e){return this.current&&"Offline"===e?(this.current=!1,this.applyChanges({Ra:this.Ra,fa:new ar,mutatedKeys:this.mutatedKeys,ns:!1},!1)):{wa:[]}}Sa(e){return!this.Ta.has(e)&&!!this.Ra.has(e)&&!this.Ra.get(e).hasLocalMutations}pa(e){e&&(e.addedDocuments.forEach(e=>this.Ta=this.Ta.add(e)),e.modifiedDocuments.forEach(e=>{}),e.removedDocuments.forEach(e=>this.Ta=this.Ta.delete(e)),this.current=e.current)}ya(){if(!this.current)return[];let e=this.da;this.da=nf(),this.Ra.forEach(e=>{this.Sa(e.key)&&(this.da=this.da.add(e.key))});let t=[];return e.forEach(e=>{this.da.has(e)||t.push(new ay(e))}),this.da.forEach(n=>{e.has(n)||t.push(new ap(n))}),t}ba(e){this.Ta=e.Ts,this.da=nf();let t=this.ma(e.documents);return this.applyChanges(t,!0)}Da(){return ai.fromInitialDocuments(this.query,this.Ra,this.mutatedKeys,0===this.Ea,this.hasCachedResults)}}class av{constructor(e,t,n){this.query=e,this.targetId=t,this.view=n}}class aI{constructor(e){this.key=e,this.va=!1}}class aT{constructor(e,t,n,r,i,s){this.localStore=e,this.remoteStore=t,this.eventManager=n,this.sharedClientState=r,this.currentUser=i,this.maxConcurrentLimboResolutions=s,this.Ca={},this.Fa=new ns(e=>ne(e),t7),this.Ma=new Map,this.xa=new Set,this.Oa=new eH($.comparator),this.Na=new Map,this.La=new iK,this.Ba={},this.ka=new Map,this.qa=iw.kn(),this.onlineState="Unknown",this.Qa=void 0}get isPrimaryClient(){return!0===this.Qa}}async function a_(e,t,n=!0){let r,i=aX(e),s=i.Fa.get(t);return s?(i.sharedClientState.addLocalQueryTarget(s.targetId),r=s.view.Da()):r=await ab(i,t,n,!0),r}async function aE(e,t){let n=aX(e);await ab(n,t,!0,!1)}async function ab(e,t,n,r){let i,s=await sn(e.localStore,t4(t)),a=s.targetId,o=e.sharedClientState.addLocalQueryTarget(a,n);return r&&(i=await aS(e,t,a,"current"===o,s.resumeToken)),e.isPrimaryClient&&n&&sq(e.remoteStore,s),i}async function aS(e,t,n,r,i){e.Ka=(t,n,r)=>(async function(e,t,n,r){let i=t.view.ma(n);i.ns&&(i=await si(e.localStore,t.query,!1).then(({documents:e})=>t.view.ma(e,i)));let s=r&&r.targetChanges.get(t.targetId),a=r&&null!=r.targetMismatches.get(t.targetId),o=t.view.applyChanges(i,e.isPrimaryClient,s,a);return aP(e,t.targetId,o.wa),o.snapshot})(e,t,n,r);let s=await si(e.localStore,t,!0),a=new aw(t,s.Ts),o=a.ma(s.documents),l=n2.createSynthesizedTargetChangeForCurrentChange(n,r&&"Offline"!==e.onlineState,i),u=a.applyChanges(o,e.isPrimaryClient,l);aP(e,n,u.wa);let h=new av(t,n,a);return e.Fa.set(t,h),e.Ma.has(n)?e.Ma.get(n).push(t):e.Ma.set(n,[t]),u.snapshot}async function ax(e,t,n){let r=e.Fa.get(t),i=e.Ma.get(r.targetId);if(i.length>1)return e.Ma.set(r.targetId,i.filter(e=>!t7(e,t))),void e.Fa.delete(t);e.isPrimaryClient?(e.sharedClientState.removeLocalQueryTarget(r.targetId),e.sharedClientState.isActiveQueryTarget(r.targetId)||await sr(e.localStore,r.targetId,!1).then(()=>{e.sharedClientState.clearQueryState(r.targetId),n&&sB(e.remoteStore,r.targetId),aF(e,r.targetId)}).catch(er)):(aF(e,r.targetId),await sr(e.localStore,r.targetId,!0))}async function aD(e,t){let n=e.Fa.get(t),r=e.Ma.get(n.targetId);e.isPrimaryClient&&1===r.length&&(e.sharedClientState.removeLocalQueryTarget(n.targetId),sB(e.remoteStore,n.targetId))}async function aC(e,t,n){let r=aZ(e);try{var i;let e,s=await function(e,t){let n,r,i=U.now(),s=t.reduce((e,t)=>e.add(t.key),nf());return e.persistence.runTransaction("Locally write mutations","readwrite",a=>{let o=na,l=nf();return e.cs.getEntries(a,s).next(e=>{(o=e).forEach((e,t)=>{t.isValidDocument()||(l=l.add(e))})}).next(()=>e.localDocuments.getOverlayedDocuments(a,o)).next(r=>{n=r;let s=[];for(let e of t){let t=function(e,t){let n=null;for(let r of e.fieldTransforms){let e=t.data.field(r.field),i=nv(r.transform,e||null);null!=i&&(null===n&&(n=tb.empty()),n.set(r.field,i))}return n||null}(e,n.get(e.key).overlayedDocument);null!=t&&s.push(new nL(e.key,t,function e(t){let n=[];return ej(t.fields,(t,r)=>{let i=new G([t]);if(ty(r)){let t=e(r.mapValue).fields;if(0===t.length)n.push(i);else for(let e of t)n.push(i.child(e))}else n.push(i)}),new e1(n)}(t.value.mapValue),nA.exists(!0)))}return e.mutationQueue.addMutationBatch(a,i,s,t)}).next(t=>{r=t;let i=t.applyToLocalDocumentSet(n,l);return e.documentOverlayCache.saveOverlays(a,t.batchId,i)})}).then(()=>({batchId:r.batchId,changes:nu(n)}))}(r.localStore,t);r.sharedClientState.addPendingMutation(s.batchId),i=s.batchId,(e=r.Ba[r.currentUser.toKey()])||(e=new eH(L)),e=e.insert(i,n),r.Ba[r.currentUser.toKey()]=e,await aq(r,s.changes),await sZ(r.remoteStore)}catch(t){let e=at(t,"Failed to persist write");n.reject(e)}}async function aN(e,t){try{let n=await function(e,t){let n=t.snapshotVersion,r=e.os;return e.persistence.runTransaction("Apply remote event","readwrite-primary",i=>{let s=e.cs.newChangeBuffer({trackRemovals:!0});r=e.os;let a=[];t.targetChanges.forEach((s,o)=>{var l;let u=r.get(o);if(!u)return;a.push(e.Ur.removeMatchingKeys(i,s.removedDocuments,o).next(()=>e.Ur.addMatchingKeys(i,s.addedDocuments,o)));let h=u.withSequenceNumber(i.currentSequenceNumber);null!==t.targetMismatches.get(o)?h=h.withResumeToken(e5.EMPTY_BYTE_STRING,q.min()).withLastLimboFreeSnapshotVersion(q.min()):s.resumeToken.approximateByteSize()>0&&(h=h.withResumeToken(s.resumeToken,n)),r=r.insert(o,h),l=h,(0===u.resumeToken.approximateByteSize()||l.snapshotVersion.toMicroseconds()-u.snapshotVersion.toMicroseconds()>=3e8||s.addedDocuments.size+s.modifiedDocuments.size+s.removedDocuments.size>0)&&a.push(e.Ur.updateTargetData(i,h))});let o=na,l=nf();if(t.documentUpdates.forEach(n=>{t.resolvedLimboDocuments.has(n)&&a.push(e.persistence.referenceDelegate.updateLimboDocument(i,n))}),a.push(st(i,s,t.documentUpdates).next(e=>{o=e.Ps,l=e.Is})),!n.isEqual(q.min())){let t=e.Ur.getLastRemoteSnapshotVersion(i).next(t=>e.Ur.setTargetsMetadata(i,i.currentSequenceNumber,n));a.push(t)}return ei.waitFor(a).next(()=>s.apply(i)).next(()=>e.localDocuments.getLocalViewOfDocuments(i,o,l)).next(()=>o)}).then(t=>(e.os=r,t))}(e.localStore,t);t.targetChanges.forEach((t,n)=>{let r=e.Na.get(n);r&&(t.addedDocuments.size+t.modifiedDocuments.size+t.removedDocuments.size<=1||b(),t.addedDocuments.size>0?r.va=!0:t.modifiedDocuments.size>0?r.va||b():t.removedDocuments.size>0&&(r.va||b(),r.va=!1))}),await aq(e,n,t)}catch(e){await er(e)}}function aA(e,t,n){var r;if(e.isPrimaryClient&&0===n||!e.isPrimaryClient&&1===n){let n,i=[];e.Fa.forEach((e,n)=>{let r=n.view.Z_(t);r.snapshot&&i.push(r.snapshot)}),(r=e.eventManager).onlineState=t,n=!1,r.queries.forEach((e,r)=>{for(let e of r.j_)e.Z_(t)&&(n=!0)}),n&&ad(r),i.length&&e.Ca.d_(i),e.onlineState=t,e.isPrimaryClient&&e.sharedClientState.setOnlineState(t)}}async function ak(e,t,n){e.sharedClientState.updateQueryState(t,"rejected",n);let r=e.Na.get(t),i=r&&r.key;if(i){let n=new eH($.comparator);n=n.insert(i,tS.newNoDocument(i,q.min()));let r=nf().add(i),s=new n1(q.min(),new Map,new eH(L),n,r);await aN(e,s),e.Oa=e.Oa.remove(i),e.Na.delete(t),aU(e)}else await sr(e.localStore,t,!1).then(()=>aF(e,t,n)).catch(er)}async function aV(e,t){var n;let r=t.batch.batchId;try{let i=await (n=e.localStore,n.persistence.runTransaction("Acknowledge batch","readwrite-primary",e=>{let r=t.batch.keys(),i=n.cs.newChangeBuffer({trackRemovals:!0});return(function(e,t,n,r){let i=n.batch,s=i.keys(),a=ei.resolve();return s.forEach(e=>{a=a.next(()=>r.getEntry(t,e)).next(t=>{let s=n.docVersions.get(e);null!==s||b(),0>t.version.compareTo(s)&&(i.applyToRemoteDocument(t,n),t.isValidDocument()&&(t.setReadTime(n.commitVersion),r.addEntry(t)))})}),a.next(()=>e.mutationQueue.removeMutationBatch(t,i))})(n,e,t,i).next(()=>i.apply(e)).next(()=>n.mutationQueue.performConsistencyCheck(e)).next(()=>n.documentOverlayCache.removeOverlaysForBatchId(e,r,t.batch.batchId)).next(()=>n.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,function(e){let t=nf();for(let n=0;n<e.mutationResults.length;++n)e.mutationResults[n].transformResults.length>0&&(t=t.add(e.batch.mutations[n].key));return t}(t))).next(()=>n.localDocuments.getDocuments(e,r))}));aM(e,r,null),aO(e,r),e.sharedClientState.updateMutationState(r,"acknowledged"),await aq(e,i)}catch(e){await er(e)}}async function aR(e,t,n){var r;try{let i=await (r=e.localStore,r.persistence.runTransaction("Reject batch","readwrite-primary",e=>{let n;return r.mutationQueue.lookupMutationBatch(e,t).next(t=>(null!==t||b(),n=t.keys(),r.mutationQueue.removeMutationBatch(e,t))).next(()=>r.mutationQueue.performConsistencyCheck(e)).next(()=>r.documentOverlayCache.removeOverlaysForBatchId(e,n,t)).next(()=>r.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,n)).next(()=>r.localDocuments.getDocuments(e,n))}));aM(e,t,n),aO(e,t),e.sharedClientState.updateMutationState(t,"rejected",n),await aq(e,i)}catch(e){await er(e)}}function aO(e,t){(e.ka.get(t)||[]).forEach(e=>{e.resolve()}),e.ka.delete(t)}function aM(e,t,n){let r=e.Ba[e.currentUser.toKey()];if(r){let i=r.get(t);i&&(n?i.reject(n):i.resolve(),r=r.remove(t)),e.Ba[e.currentUser.toKey()]=r}}function aF(e,t,n=null){for(let r of(e.sharedClientState.removeLocalQueryTarget(t),e.Ma.get(t)))e.Fa.delete(r),n&&e.Ca.$a(r,n);e.Ma.delete(t),e.isPrimaryClient&&e.La.gr(t).forEach(t=>{e.La.containsKey(t)||aL(e,t)})}function aL(e,t){e.xa.delete(t.path.canonicalString());let n=e.Oa.get(t);null!==n&&(sB(e.remoteStore,n),e.Oa=e.Oa.remove(t),e.Na.delete(n),aU(e))}function aP(e,t,n){for(let r of n)r instanceof ap?(e.La.addReference(r.key,t),function(e,t){let n=t.key,r=n.path.canonicalString();e.Oa.get(n)||e.xa.has(r)||(I("SyncEngine","New document in limbo: "+n),e.xa.add(r),aU(e))}(e,r)):r instanceof ay?(I("SyncEngine","Document no longer in limbo: "+r.key),e.La.removeReference(r.key,t),e.La.containsKey(r.key)||aL(e,r.key)):b()}function aU(e){for(;e.xa.size>0&&e.Oa.size<e.maxConcurrentLimboResolutions;){let t=e.xa.values().next().value;e.xa.delete(t);let n=new $(K.fromString(t)),r=e.qa.next();e.Na.set(r,new aI(n)),e.Oa=e.Oa.insert(n,r),sq(e.remoteStore,new rD(t4(t1(n.path)),r,"TargetPurposeLimboResolution",ey.oe))}}async function aq(e,t,n){let r=[],i=[],s=[];e.Fa.isEmpty()||(e.Fa.forEach((a,o)=>{s.push(e.Ka(o,t,n).then(t=>{var s;if((t||n)&&e.isPrimaryClient){let r=t?!t.fromCache:null==(s=null==n?void 0:n.targetChanges.get(o.targetId))?void 0:s.current;e.sharedClientState.updateQueryState(o.targetId,r?"current":"not-current")}if(t){r.push(t);let e=i4.Wi(o.targetId,t);i.push(e)}}))}),await Promise.all(s),e.Ca.d_(r),await async function(e,t){try{await e.persistence.runTransaction("notifyLocalViewChanges","readwrite",n=>ei.forEach(t,t=>ei.forEach(t.$i,r=>e.persistence.referenceDelegate.addReference(n,t.targetId,r)).next(()=>ei.forEach(t.Ui,r=>e.persistence.referenceDelegate.removeReference(n,t.targetId,r)))))}catch(e){if(!eh(e))throw e;I("LocalStore","Failed to update sequence numbers: "+e)}for(let n of t){let t=n.targetId;if(!n.fromCache){let n=e.os.get(t),r=n.snapshotVersion,i=n.withLastLimboFreeSnapshotVersion(r);e.os=e.os.insert(t,i)}}}(e.localStore,i))}async function aB(e,t){if(!e.currentUser.isEqual(t)){I("SyncEngine","User change. New user:",t.toKey());let n=await i7(e.localStore,t);e.currentUser=t,e.ka.forEach(e=>{e.forEach(e=>{e.reject(new x(S.CANCELLED,"'waitForPendingWrites' promise is rejected due to a user change."))})}),e.ka.clear(),e.sharedClientState.handleUserChange(t,n.removedBatchIds,n.addedBatchIds),await aq(e,n.hs)}}function aK(e,t){let n=e.Na.get(t);if(n&&n.va)return nf().add(n.key);{let n=nf(),r=e.Ma.get(t);if(!r)return n;for(let t of r){let r=e.Fa.get(t);n=n.unionWith(r.view.Va)}return n}}async function az(e,t){let n=await si(e.localStore,t.query,!0),r=t.view.ba(n);return e.isPrimaryClient&&aP(e,t.targetId,r.wa),r}async function aG(e,t){return sa(e.localStore,t).then(t=>aq(e,t))}async function a$(e,t,n,r){var i;let s=await function(e,t){let n=e.mutationQueue;return e.persistence.runTransaction("Lookup mutation documents","readonly",r=>n.Mn(r,t).next(t=>t?e.localDocuments.getDocuments(r,t):ei.resolve(null)))}(e.localStore,t);null!==s?("pending"===n?await sZ(e.remoteStore):"acknowledged"===n||"rejected"===n?(aM(e,t,r||null),aO(e,t),i=e.localStore,i.mutationQueue.On(t)):b(),await aq(e,s)):I("SyncEngine","Cannot apply mutation batch with id: "+t)}async function aQ(e,t){if(aX(e),aZ(e),!0===t&&!0!==e.Qa){let t=e.sharedClientState.getAllActiveQueryTargets(),n=await aj(e,t.toArray());for(let t of(e.Qa=!0,await s6(e.remoteStore,!0),n))sq(e.remoteStore,t)}else if(!1===t&&!1!==e.Qa){let t=[],n=Promise.resolve();e.Ma.forEach((r,i)=>{e.sharedClientState.isLocalQueryTarget(i)?t.push(i):n=n.then(()=>(aF(e,i),sr(e.localStore,i,!0))),sB(e.remoteStore,i)}),await n,await aj(e,t),e.Na.forEach((t,n)=>{sB(e.remoteStore,n)}),e.La.pr(),e.Na=new Map,e.Oa=new eH($.comparator),e.Qa=!1,await s6(e.remoteStore,!1)}}async function aj(e,t,n){let r=[],i=[];for(let n of t){let t,s=e.Ma.get(n);if(s&&0!==s.length)for(let n of(t=await sn(e.localStore,t4(s[0])),s)){let t=e.Fa.get(n),r=await az(e,t);r.snapshot&&i.push(r.snapshot)}else{let r=await ss(e.localStore,n);t=await sn(e.localStore,r),await aS(e,aW(r),n,!1,t.resumeToken)}r.push(t)}return e.Ca.d_(i),r}function aW(e){var t,n,r,i,s;return t=e.path,n=e.collectionGroup,r=e.orderBy,i=e.filters,s=e.limit,new t0(t,n,r,i,s,"F",e.startAt,e.endAt)}function aH(e){return e.localStore.persistence.Qi()}async function aJ(e,t,n,r){if(e.Qa)return void I("SyncEngine","Ignoring unexpected query state notification.");let i=e.Ma.get(t);if(i&&i.length>0)switch(n){case"current":case"not-current":{let r=await sa(e.localStore,nr(i[0])),s=n1.createSynthesizedRemoteEventForCurrentChange(t,"current"===n,e5.EMPTY_BYTE_STRING);await aq(e,r,s);break}case"rejected":await sr(e.localStore,t,!0),aF(e,t,r);break;default:b()}}async function aY(e,t,n){let r=aX(e);if(r.Qa){for(let e of t){if(r.Ma.has(e)&&r.sharedClientState.isActiveQueryTarget(e)){I("SyncEngine","Adding an already active target "+e);continue}let t=await ss(r.localStore,e),n=await sn(r.localStore,t);await aS(r,aW(t),n.targetId,!1,n.resumeToken),sq(r.remoteStore,n)}for(let e of n)r.Ma.has(e)&&await sr(r.localStore,e,!1).then(()=>{sB(r.remoteStore,e),aF(r,e)}).catch(er)}}function aX(e){return e.remoteStore.remoteSyncer.applyRemoteEvent=aN.bind(null,e),e.remoteStore.remoteSyncer.getRemoteKeysForTarget=aK.bind(null,e),e.remoteStore.remoteSyncer.rejectListen=ak.bind(null,e),e.Ca.d_=ah.bind(null,e.eventManager),e.Ca.$a=ac.bind(null,e.eventManager),e}function aZ(e){return e.remoteStore.remoteSyncer.applySuccessfulWrite=aV.bind(null,e),e.remoteStore.remoteSyncer.rejectFailedWrite=aR.bind(null,e),e}class a0{constructor(){this.kind="memory",this.synchronizeTabs=!1}async initialize(e){this.serializer=sA(e.databaseInfo.databaseId),this.sharedClientState=this.Wa(e),this.persistence=this.Ga(e),await this.persistence.start(),this.localStore=this.za(e),this.gcScheduler=this.ja(e,this.localStore),this.indexBackfillerScheduler=this.Ha(e,this.localStore)}ja(e,t){return null}Ha(e,t){return null}za(e){var t,n;return t=this.persistence,n=new i6,new i9(t,n,e.initialUser,this.serializer)}Ga(e){return new iW(iJ.Zr,this.serializer)}Wa(e){return new sv}async terminate(){var e,t;null==(e=this.gcScheduler)||e.stop(),null==(t=this.indexBackfillerScheduler)||t.stop(),this.sharedClientState.shutdown(),await this.persistence.shutdown()}}a0.provider={build:()=>new a0};class a1 extends a0{constructor(e){super(),this.cacheSizeBytes=e}ja(e,t){return this.persistence.referenceDelegate instanceof iY||b(),new iS(this.persistence.referenceDelegate.garbageCollector,e.asyncQueue,t)}Ga(e){let t=void 0!==this.cacheSizeBytes?iu.withCacheSize(this.cacheSizeBytes):iu.DEFAULT;return new iW(e=>iY.Zr(e,t),this.serializer)}}class a2 extends a0{constructor(e,t,n){super(),this.Ja=e,this.cacheSizeBytes=t,this.forceOwnership=n,this.kind="persistent",this.synchronizeTabs=!1}async initialize(e){await super.initialize(e),await this.Ja.initialize(this,e),await aZ(this.Ja.syncEngine),await sZ(this.Ja.remoteStore),await this.persistence.yi(()=>(this.gcScheduler&&!this.gcScheduler.started&&this.gcScheduler.start(),this.indexBackfillerScheduler&&!this.indexBackfillerScheduler.started&&this.indexBackfillerScheduler.start(),Promise.resolve()))}za(e){var t,n;return t=this.persistence,n=new i6,new i9(t,n,e.initialUser,this.serializer)}ja(e,t){return new iS(this.persistence.referenceDelegate.garbageCollector,e.asyncQueue,t)}Ha(e,t){let n=new ep(t,this.persistence);return new eg(e.asyncQueue,n)}Ga(e){let t=i3(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey),n=void 0!==this.cacheSizeBytes?iu.withCacheSize(this.cacheSizeBytes):iu.DEFAULT;return new i1(this.synchronizeTabs,t,e.clientId,n,e.asyncQueue,sC(),sN(),this.serializer,this.sharedClientState,!!this.forceOwnership)}Wa(e){return new sv}}class a5 extends a2{constructor(e,t){super(e,t,!1),this.Ja=e,this.cacheSizeBytes=t,this.synchronizeTabs=!0}async initialize(e){await super.initialize(e);let t=this.Ja.syncEngine;this.sharedClientState instanceof sw&&(this.sharedClientState.syncEngine={no:a$.bind(null,t),ro:aJ.bind(null,t),io:aY.bind(null,t),Qi:aH.bind(null,t),eo:aG.bind(null,t)},await this.sharedClientState.start()),await this.persistence.yi(async e=>{await aQ(this.Ja.syncEngine,e),this.gcScheduler&&(e&&!this.gcScheduler.started?this.gcScheduler.start():e||this.gcScheduler.stop()),this.indexBackfillerScheduler&&(e&&!this.indexBackfillerScheduler.started?this.indexBackfillerScheduler.start():e||this.indexBackfillerScheduler.stop())})}Wa(e){let t=sC();if(!sw.D(t))throw new x(S.UNIMPLEMENTED,"IndexedDB persistence is only available on platforms that support LocalStorage.");let n=i3(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey);return new sw(t,e.asyncQueue,n,e.clientId,e.initialUser)}}class a3{async initialize(e,t){this.localStore||(this.localStore=e.localStore,this.sharedClientState=e.sharedClientState,this.datastore=this.createDatastore(t),this.remoteStore=this.createRemoteStore(t),this.eventManager=this.createEventManager(t),this.syncEngine=this.createSyncEngine(t,!e.synchronizeTabs),this.sharedClientState.onlineStateHandler=e=>aA(this.syncEngine,e,1),this.remoteStore.remoteSyncer.handleCredentialChange=aB.bind(null,this.syncEngine),await s6(this.remoteStore,this.syncEngine.isPrimaryClient))}createEventManager(e){return new aa}createDatastore(e){var t;let n=sA(e.databaseInfo.databaseId),r=new sD(e.databaseInfo);return t=e.authCredentials,new sM(t,e.appCheckCredentials,r,n)}createRemoteStore(e){var t,n;return t=this.localStore,n=this.datastore,new sL(t,n,e.asyncQueue,e=>aA(this.syncEngine,e,0),sT.D()?new sT:new sI)}createSyncEngine(e,t){return function(e,t,n,r,i,s,a){let o=new aT(e,t,n,r,i,s);return a&&(o.Qa=!0),o}(this.localStore,this.remoteStore,this.eventManager,this.sharedClientState,e.initialUser,e.maxConcurrentLimboResolutions,t)}async terminate(){var e,t;await async function(e){I("RemoteStore","RemoteStore shutting down."),e.L_.add(5),await sU(e),e.k_.shutdown(),e.q_.set("Unknown")}(this.remoteStore),null==(e=this.datastore)||e.terminate(),null==(t=this.eventManager)||t.terminate()}}a3.provider={build:()=>new a3};class a4{constructor(e){this.observer=e,this.muted=!1}next(e){this.muted||this.observer.next&&this.Ya(this.observer.next,e)}error(e){this.muted||(this.observer.error?this.Ya(this.observer.error,e):T("Uncaught Error in snapshot listener:",e.toString()))}Za(){this.muted=!0}Ya(e,t){setTimeout(()=>{this.muted||e(t)},0)}}class a8{constructor(e){this.datastore=e,this.readVersions=new Map,this.mutations=[],this.committed=!1,this.lastTransactionError=null,this.writtenDocs=new Set}async lookup(e){if(this.ensureCommitNotCalled(),this.mutations.length>0)throw this.lastTransactionError=new x(S.INVALID_ARGUMENT,"Firestore transactions require all reads to be executed before all writes."),this.lastTransactionError;let t=await async function(e,t){let n={documents:t.map(t=>rc(e.serializer,t))},r=await e.Lo("BatchGetDocuments",e.serializer.databaseId,K.emptyPath(),n,t.length),i=new Map;r.forEach(t=>{var n;let r=(n=e.serializer,"found"in t?function(e,t){t.found||b(),t.found.name,t.found.updateTime;let n=rd(e,t.found.name),r=ro(t.found.updateTime),i=t.found.createTime?ro(t.found.createTime):q.min(),s=new tb({mapValue:{fields:t.found.fields}});return tS.newFoundDocument(n,r,i,s)}(n,t):"missing"in t?function(e,t){t.missing||b(),t.readTime||b();let n=rd(e,t.missing),r=ro(t.readTime);return tS.newNoDocument(n,r)}(n,t):b());i.set(r.key.toString(),r)});let s=[];return t.forEach(e=>{let t=i.get(e.toString());t||b(),s.push(t)}),s}(this.datastore,e);return t.forEach(e=>this.recordVersion(e)),t}set(e,t){this.write(t.toMutation(e,this.precondition(e))),this.writtenDocs.add(e.toString())}update(e,t){try{this.write(t.toMutation(e,this.preconditionForUpdate(e)))}catch(e){this.lastTransactionError=e}this.writtenDocs.add(e.toString())}delete(e){this.write(new nB(e,this.precondition(e))),this.writtenDocs.add(e.toString())}async commit(){if(this.ensureCommitNotCalled(),this.lastTransactionError)throw this.lastTransactionError;let e=this.readVersions;this.mutations.forEach(t=>{e.delete(t.key.toString())}),e.forEach((e,t)=>{let n=$.fromPath(t);this.mutations.push(new nK(n,this.precondition(n)))}),await async function(e,t){let n={writes:t.map(t=>rv(e.serializer,t))};await e.Mo("Commit",e.serializer.databaseId,K.emptyPath(),n)}(this.datastore,this.mutations),this.committed=!0}recordVersion(e){let t;if(e.isFoundDocument())t=e.version;else{if(!e.isNoDocument())throw b();t=q.min()}let n=this.readVersions.get(e.key.toString());if(n){if(!t.isEqual(n))throw new x(S.ABORTED,"Document version changed between two reads.")}else this.readVersions.set(e.key.toString(),t)}precondition(e){let t=this.readVersions.get(e.toString());return!this.writtenDocs.has(e.toString())&&t?t.isEqual(q.min())?nA.exists(!1):nA.updateTime(t):nA.none()}preconditionForUpdate(e){let t=this.readVersions.get(e.toString());if(!this.writtenDocs.has(e.toString())&&t){if(t.isEqual(q.min()))throw new x(S.INVALID_ARGUMENT,"Can't update a document that doesn't exist.");return nA.updateTime(t)}return nA.exists(!0)}write(e){this.ensureCommitNotCalled(),this.mutations.push(e)}ensureCommitNotCalled(){}}class a6{constructor(e,t,n,r,i){this.authCredentials=e,this.appCheckCredentials=t,this.asyncQueue=n,this.databaseInfo=r,this.user=p.UNAUTHENTICATED,this.clientId=F.newId(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this._uninitializedComponentsProvider=i,this.authCredentials.start(n,async e=>{I("FirestoreClient","Received user=",e.uid),await this.authCredentialListener(e),this.user=e}),this.appCheckCredentials.start(n,e=>(I("FirestoreClient","Received new app check token=",e),this.appCheckCredentialListener(e,this.user)))}get configuration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(e){this.authCredentialListener=e}setAppCheckTokenChangeListener(e){this.appCheckCredentialListener=e}terminate(){this.asyncQueue.enterRestrictedMode();let e=new D;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted(async()=>{try{this._onlineComponents&&await this._onlineComponents.terminate(),this._offlineComponents&&await this._offlineComponents.terminate(),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),e.resolve()}catch(n){let t=at(n,"Failed to shutdown persistence");e.reject(t)}}),e.promise}}async function a9(e,t){e.asyncQueue.verifyOperationInProgress(),I("FirestoreClient","Initializing OfflineComponentProvider");let n=e.configuration;await t.initialize(n);let r=n.initialUser;e.setCredentialChangeListener(async e=>{r.isEqual(e)||(await i7(t.localStore,e),r=e)}),t.persistence.setDatabaseDeletedListener(()=>e.terminate()),e._offlineComponents=t}async function a7(e,t){e.asyncQueue.verifyOperationInProgress();let n=await oe(e);I("FirestoreClient","Initializing OnlineComponentProvider"),await t.initialize(n,e.configuration),e.setCredentialChangeListener(e=>s8(t.remoteStore,e)),e.setAppCheckTokenChangeListener((e,n)=>s8(t.remoteStore,n)),e._onlineComponents=t}async function oe(e){if(!e._offlineComponents)if(e._uninitializedComponentsProvider){I("FirestoreClient","Using user provided OfflineComponentProvider");try{await a9(e,e._uninitializedComponentsProvider._offline)}catch(t){if(!("FirebaseError"===t.name?t.code===S.FAILED_PRECONDITION||t.code===S.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&t instanceof DOMException)||22===t.code||20===t.code||11===t.code))throw t;_("Error using user provided cache. Falling back to memory cache: "+t),await a9(e,new a0)}}else I("FirestoreClient","Using default OfflineComponentProvider"),await a9(e,new a0);return e._offlineComponents}async function ot(e){return e._onlineComponents||(e._uninitializedComponentsProvider?(I("FirestoreClient","Using user provided OnlineComponentProvider"),await a7(e,e._uninitializedComponentsProvider._online)):(I("FirestoreClient","Using default OnlineComponentProvider"),await a7(e,new a3))),e._onlineComponents}async function on(e){let t=await ot(e),n=t.eventManager;return n.onListen=a_.bind(null,t.syncEngine),n.onUnlisten=ax.bind(null,t.syncEngine),n.onFirstRemoteStoreListen=aE.bind(null,t.syncEngine),n.onLastRemoteStoreUnlisten=aD.bind(null,t.syncEngine),n}function or(e){let t={};return void 0!==e.timeoutSeconds&&(t.timeoutSeconds=e.timeoutSeconds),t}let oi=new Map;function os(e,t,n){if(!n)throw new x(S.INVALID_ARGUMENT,`Function ${e}() cannot be called with an empty ${t}.`)}function oa(e){if(!$.isDocumentKey(e))throw new x(S.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${e} has ${e.length}.`)}function oo(e){if($.isDocumentKey(e))throw new x(S.INVALID_ARGUMENT,`Invalid collection reference. Collection references must have an odd number of segments, but ${e} has ${e.length}.`)}function ol(e){if(void 0===e)return"undefined";if(null===e)return"null";if("string"==typeof e)return e.length>20&&(e=`${e.substring(0,20)}...`),JSON.stringify(e);if("number"==typeof e||"boolean"==typeof e)return""+e;if("object"==typeof e){if(e instanceof Array)return"an array";{var t;let n=(t=e).constructor?t.constructor.name:null;return n?`a custom ${n} object`:"an object"}}return"function"==typeof e?"a function":b()}function ou(e,t){if("_delegate"in e&&(e=e._delegate),!(e instanceof t)){if(t.name===e.constructor.name)throw new x(S.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");{let n=ol(e);throw new x(S.INVALID_ARGUMENT,`Expected type '${t.name}', but it was: ${n}`)}}return e}class oh{constructor(e){var t,n;if(void 0===e.host){if(void 0!==e.ssl)throw new x(S.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host="firestore.googleapis.com",this.ssl=!0}else this.host=e.host,this.ssl=null==(t=e.ssl)||t;if(this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,void 0===e.cacheSizeBytes)this.cacheSizeBytes=0x2800000;else{if(-1!==e.cacheSizeBytes&&e.cacheSizeBytes<1048576)throw new x(S.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}(function(e,t,n,r){if(!0===t&&!0===r)throw new x(S.INVALID_ARGUMENT,`${e} and ${n} cannot be used together.`)})("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===e.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=or(null!=(n=e.experimentalLongPollingOptions)?n:{}),function(e){if(void 0!==e.timeoutSeconds){if(isNaN(e.timeoutSeconds))throw new x(S.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (must not be NaN)`);if(e.timeoutSeconds<5)throw new x(S.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (minimum allowed value is 5)`);if(e.timeoutSeconds>30)throw new x(S.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (maximum allowed value is 30)`)}}(this.experimentalLongPollingOptions),this.useFetchStreams=!!e.useFetchStreams}isEqual(e){var t,n;return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&(t=this.experimentalLongPollingOptions,n=e.experimentalLongPollingOptions,t.timeoutSeconds===n.timeoutSeconds)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams}}class oc{constructor(e,t,n,r){this._authCredentials=e,this._appCheckCredentials=t,this._databaseId=n,this._app=r,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new oh({}),this._settingsFrozen=!1,this._terminateTask="notTerminated"}get app(){if(!this._app)throw new x(S.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app}get _initialized(){return this._settingsFrozen}get _terminated(){return"notTerminated"!==this._terminateTask}_setSettings(e){if(this._settingsFrozen)throw new x(S.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new oh(e),void 0!==e.credentials&&(this._authCredentials=function(e){if(!e)return new N;switch(e.type){case"firstParty":return new R(e.sessionIndex||"0",e.iamToken||null,e.authTokenFactory||null);case"provider":return e.client;default:throw new x(S.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}}(e.credentials))}_getSettings(){return this._settings}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return"notTerminated"===this._terminateTask&&(this._terminateTask=this._terminate()),this._terminateTask}async _restart(){"notTerminated"===this._terminateTask?await this._terminate():this._terminateTask="notTerminated"}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return function(e){let t=oi.get(e);t&&(I("ComponentProvider","Removing Datastore"),oi.delete(e),t.terminate())}(this),Promise.resolve()}}class od{constructor(e,t,n){this.converter=t,this._query=n,this.type="query",this.firestore=e}withConverter(e){return new od(this.firestore,e,this._query)}}class of{constructor(e,t,n){this.converter=t,this._key=n,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new om(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new of(this.firestore,e,this._key)}}class om extends od{constructor(e,t,n){super(e,t,t1(n)),this._path=n,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){let e=this._path.popLast();return e.isEmpty()?null:new of(this.firestore,null,new $(e))}withConverter(e){return new om(this.firestore,e,this._path)}}function og(e,t,...n){if(e=(0,h.Ku)(e),os("collection","path",t),e instanceof oc){let r=K.fromString(t,...n);return oo(r),new om(e,null,r)}{if(!(e instanceof of||e instanceof om))throw new x(S.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");let r=e._path.child(K.fromString(t,...n));return oo(r),new om(e.firestore,null,r)}}function op(e,t,...n){if(e=(0,h.Ku)(e),1==arguments.length&&(t=F.newId()),os("doc","path",t),e instanceof oc){let r=K.fromString(t,...n);return oa(r),new of(e,null,new $(r))}{if(!(e instanceof of||e instanceof om))throw new x(S.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");let r=e._path.child(K.fromString(t,...n));return oa(r),new of(e.firestore,e instanceof om?e.converter:null,new $(r))}}class oy{constructor(e=Promise.resolve()){this.Pu=[],this.Iu=!1,this.Tu=[],this.Eu=null,this.du=!1,this.Au=!1,this.Ru=[],this.t_=new sk(this,"async_queue_retry"),this.Vu=()=>{let e=sN();e&&I("AsyncQueue","Visibility state changed to "+e.visibilityState),this.t_.jo()},this.mu=e;let t=sN();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this.Vu)}get isShuttingDown(){return this.Iu}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.fu(),this.gu(e)}enterRestrictedMode(e){if(!this.Iu){this.Iu=!0,this.Au=e||!1;let t=sN();t&&"function"==typeof t.removeEventListener&&t.removeEventListener("visibilitychange",this.Vu)}}enqueue(e){if(this.fu(),this.Iu)return new Promise(()=>{});let t=new D;return this.gu(()=>this.Iu&&this.Au?Promise.resolve():(e().then(t.resolve,t.reject),t.promise)).then(()=>t.promise)}enqueueRetryable(e){this.enqueueAndForget(()=>(this.Pu.push(e),this.pu()))}async pu(){if(0!==this.Pu.length){try{await this.Pu[0](),this.Pu.shift(),this.t_.reset()}catch(e){if(!eh(e))throw e;I("AsyncQueue","Operation failed with retryable error: "+e)}this.Pu.length>0&&this.t_.Go(()=>this.pu())}}gu(e){let t=this.mu.then(()=>(this.du=!0,e().catch(e=>{let t;throw this.Eu=e,this.du=!1,T("INTERNAL UNHANDLED ERROR: ",(t=e.message||"",e.stack&&(t=e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack),t)),e}).then(e=>(this.du=!1,e))));return this.mu=t,t}enqueueAfterDelay(e,t,n){this.fu(),this.Ru.indexOf(e)>-1&&(t=0);let r=ae.createAndSchedule(this,e,t,n,e=>this.yu(e));return this.Tu.push(r),r}fu(){this.Eu&&b()}verifyOperationInProgress(){}async wu(){let e;do e=this.mu,await e;while(e!==this.mu)}Su(e){for(let t of this.Tu)if(t.timerId===e)return!0;return!1}bu(e){return this.wu().then(()=>{for(let t of(this.Tu.sort((e,t)=>e.targetTimeMs-t.targetTimeMs),this.Tu))if(t.skipDelay(),"all"!==e&&t.timerId===e)break;return this.wu()})}Du(e){this.Ru.push(e)}yu(e){let t=this.Tu.indexOf(e);this.Tu.splice(t,1)}}class ow extends oc{constructor(e,t,n,r){super(e,t,n,r),this.type="firestore",this._queue=new oy,this._persistenceKey=(null==r?void 0:r.name)||"[DEFAULT]"}async _terminate(){if(this._firestoreClient){let e=this._firestoreClient.terminate();this._queue=new oy(e),this._firestoreClient=void 0,await e}}}function ov(e,t){let n="object"==typeof e?e:(0,o.Sx)(),r=(0,o.j6)(n,"firestore").getImmediate({identifier:"string"==typeof e?e:t||"(default)"});if(!r._initialized){let e=(0,h.yU)("firestore");e&&function(e,t,n,r={}){var i;let s=(e=ou(e,oc))._getSettings(),a=`${t}:${n}`;if("firestore.googleapis.com"!==s.host&&s.host!==a&&_("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used."),e._setSettings(Object.assign(Object.assign({},s),{host:a,ssl:!1})),r.mockUserToken){let t,n;if("string"==typeof r.mockUserToken)t=r.mockUserToken,n=p.MOCK_USER;else{t=(0,h.Fy)(r.mockUserToken,null==(i=e._app)?void 0:i.options.projectId);let s=r.mockUserToken.sub||r.mockUserToken.user_id;if(!s)throw new x(S.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");n=new p(s)}e._authCredentials=new A(new C(t,n))}}(r,...e)}return r}function oI(e){if(e._terminated)throw new x(S.FAILED_PRECONDITION,"The client has already been terminated.");return e._firestoreClient||oT(e),e._firestoreClient}function oT(e){var t,n,r,i,s;let a=e._freezeSettings(),o=(i=e._databaseId,s=(null==(t=e._app)?void 0:t.options.appId)||"",new tt(i,s,e._persistenceKey,a.host,a.ssl,a.experimentalForceLongPolling,a.experimentalAutoDetectLongPolling,or(a.experimentalLongPollingOptions),a.useFetchStreams));e._componentsProvider||(null==(n=a.localCache)?void 0:n._offlineComponentProvider)&&(null==(r=a.localCache)?void 0:r._onlineComponentProvider)&&(e._componentsProvider={_offline:a.localCache._offlineComponentProvider,_online:a.localCache._onlineComponentProvider}),e._firestoreClient=new a6(e._authCredentials,e._appCheckCredentials,e._queue,o,e._componentsProvider&&function(e){let t=null==e?void 0:e._online.build();return{_offline:null==e?void 0:e._offline.build(t),_online:t}}(e._componentsProvider))}class o_{constructor(e="count",t){this._internalFieldPath=t,this.type="AggregateField",this.aggregateType=e}}class oE{constructor(e,t,n){this._userDataWriter=t,this._data=n,this.type="AggregateQuerySnapshot",this.query=e}data(){return this._userDataWriter.convertObjectMap(this._data)}}class ob{constructor(e){this._byteString=e}static fromBase64String(e){try{return new ob(e5.fromBase64String(e))}catch(e){throw new x(S.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+e)}}static fromUint8Array(e){return new ob(e5.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}}class oS{constructor(...e){for(let t=0;t<e.length;++t)if(0===e[t].length)throw new x(S.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new G(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}}class ox{constructor(e){this._methodName=e}}class oD{constructor(e,t){if(!isFinite(e)||e<-90||e>90)throw new x(S.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(t)||t<-180||t>180)throw new x(S.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+t);this._lat=e,this._long=t}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}toJSON(){return{latitude:this._lat,longitude:this._long}}_compareTo(e){return L(this._lat,e._lat)||L(this._long,e._long)}}class oC{constructor(e){this._values=(e||[]).map(e=>e)}toArray(){return this._values.map(e=>e)}isEqual(e){return function(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(e[n]!==t[n])return!1;return!0}(this._values,e._values)}}let oN=/^__.*__$/;class oA{constructor(e,t,n){this.data=e,this.fieldMask=t,this.fieldTransforms=n}toMutation(e,t){return null!==this.fieldMask?new nL(e,this.data,this.fieldMask,t,this.fieldTransforms):new nF(e,this.data,t,this.fieldTransforms)}}class ok{constructor(e,t,n){this.data=e,this.fieldMask=t,this.fieldTransforms=n}toMutation(e,t){return new nL(e,this.data,this.fieldMask,t,this.fieldTransforms)}}function oV(e){switch(e){case 0:case 2:case 1:return!0;case 3:case 4:return!1;default:throw b()}}class oR{constructor(e,t,n,r,i,s){this.settings=e,this.databaseId=t,this.serializer=n,this.ignoreUndefinedProperties=r,void 0===i&&this.vu(),this.fieldTransforms=i||[],this.fieldMask=s||[]}get path(){return this.settings.path}get Cu(){return this.settings.Cu}Fu(e){return new oR(Object.assign(Object.assign({},this.settings),e),this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}Mu(e){var t;let n=null==(t=this.path)?void 0:t.child(e),r=this.Fu({path:n,xu:!1});return r.Ou(e),r}Nu(e){var t;let n=null==(t=this.path)?void 0:t.child(e),r=this.Fu({path:n,xu:!1});return r.vu(),r}Lu(e){return this.Fu({path:void 0,xu:!0})}Bu(e){return oZ(e,this.settings.methodName,this.settings.ku||!1,this.path,this.settings.qu)}contains(e){return void 0!==this.fieldMask.find(t=>e.isPrefixOf(t))||void 0!==this.fieldTransforms.find(t=>e.isPrefixOf(t.field))}vu(){if(this.path)for(let e=0;e<this.path.length;e++)this.Ou(this.path.get(e))}Ou(e){if(0===e.length)throw this.Bu("Document fields must not be empty");if(oV(this.Cu)&&oN.test(e))throw this.Bu('Document fields cannot begin and end with "__"')}}class oO{constructor(e,t,n){this.databaseId=e,this.ignoreUndefinedProperties=t,this.serializer=n||sA(e)}Qu(e,t,n,r=!1){return new oR({Cu:e,methodName:t,qu:n,path:G.emptyPath(),xu:!1,ku:r},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}}function oM(e){let t=e._freezeSettings(),n=sA(e._databaseId);return new oO(e._databaseId,!!t.ignoreUndefinedProperties,n)}function oF(e,t,n,r,i,s={}){let a,o,l=e.Qu(s.merge||s.mergeFields?2:0,t,n,i);oH("Data must be an object, but it was:",l,r);let u=oj(r,l);if(s.merge)a=new e1(l.fieldMask),o=l.fieldTransforms;else if(s.mergeFields){let e=[];for(let r of s.mergeFields){let i=oJ(t,r,n);if(!l.contains(i))throw new x(S.INVALID_ARGUMENT,`Field '${i}' is specified in your field mask but missing from your input data.`);o0(e,i)||e.push(i)}a=new e1(e),o=l.fieldTransforms.filter(e=>a.covers(e.field))}else a=null,o=l.fieldTransforms;return new oA(new tb(u),a,o)}class oL extends ox{_toFieldTransform(e){if(2!==e.Cu)throw 1===e.Cu?e.Bu(`${this._methodName}() can only appear at the top level of your update data`):e.Bu(`${this._methodName}() cannot be used with set() unless you pass {merge:true}`);return e.fieldMask.push(e.path),null}isEqual(e){return e instanceof oL}}function oP(e,t,n){return new oR({Cu:3,qu:t.settings.qu,methodName:e._methodName,xu:n},t.databaseId,t.serializer,t.ignoreUndefinedProperties)}class oU extends null{_toFieldTransform(e){return new nC(e.path,new nI)}isEqual(e){return e instanceof oU}}class oq extends ox{constructor(e,t){super(e),this.Ku=t}_toFieldTransform(e){let t=oP(this,e,!0),n=new nT(this.Ku.map(e=>oQ(e,t)));return new nC(e.path,n)}isEqual(e){return e instanceof oq&&(0,h.bD)(this.Ku,e.Ku)}}class oB extends ox{constructor(e,t){super(e),this.Ku=t}_toFieldTransform(e){let t=oP(this,e,!0),n=new nE(this.Ku.map(e=>oQ(e,t)));return new nC(e.path,n)}isEqual(e){return e instanceof oB&&(0,h.bD)(this.Ku,e.Ku)}}class oK extends ox{constructor(e,t){super(e),this.$u=t}_toFieldTransform(e){let t=new nS(e.serializer,ny(e.serializer,this.$u));return new nC(e.path,t)}isEqual(e){return e instanceof oK&&this.$u===e.$u}}function oz(e,t,n,r){let i=e.Qu(1,t,n);oH("Data must be an object, but it was:",i,r);let s=[],a=tb.empty();return ej(r,(e,r)=>{let o=oX(t,e,n);r=(0,h.Ku)(r);let l=i.Nu(o);if(r instanceof oL)s.push(o);else{let e=oQ(r,l);null!=e&&(s.push(o),a.set(o,e))}}),new ok(a,new e1(s),i.fieldTransforms)}function oG(e,t,n,r,i,s){let a=e.Qu(1,t,n),o=[oJ(t,r,n)],l=[i];if(s.length%2!=0)throw new x(S.INVALID_ARGUMENT,`Function ${t}() needs to be called with an even number of arguments that alternate between field names and values.`);for(let e=0;e<s.length;e+=2)o.push(oJ(t,s[e])),l.push(s[e+1]);let u=[],c=tb.empty();for(let e=o.length-1;e>=0;--e)if(!o0(u,o[e])){let t=o[e],n=l[e];n=(0,h.Ku)(n);let r=a.Nu(t);if(n instanceof oL)u.push(t);else{let e=oQ(n,r);null!=e&&(u.push(t),c.set(t,e))}}return new ok(c,new e1(u),a.fieldTransforms)}function o$(e,t,n,r=!1){return oQ(n,e.Qu(r?4:3,t))}function oQ(e,t){if(oW(e=(0,h.Ku)(e)))return oH("Unsupported field value:",t,e),oj(e,t);if(e instanceof ox)return function(e,t){if(!oV(t.Cu))throw t.Bu(`${e._methodName}() can only be used with update() and set()`);if(!t.path)throw t.Bu(`${e._methodName}() is not currently supported inside arrays`);let n=e._toFieldTransform(t);n&&t.fieldTransforms.push(n)}(e,t),null;if(void 0===e&&t.ignoreUndefinedProperties)return null;if(t.path&&t.fieldMask.push(t.path),e instanceof Array){if(t.settings.xu&&4!==t.Cu)throw t.Bu("Nested arrays are not supported");let n=[],r=0;for(let i of e){let e=oQ(i,t.Lu(r));null==e&&(e={nullValue:"NULL_VALUE"}),n.push(e),r++}return{arrayValue:{values:n}}}return function(e,t){if(null===(e=(0,h.Ku)(e)))return{nullValue:"NULL_VALUE"};if("number"==typeof e)return ny(t.serializer,e);if("boolean"==typeof e)return{booleanValue:e};if("string"==typeof e)return{stringValue:e};if(e instanceof Date){let n=U.fromDate(e);return{timestampValue:rs(t.serializer,n)}}if(e instanceof U){let n=new U(e.seconds,1e3*Math.floor(e.nanoseconds/1e3));return{timestampValue:rs(t.serializer,n)}}if(e instanceof oD)return{geoPointValue:{latitude:e.latitude,longitude:e.longitude}};if(e instanceof ob)return{bytesValue:ra(t.serializer,e._byteString)};if(e instanceof of){let n=t.databaseId,r=e.firestore._databaseId;if(!r.isEqual(n))throw t.Bu(`Document reference is for database ${r.projectId}/${r.database} but should be for database ${n.projectId}/${n.database}`);return{referenceValue:rl(e.firestore._databaseId||t.databaseId,e._key.path)}}if(e instanceof oC)return{mapValue:{fields:{__type__:{stringValue:"__vector__"},value:{arrayValue:{values:e.toArray().map(e=>{if("number"!=typeof e)throw t.Bu("VectorValues must only contain numeric values.");return ng(t.serializer,e)})}}}}};throw t.Bu(`Unsupported field value: ${ol(e)}`)}(e,t)}function oj(e,t){let n={};return eW(e)?t.path&&t.path.length>0&&t.fieldMask.push(t.path):ej(e,(e,r)=>{let i=oQ(r,t.Mu(e));null!=i&&(n[e]=i)}),{mapValue:{fields:n}}}function oW(e){return!("object"!=typeof e||null===e||e instanceof Array||e instanceof Date||e instanceof U||e instanceof oD||e instanceof ob||e instanceof of||e instanceof ox||e instanceof oC)}function oH(e,t,n){if(!oW(n)||"object"!=typeof n||null===n||Object.getPrototypeOf(n)!==Object.prototype&&null!==Object.getPrototypeOf(n)){let r=ol(n);throw"an object"===r?t.Bu(e+" a custom object"):t.Bu(e+" "+r)}}function oJ(e,t,n){if((t=(0,h.Ku)(t))instanceof oS)return t._internalPath;if("string"==typeof t)return oX(e,t);throw oZ("Field path arguments must be of type string or ",e,!1,void 0,n)}let oY=RegExp("[~\\*/\\[\\]]");function oX(e,t,n){if(t.search(oY)>=0)throw oZ(`Invalid field path (${t}). Paths must not contain '~', '*', '/', '[', or ']'`,e,!1,void 0,n);try{return new oS(...t.split("."))._internalPath}catch(r){throw oZ(`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,e,!1,void 0,n)}}function oZ(e,t,n,r,i){let s=r&&!r.isEmpty(),a=void 0!==i,o=`Function ${t}() called with invalid data`;n&&(o+=" (via `toFirestore()`)"),o+=". ";let l="";return(s||a)&&(l+=" (found",s&&(l+=` in field ${r}`),a&&(l+=` in document ${i}`),l+=")"),new x(S.INVALID_ARGUMENT,o+e+l)}function o0(e,t){return e.some(e=>e.isEqual(t))}class o1{constructor(e,t,n,r,i){this._firestore=e,this._userDataWriter=t,this._key=n,this._document=r,this._converter=i}get id(){return this._key.path.lastSegment()}get ref(){return new of(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){if(this._document){if(this._converter){let e=new o2(this._firestore,this._userDataWriter,this._key,this._document,null);return this._converter.fromFirestore(e)}return this._userDataWriter.convertValue(this._document.data.value)}}get(e){if(this._document){let t=this._document.data.field(o5("DocumentSnapshot.get",e));if(null!==t)return this._userDataWriter.convertValue(t)}}}class o2 extends o1{data(){return super.data()}}function o5(e,t){return"string"==typeof t?oX(e,t):t instanceof oS?t._internalPath:t._delegate._internalPath}class o3{}class o4 extends o3{}function o8(e,t,...n){let r=[];for(let i of(t instanceof o3&&r.push(t),function(e){let t=e.filter(e=>e instanceof o7).length,n=e.filter(e=>e instanceof o6).length;if(t>1||t>0&&n>0)throw new x(S.INVALID_ARGUMENT,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.")}(r=r.concat(n)),r))e=i._apply(e);return e}class o6 extends o4{constructor(e,t,n){super(),this._field=e,this._op=t,this._value=n,this.type="where"}static _create(e,t,n){return new o6(e,t,n)}_apply(e){let t=this._parse(e);return lh(e._query,t),new od(e.firestore,e.converter,t6(e._query,t))}_parse(e){let t=oM(e.firestore);return function(e,t,n,r,i,s,a){let o;if(i.isKeyField()){if("array-contains"===s||"array-contains-any"===s)throw new x(S.INVALID_ARGUMENT,`Invalid Query. You can't perform '${s}' queries on documentId().`);if("in"===s||"not-in"===s){lu(a,s);let t=[];for(let n of a)t.push(ll(r,e,n));o={arrayValue:{values:t}}}else o=ll(r,e,a)}else"in"!==s&&"not-in"!==s&&"array-contains-any"!==s||lu(a,s),o=o$(n,t,a,"in"===s||"not-in"===s);return tk.create(i,s,o)}(e._query,"where",t,e.firestore._databaseId,this._field,this._op,this._value)}}function o9(e,t,n){let r=o5("where",e);return o6._create(r,t,n)}class o7 extends o3{constructor(e,t){super(),this.type=e,this._queryConstraints=t}static _create(e,t){return new o7(e,t)}_parse(e){let t=this._queryConstraints.map(t=>t._parse(e)).filter(e=>e.getFilters().length>0);return 1===t.length?t[0]:tV.create(t,this._getOperator())}_apply(e){let t=this._parse(e);return 0===t.getFilters().length?e:(function(e,t){let n=e;for(let e of t.getFlattenedFilters())lh(n,e),n=t6(n,e)}(e._query,t),new od(e.firestore,e.converter,t6(e._query,t)))}_getQueryConstraints(){return this._queryConstraints}_getOperator(){return"and"===this.type?"and":"or"}}class le extends o4{constructor(e,t){super(),this._field=e,this._direction=t,this.type="orderBy"}static _create(e,t){return new le(e,t)}_apply(e){let t=function(e,t,n){if(null!==e.startAt)throw new x(S.INVALID_ARGUMENT,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(null!==e.endAt)throw new x(S.INVALID_ARGUMENT,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");return new tN(t,n)}(e._query,this._field,this._direction);return new od(e.firestore,e.converter,function(e,t){let n=e.explicitOrderBy.concat([t]);return new t0(e.path,e.collectionGroup,n,e.filters.slice(),e.limit,e.limitType,e.startAt,e.endAt)}(e._query,t))}}function lt(e,t="asc"){let n=o5("orderBy",e);return le._create(n,t)}class ln extends o4{constructor(e,t,n){super(),this.type=e,this._limit=t,this._limitType=n}static _create(e,t,n){return new ln(e,t,n)}_apply(e){return new od(e.firestore,e.converter,t9(e._query,this._limit,this._limitType))}}function lr(e){return function(e,t){if(t<=0)throw new x(S.INVALID_ARGUMENT,`Function ${e}() requires a positive number, but it was: ${t}.`)}("limit",e),ln._create("limit",e,"F")}class li extends o4{constructor(e,t,n){super(),this.type=e,this._docOrFields=t,this._inclusive=n}static _create(e,t,n){return new li(e,t,n)}_apply(e){var t;let n=lo(e,this.type,this._docOrFields,this._inclusive);return new od(e.firestore,e.converter,(t=e._query,new t0(t.path,t.collectionGroup,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,n,t.endAt)))}}function ls(...e){return li._create("startAfter",e,!1)}class la extends o4{constructor(e,t,n){super(),this.type=e,this._docOrFields=t,this._inclusive=n}static _create(e,t,n){return new la(e,t,n)}_apply(e){var t;let n=lo(e,this.type,this._docOrFields,this._inclusive);return new od(e.firestore,e.converter,(t=e._query,new t0(t.path,t.collectionGroup,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,n)))}}function lo(e,t,n,r){if(n[0]=(0,h.Ku)(n[0]),n[0]instanceof o1)return function(e,t,n,r,i){if(!r)throw new x(S.NOT_FOUND,`Can't use a DocumentSnapshot that doesn't exist for ${n}().`);let s=[];for(let n of t3(e))if(n.field.isKeyField())s.push(td(t,r.key));else{let e=r.data.field(n.field);if(e9(e))throw new x(S.INVALID_ARGUMENT,'Invalid query. You are trying to start or end a query using a document for which the field "'+n.field+'" is an uncommitted server timestamp. (Since the value of this field is unknown, you cannot start/end a query with it.)');if(null===e){let e=n.field.canonicalString();throw new x(S.INVALID_ARGUMENT,`Invalid query. You are trying to start or end a query using a document for which the field '${e}' (used as the orderBy) does not exist.`)}s.push(e)}return new tx(s,i)}(e._query,e.firestore._databaseId,t,n[0]._document,r);{let i=oM(e.firestore);return function(e,t,n,r,i,s){let a=e.explicitOrderBy;if(i.length>a.length)throw new x(S.INVALID_ARGUMENT,`Too many arguments provided to ${r}(). The number of arguments must be less than or equal to the number of orderBy() clauses`);let o=[];for(let s=0;s<i.length;s++){let l=i[s];if(a[s].field.isKeyField()){if("string"!=typeof l)throw new x(S.INVALID_ARGUMENT,`Invalid query. Expected a string for document ID in ${r}(), but got a ${typeof l}`);if(!t5(e)&&-1!==l.indexOf("/"))throw new x(S.INVALID_ARGUMENT,`Invalid query. When querying a collection and ordering by documentId(), the value passed to ${r}() must be a plain document ID, but '${l}' contains a slash.`);let n=e.path.child(K.fromString(l));if(!$.isDocumentKey(n))throw new x(S.INVALID_ARGUMENT,`Invalid query. When querying a collection group and ordering by documentId(), the value passed to ${r}() must result in a valid document path, but '${n}' is not because it contains an odd number of segments.`);let i=new $(n);o.push(td(t,i))}else{let e=o$(n,r,l);o.push(e)}}return new tx(o,s)}(e._query,e.firestore._databaseId,i,t,n,r)}}function ll(e,t,n){if("string"==typeof(n=(0,h.Ku)(n))){if(""===n)throw new x(S.INVALID_ARGUMENT,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!t5(t)&&-1!==n.indexOf("/"))throw new x(S.INVALID_ARGUMENT,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${n}' contains a '/' character.`);let r=t.path.child(K.fromString(n));if(!$.isDocumentKey(r))throw new x(S.INVALID_ARGUMENT,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${r}' is not because it has an odd number of segments (${r.length}).`);return td(e,new $(r))}if(n instanceof of)return td(e,n._key);throw new x(S.INVALID_ARGUMENT,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${ol(n)}.`)}function lu(e,t){if(!Array.isArray(e)||0===e.length)throw new x(S.INVALID_ARGUMENT,`Invalid Query. A non-empty array is required for '${t.toString()}' filters.`)}function lh(e,t){let n=function(e,t){for(let n of e)for(let e of n.getFlattenedFilters())if(t.indexOf(e.op)>=0)return e.op;return null}(e.filters,function(e){switch(e){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}}(t.op));if(null!==n)throw n===t.op?new x(S.INVALID_ARGUMENT,`Invalid query. You cannot use more than one '${t.op.toString()}' filter.`):new x(S.INVALID_ARGUMENT,`Invalid query. You cannot use '${t.op.toString()}' filters with '${n.toString()}' filters.`)}class lc{convertValue(e,t="none"){switch(ts(e)){case 0:return null;case 1:return e.booleanValue;case 2:return e8(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,t);case 5:return e.stringValue;case 6:return this.convertBytes(e6(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,t);case 11:return this.convertObject(e.mapValue,t);case 10:return this.convertVectorValue(e.mapValue);default:throw b()}}convertObject(e,t){return this.convertObjectMap(e.fields,t)}convertObjectMap(e,t="none"){let n={};return ej(e,(e,r)=>{n[e]=this.convertValue(r,t)}),n}convertVectorValue(e){var t,n,r;return new oC(null==(r=null==(n=null==(t=e.fields)?void 0:t.value.arrayValue)?void 0:n.values)?void 0:r.map(e=>e8(e.doubleValue)))}convertGeoPoint(e){return new oD(e8(e.latitude),e8(e.longitude))}convertArray(e,t){return(e.values||[]).map(e=>this.convertValue(e,t))}convertServerTimestamp(e,t){switch(t){case"previous":let n=e7(e);return null==n?null:this.convertValue(n,t);case"estimate":return this.convertTimestamp(te(e));default:return null}}convertTimestamp(e){let t=e4(e);return new U(t.seconds,t.nanos)}convertDocumentKey(e,t){let n=K.fromString(e);rx(n)||b();let r=new tn(n.get(1),n.get(3)),i=new $(n.popFirst(5));return r.isEqual(t)||T(`Document ${i} contains a document reference within a different database (${r.projectId}/${r.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`),i}}function ld(e,t,n){return e?n&&(n.merge||n.mergeFields)?e.toFirestore(t,n):e.toFirestore(t):t}class lf extends lc{constructor(e){super(),this.firestore=e}convertBytes(e){return new ob(e)}convertReference(e){let t=this.convertDocumentKey(e,this.firestore._databaseId);return new of(this.firestore,null,t)}}class lm{constructor(e,t){this.hasPendingWrites=e,this.fromCache=t}isEqual(e){return this.hasPendingWrites===e.hasPendingWrites&&this.fromCache===e.fromCache}}class lg extends o1{constructor(e,t,n,r,i,s){super(e,t,n,r,s),this._firestore=e,this._firestoreImpl=e,this.metadata=i}exists(){return super.exists()}data(e={}){if(this._document){if(this._converter){let t=new lp(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null);return this._converter.fromFirestore(t,e)}return this._userDataWriter.convertValue(this._document.data.value,e.serverTimestamps)}}get(e,t={}){if(this._document){let n=this._document.data.field(o5("DocumentSnapshot.get",e));if(null!==n)return this._userDataWriter.convertValue(n,t.serverTimestamps)}}}class lp extends lg{data(e={}){return super.data(e)}}class ly{constructor(e,t,n,r){this._firestore=e,this._userDataWriter=t,this._snapshot=r,this.metadata=new lm(r.hasPendingWrites,r.fromCache),this.query=n}get docs(){let e=[];return this.forEach(t=>e.push(t)),e}get size(){return this._snapshot.docs.size}get empty(){return 0===this.size}forEach(e,t){this._snapshot.docs.forEach(n=>{e.call(t,new lp(this._firestore,this._userDataWriter,n.key,n,new lm(this._snapshot.mutatedKeys.has(n.key),this._snapshot.fromCache),this.query.converter))})}docChanges(e={}){let t=!!e.includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new x(S.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=function(e,t){if(e._snapshot.oldDocs.isEmpty()){let t=0;return e._snapshot.docChanges.map(n=>{let r=new lp(e._firestore,e._userDataWriter,n.doc.key,n.doc,new lm(e._snapshot.mutatedKeys.has(n.doc.key),e._snapshot.fromCache),e.query.converter);return n.doc,{type:"added",doc:r,oldIndex:-1,newIndex:t++}})}{let n=e._snapshot.oldDocs;return e._snapshot.docChanges.filter(e=>t||3!==e.type).map(t=>{let r=new lp(e._firestore,e._userDataWriter,t.doc.key,t.doc,new lm(e._snapshot.mutatedKeys.has(t.doc.key),e._snapshot.fromCache),e.query.converter),i=-1,s=-1;return 0!==t.type&&(i=n.indexOf(t.doc.key),n=n.delete(t.doc.key)),1!==t.type&&(s=(n=n.add(t.doc)).indexOf(t.doc.key)),{type:function(e){switch(e){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return b()}}(t.type),doc:r,oldIndex:i,newIndex:s}})}}(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges}}function lw(e){e=ou(e,of);let t=ou(e.firestore,ow);return(function(e,t,n={}){let r=new D;return e.asyncQueue.enqueueAndForget(async()=>(function(e,t,n,r,i){let s=new a4({next:o=>{s.Za(),t.enqueueAndForget(()=>au(e,a));let l=o.docs.has(n);!l&&o.fromCache?i.reject(new x(S.UNAVAILABLE,"Failed to get document because the client is offline.")):l&&o.fromCache&&r&&"server"===r.source?i.reject(new x(S.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):i.resolve(o)},error:e=>i.reject(e)}),a=new af(t1(n.path),s,{includeMetadataChanges:!0,_a:!0});return al(e,a)})(await on(e),e.asyncQueue,t,n,r)),r.promise})(oI(t),e._key).then(n=>(function(e,t,n){let r=n.docs.get(t._key),i=new lv(e);return new lg(e,i,t._key,r,new lm(n.hasPendingWrites,n.fromCache),t.converter)})(t,e,n))}class lv extends lc{constructor(e){super(),this.firestore=e}convertBytes(e){return new ob(e)}convertReference(e){let t=this.convertDocumentKey(e,this.firestore._databaseId);return new of(this.firestore,null,t)}}function lI(e){e=ou(e,od);let t=ou(e.firestore,ow),n=oI(t),r=new lv(t);return function(e){if("L"===e.limitType&&0===e.explicitOrderBy.length)throw new x(S.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}(e._query),(function(e,t,n={}){let r=new D;return e.asyncQueue.enqueueAndForget(async()=>(function(e,t,n,r,i){let s=new a4({next:n=>{s.Za(),t.enqueueAndForget(()=>au(e,a)),n.fromCache&&"server"===r.source?i.reject(new x(S.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):i.resolve(n)},error:e=>i.reject(e)}),a=new af(n,s,{includeMetadataChanges:!0,_a:!0});return al(e,a)})(await on(e),e.asyncQueue,t,n,r)),r.promise})(n,e._query).then(n=>new ly(t,r,e,n))}function lT(e,t,n){e=ou(e,of);let r=ou(e.firestore,ow),i=ld(e.converter,t,n);return lS(r,[oF(oM(r),"setDoc",e._key,i,null!==e.converter,n).toMutation(e._key,nA.none())])}function l_(e,t,n,...r){e=ou(e,of);let i=ou(e.firestore,ow),s=oM(i);return lS(i,[("string"==typeof(t=(0,h.Ku)(t))||t instanceof oS?oG(s,"updateDoc",e._key,t,n,r):oz(s,"updateDoc",e._key,t)).toMutation(e._key,nA.exists(!0))])}function lE(e){return lS(ou(e.firestore,ow),[new nB(e._key,nA.none())])}function lb(e,t){let n=ou(e.firestore,ow),r=op(e),i=ld(e.converter,t);return lS(n,[oF(oM(e.firestore),"addDoc",r._key,i,null!==e.converter,{}).toMutation(r._key,nA.exists(!1))]).then(()=>r)}function lS(e,t){var n=oI(e);let r=new D;return n.asyncQueue.enqueueAndForget(async()=>aC(await ot(n).then(e=>e.syncEngine),t,r)),r.promise}function lx(e){return function(e,t){let n=ou(e.firestore,ow),r=oI(n),i=function(e,t){let n=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.push(t(e[r],r,e));return n}(t,(e,t)=>new nQ(t,e.aggregateType,e._internalFieldPath));return(function(e,t,n){let r=new D;return e.asyncQueue.enqueueAndForget(async()=>{try{var i;let s=await (i=e,ot(i).then(e=>e.datastore));r.resolve(async function(e,t,n){var r,i;let{request:s,ut:a,parent:o}=function(e,t,n,r){let{_t:i,parent:s}=r_(e,t),a={},o=[],l=0;return n.forEach(e=>{let t="aggregate_"+l++;a[t]=e.alias,"count"===e.aggregateType?o.push({alias:t,count:{}}):"avg"===e.aggregateType?o.push({alias:t,avg:{field:rb(e.fieldPath)}}):"sum"===e.aggregateType&&o.push({alias:t,sum:{field:rb(e.fieldPath)}})}),{request:{structuredAggregationQuery:{aggregations:o,structuredQuery:i.structuredQuery},parent:i.parent},ut:a,parent:s}}(e.serializer,((i=t).he||(i.he=t8(i,i.explicitOrderBy)),i.he),n);e.connection.Fo||delete s.parent;let l=(await e.Lo("RunAggregationQuery",e.serializer.databaseId,o,s,1)).filter(e=>!!e.result);1===l.length||b();let u=null==(r=l[0].result)?void 0:r.aggregateFields;return Object.keys(u).reduce((e,t)=>(e[a[t]]=u[t],e),{})}(s,t,n))}catch(e){r.reject(e)}}),r.promise})(r,e._query,i).then(t=>new oE(e,new lv(n),t))}(e,{count:new o_("count")})}class lD{constructor(e){this.forceOwnership=e,this.kind="persistentSingleTab"}toJSON(){return{kind:this.kind}}_initialize(e){this._onlineComponentProvider=a3.provider,this._offlineComponentProvider={build:t=>new a2(t,null==e?void 0:e.cacheSizeBytes,this.forceOwnership)}}}function lC(e,t){if((e=(0,h.Ku)(e)).firestore!==t)throw new x(S.INVALID_ARGUMENT,"Provided document reference is from a different Firestore instance.");return e}function lN(e){return new oK("increment",e)}new WeakMap,!function(e,t=!0){y=o.MF,(0,o.om)(new l.uA("firestore",(e,{instanceIdentifier:n,options:r})=>{let i=e.getProvider("app").getImmediate(),s=new ow(new k(e.getProvider("auth-internal")),new M(e.getProvider("app-check-internal")),function(e,t){if(!Object.prototype.hasOwnProperty.apply(e.options,["projectId"]))throw new x(S.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.');return new tn(e.options.projectId,t)}(i,n),i);return r=Object.assign({useFetchStreams:t},r),s._setSettings(r),s},"PUBLIC").setMultipleInstances(!0)),(0,o.KO)(g,"4.7.3",void 0),(0,o.KO)(g,"4.7.3","esm2017")}()}}]);