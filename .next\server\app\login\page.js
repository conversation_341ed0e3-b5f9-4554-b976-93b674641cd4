(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4146:(e,r,s)=>{Promise.resolve().then(s.bind(s,69488))},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41098:(e,r,s)=>{Promise.resolve().then(s.bind(s,94934))},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68597:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(65239),i=s(48088),o=s(88170),n=s.n(o),a=s(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94934)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\login\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},69488:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>x});var t=s(60687),i=s(43210),o=s(85814),n=s.n(o),a=s(30474),l=s(63385),d=s(33784),c=s(87979),u=s(51278),p=s(77567);function x(){let{user:e,loading:r}=(0,c.hD)(),[s,o]=(0,i.useState)(""),[x,m]=(0,i.useState)(""),[h,g]=(0,i.useState)(!1),[f,b]=(0,i.useState)(!1),[v,j]=(0,i.useState)(!1),y=async e=>{if(e.preventDefault(),!s||!x)return void p.A.fire({icon:"error",title:"Error",text:"Please fill in all fields",background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"});g(!0);try{let e=await (0,l.x9)(d.j2,s,x);v||(0,u.Dl)(!0),(0,u.nS)(e.user.uid),console.log("\uD83D\uDD12 User session isolated for:",e.user.uid)}catch(r){console.error("Login error:",r);let e="An error occurred during login";switch(r.code){case"auth/user-not-found":e="No account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=r.message||"Login failed"}p.A.fire({icon:"error",title:"Login Failed",text:e,background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"}),m("")}finally{g(!1)}};return r?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,t.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,t.jsx)(a.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:v?"Session Expired":"Welcome Back"}),(0,t.jsx)("p",{className:"text-white/80",children:v?"Log in to restore your progress":"Sign in to continue earning"}),v&&(0,t.jsx)("div",{className:"mt-4 bg-orange-500/20 border border-orange-400/30 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,t.jsx)("i",{className:"fas fa-exclamation-triangle text-orange-400 mr-2"}),(0,t.jsx)("span",{className:"text-orange-300 text-sm",children:"Your work progress will be restored after login"})]})})]}),(0,t.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address"}),(0,t.jsx)("input",{type:"email",id:"email",value:s,onChange:e=>o(e.target.value),className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:f?"text":"password",id:"password",value:x,onChange:e=>m(e.target.value),className:"form-input pr-12",placeholder:"Enter your password",required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>b(!f),className:"password-toggle-btn","aria-label":f?"Hide password":"Show password",children:(0,t.jsx)("i",{className:`fas ${f?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:h,className:"w-full btn-primary flex items-center justify-center",children:h?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Logging in..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Login"]})})]}),(0,t.jsxs)("div",{className:"mt-6 text-center space-y-3",children:[(0,t.jsx)(n(),{href:"/forgot-password",className:"text-white/80 hover:text-white transition-colors",children:"Forgot your password?"}),(0,t.jsxs)("div",{className:"text-white/60",children:["Don't have an account?"," ",(0,t.jsx)(n(),{href:"/register",className:"text-white font-semibold hover:underline",children:"Sign up here"})]})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)(n(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94934:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\login\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6204,2756,7567,8441,7979],()=>s(68597));module.exports=t})();