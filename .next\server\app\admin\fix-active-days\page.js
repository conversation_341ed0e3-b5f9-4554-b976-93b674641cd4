(()=>{var e={};e.id=1561,e.ids=[1561],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},18116:(e,s,r)=>{Promise.resolve().then(r.bind(r,43927))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31620:(e,s,r)=>{Promise.resolve().then(r.bind(r,95293))},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},43927:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\fix-active-days\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\fix-active-days\\page.tsx","default")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},91999:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>l});var t=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);r.d(s,c);let l={children:["",{children:["admin",{children:["fix-active-days",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43927)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\fix-active-days\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\fix-active-days\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/fix-active-days/page",pathname:"/admin/fix-active-days",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94735:e=>{"use strict";e.exports=require("events")},95293:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(60687),i=r(43210),n=r(87979),o=r(3582),a=r(77567);function c(){let{user:e,loading:s}=(0,n.Nu)(),[c,l]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!1),[x,p]=(0,i.useState)(null),m=e?.email==="<EMAIL>",h=async()=>{if(!m)return void a.A.fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await a.A.fire({icon:"warning",title:"Fix All Users Active Days",text:"This will recalculate and update active days for all users. This may take a while. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Fix All",cancelButtonText:"Cancel"})).isConfirmed)try{l(!0);let e=await (0,o.gj)();p(e),a.A.fire({icon:"success",title:"Active Days Fixed!",html:`
          <div class="text-left">
            <p><strong>Fixed:</strong> ${e.fixedCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
          </div>
        `,timer:5e3})}catch(e){console.error("Error fixing active days:",e),a.A.fire({icon:"error",title:"Error",text:"Failed to fix active days. Check console for details."})}finally{l(!1)}},f=async()=>{if(!m)return void a.A.fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await a.A.fire({icon:"warning",title:"Reset All Daily Video Counts",text:"This will reset today's video count to 0 for all users. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Reset All",cancelButtonText:"Cancel"})).isConfirmed)try{u(!0);let{getDocs:e,collection:s}=await Promise.resolve().then(r.bind(r,75535)),{db:t}=await Promise.resolve().then(r.bind(r,33784)),{COLLECTIONS:i}=await Promise.resolve().then(r.bind(r,3582)),n=await e(s(t,i.users)),c=0,l=0;for(let e of n.docs)try{await (0,o.HY)(e.id),c++}catch(s){console.error(`Error resetting daily count for user ${e.id}:`,s),l++}a.A.fire({icon:"success",title:"Daily Counts Reset!",html:`
          <div class="text-left">
            <p><strong>Reset:</strong> ${c} users</p>
            <p><strong>Errors:</strong> ${l} users</p>
          </div>
        `,timer:5e3})}catch(e){console.error("Error resetting daily counts:",e),a.A.fire({icon:"error",title:"Error",text:"Failed to reset daily counts. Check console for details."})}finally{u(!1)}};return s?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner mb-4"}),(0,t.jsx)("p",{className:"text-white",children:"Loading..."})]})}):m?(0,t.jsx)("div",{className:"min-h-screen p-4",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold text-white mb-6",children:[(0,t.jsx)("i",{className:"fas fa-tools mr-2"}),"Fix Active Days & Daily Counts"]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,t.jsx)("i",{className:"fas fa-calendar-check mr-2"}),"Fix Active Days"]}),(0,t.jsx)("p",{className:"text-white/80 mb-4",children:"Recalculates and updates active days for all users based on their plan activation date and leave history."}),(0,t.jsx)("button",{onClick:h,disabled:c,className:`btn-primary ${c?"opacity-50 cursor-not-allowed":""}`,children:c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Fixing Active Days..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-wrench mr-2"}),"Fix All Users Active Days"]})})]}),(0,t.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,t.jsx)("i",{className:"fas fa-redo mr-2"}),"Reset Daily Video Counts"]}),(0,t.jsx)("p",{className:"text-white/80 mb-4",children:"Resets today's video count to 0 for all users. Use this if daily counts are showing incorrect values."}),(0,t.jsx)("button",{onClick:f,disabled:d,className:`btn-secondary ${d?"opacity-50 cursor-not-allowed":""}`,children:d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Resetting Daily Counts..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Reset All Daily Counts"]})})]}),x&&(0,t.jsxs)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-lg p-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-green-300 mb-2",children:[(0,t.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Last Operation Results"]}),(0,t.jsxs)("div",{className:"text-white",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Fixed:"})," ",x.fixedCount," users"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Errors:"})," ",x.errorCount," users"]})]})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("a",{href:"/admin",className:"btn-secondary",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin Dashboard"]})})]})]})})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-white mb-4",children:"Only admin can access this page."}),(0,t.jsx)("a",{href:"/admin",className:"btn-primary",children:"Back to Admin"})]})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[6204,2756,7567,8441,3582,7979],()=>r(91999));module.exports=t})();