'use client'

import { useState } from 'react'
import { createUserWithEmailAndPassword } from 'firebase/auth'
import { doc, setDoc, getDoc } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'

export default function TestSimpleRegistrationPage() {
  const [result, setResult] = useState('')
  const [isRunning, setIsRunning] = useState(false)

  const addToResult = (message: string) => {
    setResult(prev => prev + message + '\n')
    console.log(message)
  }

  const testSimpleRegistration = async () => {
    setIsRunning(true)
    setResult('')
    
    try {
      addToResult('🧪 Testing Simple Registration...')
      
      const testEmail = `simple-test-${Date.now()}@example.com`
      const testPassword = 'test123456'
      
      // Step 1: Create user
      addToResult('Step 1: Creating Firebase Auth user...')
      const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword)
      const user = userCredential.user
      addToResult(`✅ User created: ${user.uid}`)
      
      // Step 2: Create simple document
      addToResult('Step 2: Creating simple Firestore document...')
      const simpleData = {
        name: 'Simple Test User',
        email: testEmail,
        plan: 'Trial',
        wallet: 0,
        created: new Date().toISOString()
      }
      
      const userDoc = doc(db, 'users', user.uid)
      await setDoc(userDoc, simpleData)
      addToResult('✅ Document created successfully')
      
      // Step 3: Verify document
      addToResult('Step 3: Verifying document...')
      const docSnap = await getDoc(userDoc)
      if (docSnap.exists()) {
        addToResult('✅ Document verified successfully')
        addToResult(`Data: ${JSON.stringify(docSnap.data(), null, 2)}`)
      } else {
        addToResult('❌ Document not found')
      }
      
      // Step 4: Cleanup
      addToResult('Step 4: Cleaning up...')
      await user.delete()
      addToResult('✅ Test user deleted')
      
      addToResult('\n🎉 Simple registration test completed successfully!')
      
    } catch (error: any) {
      addToResult(`❌ Error: ${error.message}`)
      addToResult(`❌ Code: ${error.code}`)
      addToResult(`❌ Full error: ${JSON.stringify(error, null, 2)}`)
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        <div className="glass-card p-6 mb-6">
          <h1 className="text-2xl font-bold text-white mb-4">
            🧪 Simple Registration Test
          </h1>
          <p className="text-white/80 mb-6">
            This test uses simple field names to check if the issue is with complex field mapping.
          </p>
          
          <button
            onClick={testSimpleRegistration}
            disabled={isRunning}
            className="btn-primary mb-6"
          >
            {isRunning ? (
              <>
                <div className="spinner mr-2 w-5 h-5"></div>
                Testing...
              </>
            ) : (
              <>
                <i className="fas fa-test-tube mr-2"></i>
                Test Simple Registration
              </>
            )}
          </button>
          
          {result && (
            <div className="bg-black/50 p-4 rounded-lg">
              <h3 className="text-white font-semibold mb-2">Test Results:</h3>
              <pre className="text-green-400 text-sm whitespace-pre-wrap font-mono overflow-x-auto">
                {result}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
