(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{1469:(e,s,r)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var r in s)Object.defineProperty(e,r,{enumerable:!0,get:s[r]})}(s,{default:function(){return n},getImageProps:function(){return i}});let a=r(8229),t=r(8883),l=r(3063),o=a._(r(1193));function i(e){let{props:s}=(0,t.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(s))void 0===r&&delete s[e];return{props:s}}let n=l.Image},6616:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var a=r(5155),t=r(2115),l=r(6874),o=r.n(l),i=r(6766),n=r(3004),c=r(5317),d=r(6104),m=r(6681),u=r(3592),f=r(4752),h=r.n(f);function g(){let{user:e,loading:s}=(0,m.hD)(),[r,l]=(0,t.useState)({name:"",email:"",mobile:"",password:"",confirmPassword:"",referralCode:""}),[f,g]=(0,t.useState)(!1),[p,b]=(0,t.useState)(!1),[w,x]=(0,t.useState)(!1);(0,t.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("ref");e&&l(s=>({...s,referralCode:e}))},[]);let N=e=>{let{name:s,value:r}=e.target;l(e=>({...e,[s]:r}))},j=()=>{let{name:e,email:s,mobile:a,password:t,confirmPassword:l}=r;if(!e||!s||!a||!t||!l)throw Error("Please fill in all required fields");if(e.length<2)throw Error("Name must be at least 2 characters long");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))throw Error("Please enter a valid email address");if(!/^[6-9]\d{9}$/.test(a))throw Error("Please enter a valid 10-digit mobile number");if(t.length<6)throw Error("Password must be at least 6 characters long");if(t!==l)throw Error("Passwords do not match")},y=async e=>{e.preventDefault();try{j(),g(!0),console.log("Creating user with email and password...");let e=(await (0,n.eJ)(d.j2,r.email,r.password)).user;console.log("Firebase Auth user created successfully:",e.uid),console.log("Generating referral code...");let s=Date.now().toString().slice(-4),a=Math.random().toString(36).substring(2,4).toUpperCase(),t="MY".concat(s).concat(a);console.log("Generated referral code:",t);let l={[u.FIELD_NAMES.name]:r.name.trim(),[u.FIELD_NAMES.email]:r.email.toLowerCase(),[u.FIELD_NAMES.mobile]:r.mobile,[u.FIELD_NAMES.referralCode]:t,[u.FIELD_NAMES.referredBy]:r.referralCode||"",[u.FIELD_NAMES.referralBonusCredited]:!1,[u.FIELD_NAMES.plan]:"Trial",[u.FIELD_NAMES.planExpiry]:null,[u.FIELD_NAMES.activeDays]:1,[u.FIELD_NAMES.joinedDate]:c.Dc.now(),[u.FIELD_NAMES.wallet]:0,[u.FIELD_NAMES.totalVideos]:0,[u.FIELD_NAMES.todayVideos]:0,[u.FIELD_NAMES.lastVideoDate]:null,[u.FIELD_NAMES.videoDuration]:30,status:"active"};console.log("Creating user document with data:",l),console.log("User UID:",e.uid),console.log("Collection:",u.COLLECTIONS.users),console.log("Document path:","".concat(u.COLLECTIONS.users,"/").concat(e.uid)),console.log("Creating user document in Firestore...");let o=(0,c.H9)(d.db,u.COLLECTIONS.users,e.uid);console.log("Document reference created:",o.path),console.log("About to create document with data:",JSON.stringify(l,null,2));try{console.log("Attempting to create document..."),console.log("User UID:",e.uid),console.log("Document path:",o.path),console.log("Auth user email:",e.email),console.log("Auth user verified:",e.emailVerified),await (0,c.BN)(o,l),console.log("✅ User document created successfully");let s=await (0,c.x7)(o);if(s.exists())console.log("✅ Document verification successful:",s.data()),console.log("✅ Registration completed successfully - both Auth and Firestore created");else throw console.error("❌ Document was not created properly"),Error("User document was not created properly")}catch(e){throw console.error("❌ Firestore setDoc failed:",e),console.error("❌ Firestore error code:",e.code),console.error("❌ Firestore error message:",e.message),console.error("❌ Full error object:",JSON.stringify(e,null,2)),console.error("❌ CRITICAL: Firebase Auth succeeded but Firestore document creation failed"),console.error("❌ User account exists but profile is incomplete"),Error("Failed to create user profile: ".concat(e.message,". Your account was created but profile setup failed. Please contact support."))}console.log("User registered successfully. Referral bonus will be processed when upgraded to paid plan."),h().fire({icon:"success",title:"Registration Successful!",text:"Your account and profile have been created successfully. Welcome to MyTube!",timer:2e3,showConfirmButton:!1}).then(()=>{console.log("✅ Complete registration successful - redirecting to dashboard..."),window.location.href="/dashboard"})}catch(s){console.error("Registration error:",s),console.error("Error code:",s.code),console.error("Error message:",s.message),console.error("Full error object:",JSON.stringify(s,null,2));let e="An error occurred during registration";if(s.message.includes("fill in all"))e=s.message;else if(s.message.includes("Name must be"))e=s.message;else if(s.message.includes("valid email"))e=s.message;else if(s.message.includes("valid 10-digit"))e=s.message;else if(s.message.includes("Password must be"))e=s.message;else if(s.message.includes("Passwords do not match"))e=s.message;else if(s.message.includes("email address is already registered"))e=s.message;else if(s.message.includes("mobile number is already registered"))e=s.message;else switch(s.code){case"auth/email-already-in-use":e="An account with this email already exists";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/weak-password":e="Password is too weak";break;default:e=s.message||"Registration failed"}h().fire({icon:"error",title:"Registration Failed",text:e})}finally{g(!1)}};return s?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 py-8",children:(0,a.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,a.jsx)(i.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Create Account"}),(0,a.jsx)("p",{className:"text-white/80",children:"Join MyTube and start earning today"})]}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-white font-medium mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:r.name,onChange:N,className:"form-input",placeholder:"Enter your full name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",value:r.email,onChange:N,className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"mobile",className:"block text-white font-medium mb-2",children:"Mobile Number *"}),(0,a.jsx)("input",{type:"tel",id:"mobile",name:"mobile",value:r.mobile,onChange:N,className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:p?"text":"password",id:"password",name:"password",value:r.password,onChange:N,className:"form-input pr-12",placeholder:"Enter password (min 6 characters)",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>b(!p),className:"password-toggle-btn","aria-label":p?"Hide password":"Show password",children:(0,a.jsx)("i",{className:"fas ".concat(p?"fa-eye-slash":"fa-eye")})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm Password *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:w?"text":"password",id:"confirmPassword",name:"confirmPassword",value:r.confirmPassword,onChange:N,className:"form-input pr-12",placeholder:"Confirm your password",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>x(!w),className:"password-toggle-btn","aria-label":w?"Hide confirm password":"Show confirm password",children:(0,a.jsx)("i",{className:"fas ".concat(w?"fa-eye-slash":"fa-eye")})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"referralCode",className:"block text-white font-medium mb-2",children:"Referral Code (Optional)"}),(0,a.jsx)("input",{type:"text",id:"referralCode",name:"referralCode",value:r.referralCode,onChange:N,className:"form-input",placeholder:"Enter referral code if you have one"})]}),(0,a.jsx)("button",{type:"submit",disabled:f,className:"w-full btn-primary flex items-center justify-center mt-6",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Account..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create Account"]})})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("div",{className:"text-white/60",children:["Already have an account?"," ",(0,a.jsx)(o(),{href:"/login",className:"text-white font-semibold hover:underline",children:"Sign in here"})]})}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)(o(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},6626:(e,s,r)=>{Promise.resolve().then(r.bind(r,6616))},6766:(e,s,r)=>{"use strict";r.d(s,{default:()=>t.a});var a=r(1469),t=r.n(a)}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,6874,3063,3592,6681,8441,1684,7358],()=>s(6626)),_N_E=e.O()}]);