'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { sendPasswordResetEmail } from 'firebase/auth'
import { auth } from '@/lib/firebase'
import { useAuthState } from '@/hooks/useAuth'
import Swal from 'sweetalert2'

export default function ForgotPasswordPage() {
  const { user, loading } = useAuthState()
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [emailSent, setEmailSent] = useState(false)

  useEffect(() => {
    if (user && !loading) {
      window.location.href = '/dashboard'
    }
  }, [user, loading])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email.trim()) {
      Swal.fire({
        icon: 'error',
        title: 'Email Required',
        text: 'Please enter your email address',
      })
      return
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      Swal.fire({
        icon: 'error',
        title: 'Invalid Email',
        text: 'Please enter a valid email address',
      })
      return
    }

    setIsLoading(true)

    try {
      await sendPasswordResetEmail(auth, email.trim().toLowerCase(), {
        url: `${window.location.origin}/login`,
        handleCodeInApp: false
      })

      setEmailSent(true)
      
      Swal.fire({
        icon: 'success',
        title: 'Reset Email Sent!',
        html: `
          <p>We've sent a password reset link to:</p>
          <p class="font-semibold text-blue-600">${email}</p>
          <p class="mt-4 text-sm text-gray-600">
            Please check your email and click the link to reset your password.
            If you don't see the email, check your spam folder.
          </p>
        `,
        confirmButtonText: 'Got it!',
        confirmButtonColor: '#3b82f6'
      })

    } catch (error: any) {
      console.error('Password reset error:', error)
      
      let message = 'An error occurred while sending the reset email'
      
      switch (error.code) {
        case 'auth/user-not-found':
          message = 'No account found with this email address. Please check your email or create a new account.'
          break
        case 'auth/invalid-email':
          message = 'Invalid email address format'
          break
        case 'auth/too-many-requests':
          message = 'Too many reset attempts. Please wait a few minutes before trying again.'
          break
        case 'auth/network-request-failed':
          message = 'Network error. Please check your internet connection and try again.'
          break
        default:
          message = error.message || 'Failed to send reset email'
      }

      Swal.fire({
        icon: 'error',
        title: 'Reset Failed',
        text: message,
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
        <p className="text-white mt-4">Loading...</p>
      </div>
    )
  }

  return (
    <main className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <Image
              src="/logo.png"
              alt="MyTube"
              width={120}
              height={120}
              className="mx-auto mb-4"
            />
          </Link>
          <h1 className="text-3xl font-bold text-white mb-2">Reset Password</h1>
          <p className="text-white/80">
            {emailSent 
              ? 'Check your email for reset instructions'
              : 'Enter your email to receive a password reset link'
            }
          </p>
        </div>

        {!emailSent ? (
          <form onSubmit={handleSubmit} className="glass-card p-8 space-y-6">
            {/* Email Input */}
            <div>
              <label htmlFor="email" className="block text-white font-medium mb-2">
                Email Address
              </label>
              <div className="relative">
                <i className="fas fa-envelope absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"></i>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40"
                  placeholder="Enter your email address"
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full btn-primary flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <div className="spinner mr-2 w-5 h-5"></div>
                  Sending Reset Email...
                </>
              ) : (
                <>
                  <i className="fas fa-paper-plane mr-2"></i>
                  Send Reset Email
                </>
              )}
            </button>
          </form>
        ) : (
          <div className="glass-card p-8 text-center">
            <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-check text-green-400 text-2xl"></i>
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Email Sent!</h3>
            <p className="text-white/80 mb-6">
              We've sent a password reset link to <span className="font-semibold text-blue-400">{email}</span>
            </p>
            <div className="space-y-4">
              <button
                onClick={() => {
                  setEmailSent(false)
                  setEmail('')
                }}
                className="w-full btn-secondary"
              >
                <i className="fas fa-redo mr-2"></i>
                Send to Different Email
              </button>
            </div>
          </div>
        )}

        {/* Links */}
        <div className="mt-6 text-center space-y-3">
          <Link
            href="/login"
            className="text-white/80 hover:text-white transition-colors flex items-center justify-center"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Login
          </Link>
          
          <div className="text-white/60">
            Don't have an account?{' '}
            <Link
              href="/register"
              className="text-white font-semibold hover:underline"
            >
              Sign up here
            </Link>
          </div>
        </div>

        {/* Help Text */}
        <div className="mt-8 text-center">
          <div className="glass-card p-4">
            <h4 className="text-white font-semibold mb-2">
              <i className="fas fa-question-circle mr-2"></i>
              Need Help?
            </h4>
            <p className="text-white/60 text-sm mb-3">
              If you don't receive the email within a few minutes:
            </p>
            <ul className="text-white/60 text-sm space-y-1 text-left">
              <li>• Check your spam/junk folder</li>
              <li>• Make sure you entered the correct email</li>
              <li>• Wait a few minutes and try again</li>
              <li>• Contact support if the problem persists</li>
            </ul>
          </div>
        </div>
      </div>
    </main>
  )
}
