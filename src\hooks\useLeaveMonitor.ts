import { useState, useEffect, useCallback } from 'react'
import { isWorkBlocked } from '@/lib/leaveService'

interface LeaveStatus {
  blocked: boolean
  reason?: string
  lastChecked: Date
}

interface UseLeaveMonitorOptions {
  userId: string | null
  checkInterval?: number // in milliseconds, default 30 seconds
  enabled?: boolean
}

export function useLeaveMonitor({ 
  userId, 
  checkInterval = 30000, // 30 seconds
  enabled = true 
}: UseLeaveMonitorOptions) {
  const [leaveStatus, setLeaveStatus] = useState<LeaveStatus>({
    blocked: false,
    lastChecked: new Date()
  })
  const [isChecking, setIsChecking] = useState(false)

  const checkLeaveStatus = useCallback(async () => {
    if (!userId || !enabled) return

    try {
      setIsChecking(true)
      const workStatus = await isWorkBlocked(userId)
      
      setLeaveStatus({
        blocked: workStatus.blocked,
        reason: workStatus.reason,
        lastChecked: new Date()
      })

      return workStatus
    } catch (error) {
      console.error('Error checking leave status:', error)
      // On error, don't change the current status
      setLeaveStatus(prev => ({
        ...prev,
        lastChecked: new Date()
      }))
      return { blocked: false }
    } finally {
      setIsChecking(false)
    }
  }, [userId, enabled])

  // Initial check
  useEffect(() => {
    if (userId && enabled) {
      checkLeaveStatus()
    }
  }, [userId, enabled, checkLeaveStatus])

  // Periodic checks
  useEffect(() => {
    if (!userId || !enabled || checkInterval <= 0) return

    const interval = setInterval(() => {
      checkLeaveStatus()
    }, checkInterval)

    return () => clearInterval(interval)
  }, [userId, enabled, checkInterval, checkLeaveStatus])

  return {
    leaveStatus,
    isChecking,
    checkLeaveStatus,
    isBlocked: leaveStatus.blocked
  }
}
